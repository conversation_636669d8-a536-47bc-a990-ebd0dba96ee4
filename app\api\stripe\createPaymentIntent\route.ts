import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { auth } from "@/app/auth";
import { JC_Utils } from "@/app/Utils";
import { <PERSON>ieKeyEnum } from "@/app/enums/CookieKey";
import { OrderBusiness } from "../../order/business";
import { UserBusiness } from "../../user/business";
import { ProductBusiness } from "../../product/business";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
    try {

        const { orderId, enteredEmail, enteredFullName } = await request.json();

        let paymentIntent:Stripe.Response<Stripe.PaymentIntent>;

        const session = await auth();

        // Get order
        let bag = await OrderBusiness.Get(orderId);
        let bagAmount:number = Number(bag.getTotalPrice(await ProductBusiness.GetList()));
        let stripeAmount = bagAmount*100;

        let userStripeCustomerId;
        // IF logged in
        if (session?.user != null) {
            userStripeCustomerId = (await UserBusiness.Get(session.user.Id)).StripeCustomerId;
            // IF don't have a stripe customer id yet, create stripe customer for this User
            if (JC_Utils.stringNullOrEmpty(userStripeCustomerId)) {
                userStripeCustomerId = (await stripe.customers.create({
                    email: session.user.Email,
                    name: `${session.user.FirstName} ${session.user.LastName}`,
                })).id;
                await UserBusiness.UpdateStripeCustomerId(session.user.Id, userStripeCustomerId);
            }
        }

        const paymentIntentId = cookies().get(CookieKeyEnum.JC_PaymentIntentId)?.value;

        // IF paymentIntentId in cookies
        if (paymentIntentId) {
            // Get paymentIntent from Stripe using this id
            paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
            // IF amount no longer matches, create new payment intent and set to cookies
            if (paymentIntent.amount != stripeAmount) {
                paymentIntent = await createPaymentIntent(enteredFullName, session?.user.Email ?? enteredEmail, stripeAmount, orderId, userStripeCustomerId);
                cookies().set(CookieKeyEnum.JC_PaymentIntentId, paymentIntent.id);
            }
        // ELSE create new payment intent and set to cookies
        } else {
            paymentIntent = await createPaymentIntent(enteredFullName, session?.user.Email ?? enteredEmail, stripeAmount, orderId, userStripeCustomerId);
            cookies().set(CookieKeyEnum.JC_PaymentIntentId, paymentIntent.id, { maxAge: 60*5 }); // Cookie lasts for 5 mins
        }

        return NextResponse.json({ amount: bagAmount, clientSecret: paymentIntent.client_secret });

    } catch (error) {
        console.error(error);
        return NextResponse.json({ error }, { status: 500 });
    }
  }


// - PRIVATE - //

// Create payment intent
async function createPaymentIntent(fullName:string, email:string, amount:number, orderId:string, userStripeCustomerId?:string) {
    return await stripe.paymentIntents.create({
        amount: amount,
        currency: "aud",
        customer: userStripeCustomerId,
        metadata: { fullName: fullName, email: email, orderId: orderId },
        automatic_payment_methods: { enabled: true }
    });
}

