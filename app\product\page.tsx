"use client"

import styles from "./page.module.scss";
import React, { Suspense, useEffect, useState } from "react";
import Image from "next/image";
import { useRouter, useSearchParams } from 'next/navigation'
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_Dropdown from "../components/JC_Dropdown/JC_Dropdown";
import { JC_Get } from "../services/JC_Get";
import { JC_GetList } from "../services/JC_GetList";
import { AddToBag } from "../services/Bag";
import { useSession } from "next-auth/react";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { DropdownTypeEnum } from "../enums/DropdownType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { SettingsTypeEnum } from "../enums/VariationSettings";
import { JC_Utils } from "../Utils";


export default function Page_Product_Suspense() {
    return <Suspense><Page_Product /></Suspense>
}

function Page_Product() {

    const session = useSession();
    const router = useRouter();

    // - STATE - //

    // Loading
    const [addToBagLoading, setAddToBagLoading] = useState<boolean>(false);
    // Product
    const [product, setProduct] = useState<ProductModel>();
    const [imageNames, setImageNames] = useState<string[]>([]);
    const [selectedImageName, setSelectedImageName] = useState<string>("");
    const [hoveringImageName, setHoveringImageName] = useState<string>("");
    const [quantity, setQuantity] = useState<number>(1);
    const [discountValue, setDiscountValue] = useState<number>(1);


    // - VARIABLES - //

    let hasDiscount = session.data?.user.IsDiscountUser;

    // For products with no variations, we'll handle them as if they're always selected
    let hasVariations = product != null && product.getVariationCategories().some(c => c.Name != "None");

    // Track selected variations in state
    const [selectedVariation1, setSelectedVariation1] = useState<ProductVariationModel | null>(null);
    const [selectedVariation2, setSelectedVariation2] = useState<ProductVariationModel | null>(null);

    // Initialize with first variation of each category if needed
    useEffect(() => {
        if (product && !hasVariations && product.Ex_Variations.length > 0) {
            setSelectedVariation1(product.Ex_Variations[0]);
        }
    }, [product, hasVariations]);

    let allSelectionsMade = !hasVariations ||
        product?.getVariationCategories().every(c =>
            [selectedVariation1, selectedVariation2].some(v => v && v.VariationCategoryCode === c.Code)
        );
    let hasServingsData = product?.Ex_Variations != null && allSelectionsMade;

    // Get the selected variation IDs
    const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

    // Get item settings for servings
    let servingsSettings = !hasServingsData ? null : product!.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, selectedIds);
    let servSize = !hasServingsData ? 100 : servingsSettings!.ServingSizeGrams;
    let servPerPack = !hasServingsData ? 100 : servingsSettings!.ServingsPerPack;

    // Get variations data and check if nutritional values are enabled
    let varData = allSelectionsMade ? product?.getVarDataForSelections() : null;
    let nutValsEnabled = varData?.Ex_NutValsEnabled ?? false;
    let nutVals = (allSelectionsMade && nutValsEnabled) ? varData?.Ex_NutVals : null;

    let idParam = useSearchParams().get("id") as string;
    useEffect(() => {
        if (!JC_Utils.stringNullOrEmpty(idParam)) {
            JC_Get<ProductModel>("product", { id: idParam }, ProductModel).then(foundProduct => {
                if (foundProduct != null) {
                    setProduct(foundProduct);

                    // Initialize with first variation of each category if needed
                    if (!hasVariations && foundProduct.Ex_Variations.length > 0) {
                        setSelectedVariation1(foundProduct.Ex_Variations[0]);
                    }

                    // Set list of images
                    let allImages = [];
                    if (!JC_Utils.stringNullOrEmpty(foundProduct.ImageFileName)) {
                        allImages.push(`products/${foundProduct.ImageFileName}`);
                    }
                    setImageNames(allImages);
                    // Set default selected image as Product's main image
                    setSelectedImageName(allImages[0]);
                }
            });
            if (hasDiscount) {
                JC_Get("globalSettings", { code: "UserDiscount" }).then((result:any) => {
                    setDiscountValue(result.Value);
                });
            }
        }
    }, [idParam]);


    // - HANDLES - //

    // Select a Variation
    function selectVariation(varId:string) {
        if (!product) return;

        // Create a deep copy of the product to ensure React detects the state change
        const updatedProduct = new ProductModel(product);

        // Find the selected variation in the new product object
        const selected = updatedProduct.Ex_Variations.find(v => v.Id == varId)!;

        // Determine which variation slot to update based on category
        const categoryCode = selected.VariationCategoryCode;

        // If we have a first variation and it's from the same category, replace it
        if (selectedVariation1 && selectedVariation1.VariationCategoryCode === categoryCode) {
            setSelectedVariation1(selected);
        }
        // If we have a second variation and it's from the same category, replace it
        else if (selectedVariation2 && selectedVariation2.VariationCategoryCode === categoryCode) {
            setSelectedVariation2(selected);
        }
        // If we don't have a first variation yet, set it
        else if (!selectedVariation1) {
            setSelectedVariation1(selected);
        }
        // If we don't have a second variation yet, set it
        else if (!selectedVariation2) {
            setSelectedVariation2(selected);
        }
        // If both slots are filled with different categories, replace the first one
        else {
            setSelectedVariation1(selected);
        }

        // Calculate nutritional values for the selected variation
        const var1 = selected.VariationCategoryCode === selectedVariation1?.VariationCategoryCode ? selected : selectedVariation1 || undefined;
        const var2 = selected.VariationCategoryCode === selectedVariation2?.VariationCategoryCode ? selected : selectedVariation2 || undefined;
        updatedProduct.calcNutValsForSelected(var1, var2);

        // Update the state with the new product object
        setProduct(updatedProduct);
    }

    // Add to Bag
    async function addToBag() {
        setAddToBagLoading(true);
        try {
            // Get the IDs of the selected variations
            const selectedVariationIds = [selectedVariation1, selectedVariation2]
                .filter(Boolean)
                .map(v => v!.Id);

            await AddToBag(session.data, product!.Id, selectedVariationIds, quantity);
            JC_Utils.showToastSuccess("Item added to your bag!");

            // IF has variations, clear selections
            if (hasVariations) {
                // Reset the selected variations
                setSelectedVariation1(null);
                setSelectedVariation2(null);
                setQuantity(1);
                setSelectedImageName(imageNames[1] ?? imageNames[0]);
            // ELSE go back to Products page
            } else {
                router.push(`productGroups`);
            }
            setAddToBagLoading(false);
        } catch (error) {
            console.log(error);
            setAddToBagLoading(false);
            JC_Utils.showToastError("Could not add item to cart.");
        }
    }


    // - MAIN - //

    return product == null
        ? (<JC_Spinner isPageBody />)
        : (
            <div className={styles.mainContainer}>

                <JC_Title overrideClass={styles.titleOverride} title={product.Name} isSecondary />

                {/* Back Button */}
                <div className={styles.backButton}>
                    <JC_Button
                        text="Products"
                        iconName="Arrow"
                        linkToPage="productGroups"
                    />
                </div>

                <div className={styles.bodyContainer}>

                    {/* Details */}
                    <div className={styles.imagesNutValsContainer}>

                        {/* Small Images */}
                        {hasVariations &&
                        <div className={styles.smallImagesList}>
                            {imageNames.map(imageName =>
                                <Image
                                    key={imageName}
                                    id={`var-image-${imageName}`}
                                    className={`
                                        ${styles.smallImage}
                                        ${imageName == hoveringImageName ? styles.hovering : ''}
                                        ${imageName == selectedImageName ? styles.selected : ''}
                                    `}
                                    src={`/products/${imageName}.webp`}
                                    width={200}
                                    height={200}
                                    alt={imageName}
                                    onClick={() => setSelectedImageName(imageName)}
                                />
                            )}
                        </div>}

                        {/* Main Image + Nut Vals Container */}
                        <div className={styles.mainImageNutValsContainer}>

                            {/* Main Image */}
                            <div className={styles.mainImageContainer}>
                                <Image
                                    key={`${hoveringImageName} ${selectedImageName}`}
                                    className={styles.mainImage}
                                    src={`/products/${!JC_Utils.stringNullOrEmpty(hoveringImageName) ? hoveringImageName : selectedImageName}.webp`}
                                    width={400}
                                    height={400}
                                    alt={selectedImageName}
                                />

                                {/* Div spanning whole page that darkens when hover image */}
                                <div className={styles.darkenDiv} />
                            </div>

                            <div className={styles.servings}>
                                <b>S. Size:</b> {!allSelectionsMade ? "-" : servSize}<br/>
                                <b>S. per Package:</b> {!allSelectionsMade ? "-" : servPerPack}
                            </div>

                            {/* Nutritional Values */}
                            <div className={`${styles.nutValsContainer} ${allSelectionsMade && !nutValsEnabled ? styles.darkened : ''}`}>
                                {/* Not Available Yet overlay */}
                                {allSelectionsMade && !nutValsEnabled && (
                                    <div className={styles.notAvailableOverlay}>
                                        <div className={styles.notAvailableText}>Not Available Yet</div>
                                    </div>
                                )}
                                <div className={styles.headingsContainer}>
                                    <div/>
                                    <div>Average Quantity<br/>per Serving</div>
                                    <div>Average Quantity<br/>per 100g</div>
                                </div>
                                <div className={styles.nutVals}>
                                    <div>Energy</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Kilojoules*(servSize/100), 0)}</div>}   <div>kJ</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Kilojoules, 0)}</div>}                  <div>kJ</div>
                                    <div>Protein</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Protein*(servSize/100), 1)}</div>}       <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Protein, 1)}</div>}                      <div>g</div>
                                    <div>Fat, total</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.FatTotal*(servSize/100), 1)}</div>}      <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.FatTotal, 1)}</div>}                     <div>g</div>
                                    <div>- saturated</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.FatSaturated*(servSize/100), 1)}</div>}  <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.FatSaturated, 1)}</div>}                 <div>g</div>
                                    <div>Carbohydrate</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Carbohydrate*(servSize/100), 1)}</div>}  <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Carbohydrate, 1)}</div>}                 <div>g</div>
                                    <div>- sugars</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Sugars*(servSize/100), 1)}</div>}        <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Sugars, 1)}</div>}                       <div>g</div>
                                    <div>Fibre</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Fiber*(servSize/100), 1)}</div>}         <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Fiber, 1)}</div>}                        <div>g</div>
                                    <div>Net Carbs</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals._NetCarbs*(servSize/100), 1)}</div>}     <div>g</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals._NetCarbs, 1)}</div>}                    <div>g</div>
                                    <div>Sodium</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Sodium*(servSize/100), 0)}</div>}        <div>mg</div>
                                    {nutVals  == null ? <div>-</div> : <div>{JC_Utils.roundAndCutZeroes(nutVals.Sodium, 0)}</div>}                       <div>mg</div>
                                </div>

                            </div>
                        </div>


                    </div>

                    {/* Selections + Add To Cart */}
                    <div className={styles.selectionsContainer}>

                        {/* Variation Dropdowns */}
                        {hasVariations && product.getVariationCategories().map(c =>
                            <JC_Dropdown
                                key={c.Code}
                                overrideClass={styles.variationDropdown}
                                type={DropdownTypeEnum.Default}
                                label={c.Name}
                                placeholder={`Select a ${c.Name.toLowerCase()}...`}
                                options={product.Ex_Variations
                                                .filter(v => v.VariationCategoryCode == c.Code && v.EnabledOnWebsite)
                                                .sort((a,b) => a.SortOrder > b.SortOrder ? 1 : -1)
                                                .map(v => ({
                                                    OptionId: v.Id,
                                                    Label: v.Name
                                                    // Label: `${v.Name}${v.AddedPrice > 0 ? ` + $${v.AddedPrice.toFixed(2)}` : ''}`
                                                }))}
                                onOptionMouseOver={(_:string) => {}}
                                onOptionMouseOut={(_:string) => {}}
                                selectedOptionId={[selectedVariation1, selectedVariation2].find(v => v && v.VariationCategoryCode === c.Code)?.Id}
                                onSelection={(newVarId) => {
                                    selectVariation(newVarId);
                                }}
                            />
                        )}

                        {/* $ / Unit */}
                        <div className={styles.pricePerUnit}>
                            {!allSelectionsMade
                                ? "-"
                                : !hasDiscount
                                    ? `$${product.getPriceForSelections()} / ${product.UnitName}`
                                    : <div>
                                        <div className={styles.discountPrice}>{`$${product.getPriceForSelections()}`}</div>
                                        <div>{`$${product.getPriceForSelections()*discountValue} / ${product.UnitName}`}</div>
                                    </div>}
                        </div>

                        {/* Quantity */}
                        <div className={styles.quantityContainer}>

                            {/* Reduce */}
                            <div className={`${styles.quantityButton} ${styles.quantityTake}`} onClick={() => !addToBagLoading ? setQuantity(Math.max(1, quantity-1)) : null}>-</div>
                            {/* Num */}
                            <div className={styles.quantityNum}>{quantity}</div>
                            {/* Add */}
                            <div className={`${styles.quantityButton} ${styles.quantityAdd}`} onClick={() => !addToBagLoading ? setQuantity(quantity+1) : null}>+</div>
                            {/* Total */}
                            <div className={styles.quantityTotal}>
                                {!allSelectionsMade
                                    ? "-"
                                    : `$${(product.getPriceForSelections() * quantity * discountValue).toFixed(2)}`}
                            </div>

                        </div>

                        {/* Add To Bag */}
                        <JC_Button
                            overrideClass={styles.addToBagButtonOverride}
                            text="Add To Bag"
                            isSecondary
                            onClick={addToBag}
                            isDisabled={!allSelectionsMade}
                            isLoading={addToBagLoading}
                        />

                    </div>

                </div>

            </div>
        );
}
