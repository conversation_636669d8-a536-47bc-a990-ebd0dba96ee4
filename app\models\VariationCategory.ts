import { _Base } from "./_Base";

export class VariationCategoryModel extends _Base {

    static apiRoute:string = "variationCategory";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    HasLabelSizeSettings: boolean;
    HasIngredientsSettings: boolean;
    HasServingsSettings: boolean;
    HasLabelsSettings: boolean;
    HasNameOverrideSettings: boolean;
    SortOrder: number;


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<VariationCategoryModel>) {
        super(init);
        this.Code = "",
        this.Name = "",
        this.HasLabelSizeSettings = false,
        this.HasIngredientsSettings = false,
        this.HasServingsSettings = false,
        this.HasLabelsSettings = false,
        this.HasNameOverrideSettings = false,
        this.SortOrder = 999,
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}