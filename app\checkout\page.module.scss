@import '../global';


.checkoutSubTitleOverride {
    color: $offBlack !important;
    font-size: 40px !important;
    margin-bottom: 20px !important;
    grid-column: 1 / -1;
}

.mainContainer {
    @include mainPageStyles;

    .title {
        grid-column: 1 / -1;
    }

    // Body With 2 Columns
    .mainBody {
        display: flex;
        justify-content: space-evenly;

        // Bag
        .bagContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
            row-gap: 16px;
            position: relative;
            width: max-content;

            // Charlotte
            .charlotte {
                width: 255px;
                height: auto;
                position: absolute;
                left: -320px;
                top: 40px;
                opacity: $charlotteOpacity;
            }
        }

        // Details
        .detailsFormOverride {
            height: max-content;

            // Set width of fields
            .fieldOverride {
                width: 280px;
            }

            // Space "Company" over 2 columns
            .companyFieldOverride {
                grid-column: 1 / 3;
            }

            // Pick-Up/Delivery Buttons
            .pickUpDeliveryButtonsContainer {
                grid-column: 1 / 3;
                display: flex;
                column-gap: 30px;
                margin-top: 10px;

                // Button
                .pickUpDeliveryButtonOverride {
                    height: 88px;
                    outline-color: $offBlack;
                    &:hover {
                        outline-color: $secondaryColor;
                    }
                }

                // Button Selected
                .pickUpDeliveryButtonSelectedOverride {
                    height: 88px;
                    outline-color: $primaryColor;
                    cursor: default;
                    outline-offset: initial;
                }

                // Pick-Up Icon
                .pickUpIconOverride {
                    height: 48px;
                }

                // Delivery Icon
                .deliveryIconOverride {
                    margin-top: 9px;
                    height: 38px;
                }

            }
        }
    }

    // Payment Modal
    .orDivider {
        margin-top: 34px;
        width: 100%;
        height: max-content;
        position: relative;
        overflow: visible;

        & > hr {
            width: 100%;
            border-bottom: $smallBorderWidth $greyHover;
        }

        .orText {
            position: absolute;
            top: 0%; left: 50%; transform: translate(-50%, -52%);
            padding: 0px 10px;
            background-color: $offWhite;
            font-size: 16px;
            color: $offBlack;
        }
    }

}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainBody {
        justify-content: space-around !important;
        .fieldOverride {
            width: 220px !important;
        }
    }
}

@media (max-width: $smallScreenSize) {
    .mainBody {
        flex-direction: column;
        align-items: center;
        row-gap: 60px;
        // align-items: center;
    }
}

// @media (max-width: $tinyScreenSize) {
//     .importantInfoContainer {
//         height: 450px !important;
//         .impInfoText {
//             font-size: 30px !important;
//         }
//     }
// }