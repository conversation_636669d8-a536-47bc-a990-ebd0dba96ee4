SELECT "review"."Id"
      ,"review"."ProductId"
      ,"product"."Name" "__Product"
      ,"review"."UserId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"user"."CompanyName" "__UserCompany"
      ,"review"."Rating"
      ,"review"."Text"
      ,"review"."CreatedAt"
      ,"review"."ModifiedAt"
      ,"review"."Deleted"
FROM public."UserProductReview" "review"
INNER JOIN public."Product" "product" ON "review"."ProductId" = "product"."Id"
INNER JOIN public."User" "user" ON "review"."UserId" = "user"."Id"
WHERE 1=1
      AND "review"."Deleted" = 'False'
ORDER BY "review"."CreatedAt" DESC;