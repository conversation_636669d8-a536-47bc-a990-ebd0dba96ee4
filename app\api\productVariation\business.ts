import { sql } from "@vercel/postgres";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ItemIngredientModel } from "@/app/models/ItemIngredient";
import { ItemIngredientBusiness } from "../itemIngredient/business";
import { ItemSettingsBusiness } from "../itemSettings/business";

export class ProductVariationBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetForProduct(productId:string) {
        const variations = (await sql<ProductVariationModel>`
            SELECT "Id",
                    "ProductId",
                    "VariationCategoryCode",
                    "DependentOnProductVariationId",
                    "Name",
                    "AddedPrice",
                    "ImageFileName",
                    "SortOrder",
                    "EnabledOnWebsite",
                    "CreatedAt"
            FROM public."ProductVariation"
            WHERE "ProductId" = ${productId}
              AND "Deleted" = 'False'
        `).rows;

        // Get settings for each variation
        for (const variation of variations) {
            const settings = await ItemSettingsBusiness.GetForProductVariation(variation.Id);
            if (settings && settings.length > 0) {
                variation.Ex_Settings = settings[0];
            }
        }

        return variations;
    }

    static async GetNextSortOrder(productId:string) {
        let latestSortOrder = (await sql`
            SELECT MAX("SortOrder") "LatestSortOrder"
            FROM public."ProductVariation"
            WHERE "ProductId" = ${productId}
              AND "Deleted" = 'False'
        `).rows[0]["LatestSortOrder"];
        return latestSortOrder + 1;
    }

    static async RecordExists(id: string) {
        const result = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."ProductVariation"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `;
        return result.rows[0]["Count"] > 0;
    }


    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newItem:ProductVariationModel) {
        // Insert the new ProductVariation record
        await sql`
            INSERT INTO public."ProductVariation"
            (
                "Id",
                "ProductId",
                "VariationCategoryCode",
                "DependentOnProductVariationId",
                "Name",
                "AddedPrice",
                "ImageFileName",
                "SortOrder",
                "EnabledOnWebsite",
                "CreatedAt"
            )
            VALUES
            (
                ${newItem.Id},
                ${newItem.ProductId},
                ${newItem.VariationCategoryCode},
                ${newItem.DependentOnProductVariationId},
                ${newItem.Name},
                ${newItem.AddedPrice},
                ${newItem.ImageFileName},
                ${await this.GetNextSortOrder(newItem.ProductId)},
                ${newItem.EnabledOnWebsite},
                ${new Date().toUTCString()}
            )
        `;

        // Create settings for the variation if they exist
        if (newItem.Ex_Settings) {
            // Make sure the settings have the correct IDs
            newItem.Ex_Settings.ProductId = newItem.ProductId;
            newItem.Ex_Settings.ProductVariationId = newItem.Id;

            await ItemSettingsBusiness.Create(newItem.Ex_Settings);
        }

        // Create ingredients for the variation if they exist
        if (newItem.Ex_Ingredients && newItem.Ex_Ingredients.length > 0) {
            const ingredientItems = newItem.Ex_Ingredients.map(ing => new ItemIngredientModel({
                // Set only ProductVariationId, not ProductId to comply with constraint
                ProductId: null,
                ProductVariationId: newItem.Id,
                IngredientId: ing.IngredientId, // Use IngredientId instead of Id
                AmountGrams: ing.AmountGrams,
                ShowPercent: ing.ShowPercent,
                Ex_ProductName: "",
                Ex_ProductVariationName: newItem.Name,
                // Preserve the Ex_Ingredient data if it exists
                Ex_Ingredient: ing.Ex_Ingredient
            }));

            if (ingredientItems.length > 0) {
                await ItemIngredientBusiness.CreateList(ingredientItems);
            }
        }

        // ProductVariationsData creation is now handled on the frontend
    }


    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(item: ProductVariationModel) {
        // Check if the variation exists
        const exists = await this.RecordExists(item.Id);

        // If the variation doesn't exist, create it instead
        if (!exists) {
            await this.Create(item);
            return;
        }

        // Update the variation
        await sql`
            UPDATE public."ProductVariation"
            SET "ProductId"                     = ${item.ProductId},
                "VariationCategoryCode"         = ${item.VariationCategoryCode},
                "DependentOnProductVariationId" = ${item.DependentOnProductVariationId},
                "Name"                          = ${item.Name},
                "AddedPrice"                    = ${item.AddedPrice},
                "ImageFileName"                 = ${item.ImageFileName},
                "SortOrder"                     = ${item.SortOrder},
                "EnabledOnWebsite"              = ${item.EnabledOnWebsite},
                "ModifiedAt"                    = ${new Date().toUTCString()},
                "Deleted"                       = ${item.Deleted ?? false}
            WHERE "Id" = ${item.Id}
        `;

        // Update settings if this variation has settings
        if (item.Ex_Settings && item.Ex_Settings.UI_HasChanges) {
            // Make sure the settings have the correct IDs
            item.Ex_Settings.ProductId = item.ProductId;
            item.Ex_Settings.ProductVariationId = item.Id;

            await ItemSettingsBusiness.Update(item.Ex_Settings);
        }

        // Update ingredients if this variation has ingredients
        if (item.Ex_Ingredients && item.Ex_Ingredients.length > 0) {
            // Get ingredients that have changes
            const ingredientsToUpdate = item.Ex_Ingredients.filter(i => i.UI_HasChanges || i.Deleted);

            // Map ingredients to ItemIngredientModel, ensuring we preserve the correct properties
            const ingredientItems = ingredientsToUpdate.map(ing => {
                // Create a new ItemIngredientModel with the correct properties
                const newItem = new ItemIngredientModel({
                    Id: ing.Id,
                    ProductId: null, // Ensure ProductId is null for variation ingredients
                    ProductVariationId: item.Id,
                    IngredientId: ing.IngredientId, // Use IngredientId, not Id
                    AmountGrams: ing.AmountGrams,
                    ShowPercent: ing.ShowPercent,
                    Deleted: ing.Deleted,
                    Ex_ProductName: "",
                    Ex_ProductVariationName: item.Name,
                    Ex_Ingredient: ing.Ex_Ingredient, // Preserve the Ex_Ingredient data
                    UI_HasChanges: ing.UI_HasChanges
                });
                return newItem;
            });

            // Update all ingredients at once
            if (ingredientItems.length > 0) {
                await ItemIngredientBusiness.UpdateList(ingredientItems);
            }
        }
    }

    static async UpdateList(items: ProductVariationModel[]) {
        for (const item of items) {
            await this.Update(item);
        }
    }


    // - ------------ - //
    // - UPDATE SORT - //
    // - ------------ - //

    static async UpdateSortOrder(id: string, sortOrder: number) {
        await sql`
            UPDATE public."ProductVariation"
            SET "SortOrder" = ${sortOrder},
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `;
    }


    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id:string) {

        const { ProductId: productId, VariationCategoryCode: variationCategoryCode } = (await sql`
            SELECT "ProductId", "VariationCategoryCode"
            FROM public."ProductVariation"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `).rows[0];

        let numCategories = (await sql`
            SELECT COUNT(DISTINCT "VariationCategoryCode") "Count"
            FROM public."ProductVariation"
            WHERE "ProductId" = ${productId}
              AND "Deleted" = 'False'
        `).rows[0]["Count"];

        // - ProductVariationsData - //

        // IF only have 1 category, just delete that ProductVariationsData record
        if (numCategories == 1) {
            await sql`
                UPDATE public."ProductVariationsData"
                SET "Deleted"    = 'True',
                    "ModifiedAt" = ${new Date().toUTCString()}
                WHERE (UPPER(${id}) = ANY("ProductVariationIds")
                    OR LOWER(${id}) = ANY("ProductVariationIds"))
            `;
        }
        // ELSE we assume there is 2
        else {
            let numInCategory = (await sql`
                SELECT COUNT("Id") "Count"
                FROM public."ProductVariation"
                WHERE "ProductId" = ${productId} AND "VariationCategoryCode" = ${variationCategoryCode}
                  AND "Deleted" = 'False'
            `).rows[0]["Count"];;
            // IF this is the only variation in the variation category, just remove this id from each existing variations data productids
            if (numInCategory == 1) {
                await sql`
                    UPDATE public."ProductVariationsData"
                    SET "ProductVariationIds" = array_remove("ProductVariationIds", ${id})
                    WHERE (UPPER(${id}) = ANY("ProductVariationIds")
                        OR LOWER(${id}) = ANY("ProductVariationIds"))
                      AND "Deleted" = 'False'
                `;
            }
            // ELSE delete each variations data record that contains this Id in ProductVariationIds
            else {
                await sql`
                    UPDATE public."ProductVariationsData"
                    SET "Deleted"    = 'True',
                        "ModifiedAt" = ${new Date().toUTCString()}
                    WHERE (UPPER(${id}) = ANY("ProductVariationIds")
                        OR LOWER(${id}) = ANY("ProductVariationIds"))
                `;
            }
        }

        // - ProductVariation - //

        await sql`
            UPDATE public."ProductVariation"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;

    }

}