// Styles
import styles from "./JC_Footer.module.scss";
// React
import React from "react";
// Next
import Link from "next/link";
import Image from "next/image";
// JC Components
import <PERSON><PERSON>_<PERSON><PERSON> from "../JC_Button/JC_Button";
import J<PERSON>_CasellaWeb from "../JC_CasellaWeb/JC_CasellaWeb";

export default function JC_Footer() {
    return (

        <div className={styles.mainContainer} id="JC_footer">

            <div className={styles.innerContainer}>

                {/* Nav Buttons */}
                <div className={styles.navButtons}>
                    <Link className={styles.link} href="/">Home</Link>
                    <Link className={styles.link} href="productGroups">Products</Link>
                    <Link className={styles.link} href="about">About Us</Link>
                    <Link className={styles.link} href="stockists">Stockists</Link>
                    <Link className={styles.link} href="contact">Contact</Link>
                    <Link className={styles.link} href="privacyPolicy">Privacy Policy</Link>
                </div>


                {/* Social */}
                <div className={styles.rightContainer}>

                    <div className={styles.trademark}>© 2024 Casella Kitchen</div>

                    <div className={styles.socialContainer}>
                        {/* Facebook */}
                        <Link href={"https://www.facebook.com/casellakitchen"} target="_blank">
                            <Image
                                className={styles.socialIcon}
                                src="/icons/SocialFacebook.webp"
                                width={200}
                                height={200}
                                alt="CasellaKitchenFacebook"
                            />
                        </Link>

                        {/* Insta */}
                        <Link href={"https://www.instagram.com/casellakitchen"} target="_blank">
                            <Image
                                className={styles.socialIcon}
                                src="/icons/SocialInsta.webp"
                                width={200}
                                height={200}
                                alt="CasellaKitchenInsta"
                            />
                        </Link>
                    </div>

                    {/* Casella Web */}
                    <JC_CasellaWeb overrideClass={styles.casellaWebOverride} />

                </div>

                
            </div>

        </div>

    );
}
