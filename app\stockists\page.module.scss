@import '../global';

.mainContainer {
    @include mainPageStyles;
    display: flex;
    flex-direction: column;

    // Title (small screens)
    .title {
        display: none;
    }

    // Group titles
    .groupTitle {
        margin-top: 60px;
        margin-bottom: 20px;
        text-align: center;
    }

    // Logo + Map
    .logoMap {
        height: max-content;
        padding: 0 80px;
        display: flex;
        justify-content: space-between;
        column-gap: 20px;

        // Logo
        .logo {
            width: 350px;
            height: auto;
        }

        // Map
        .map {
            flex-grow: 1;
        }
    }

    // List
    .listContainer {
        margin-top: 20px;
        width: 100%;
        height: max-content;
        display: grid;
        grid-template-columns: 300px 300px 300px;
        justify-content: space-evenly;
        align-items: start;
        column-gap: 30px;
        row-gap: 50px;

        .item {
            display: flex;
            flex-direction: column;
            row-gap: 10px;
            align-items: center;
            text-align: center;
            cursor: pointer;

            // Logo
            .itemLogo {
                width: auto;
                height: 150px;
            }

            // Name
            .itemName {
                font-size: 20px;
                font-weight: bold;
            }

            // Address
            .itemAddress {
                font-size: 15px;
            }

            // Phone
            .itemPhone {
                font-size: 15px;
            }

            &:hover {
                .itemName {
                    font-weight: bold;
                    color: $primaryColor;
                }
            }
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    // .logoMap {
    //     padding: 0 50px !important;
    //     column-gap: 15px !important;
    //     .logo {
    //         width: 270px !important;
    //     }
    // }
}

@media (max-width: $smallScreenSize) {
    .title {
        display: initial !important;
        height: 100px !important;
    }
    .logoMap {
        padding: 0 40px !important;
        display: initial !important;
        .logo { display: none; }
        .map { height: 300px; }
    }
    .listContainer {
        grid-template-columns: 300px 300px !important;
    }
}

@media (max-width: $tinyScreenSize) {
    .title {
        display: initial !important;
        height: 100px !important;
    }
    .logoMap {
        padding: 0 10px !important;
        .logo { display: none; }
        .map { height: 300px; }
    }
    .listContainer {
        grid-template-columns: 300px !important;
    }
}