SELECT "bag"."Id"
      ,"bag"."UserId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"user"."CompanyName" "__UserCompany"
      ,"bag"."ProductId"
      ,"product"."Name" "__Product"
      ,"bag"."ProductVariationIds"
      ,COALESCE(
          (SELECT array_agg("variation"."Name" ORDER BY "variation_ord")
          FROM unnest("bag"."ProductVariationIds") WITH ORDINALITY AS "variationId"(id, variation_ord)
          JOIN public."ProductVariation" "variation" ON "variation"."Id" = "variationId".id::uuid
          ), '{}'
      ) AS "__ProductVariations"
      ,"bag"."Quantity"
      ,"bag"."CreatedAt"
      ,"bag"."ModifiedAt"
      ,"bag"."Deleted"
FROM public."BagItem" "bag"
INNER JOIN public."User" "user" ON "bag"."UserId" = "user"."Id"
INNER JOIN public."Product" "product" ON "bag"."ProductId" = "product"."Id"
WHERE 1=1
      AND "bag"."Deleted" = 'False'
ORDER BY "bag"."CreatedAt" DESC;