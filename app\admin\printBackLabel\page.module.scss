@import '../../global';
@import '../admin';

// Print Settings
@page {
    margin: 0.3cm;
}

// Page
.printContainer {
    width: 100%;
    height: 100%;
    background-color: white;
}

.mainContainer {
    // Padding is now set via inline style from global settings
    width: 771px;
    height: 1090px;
    display: grid;
    grid-template-columns: max-content max-content;
    justify-content: space-between;
    align-content: space-between;
    align-items: center;
    background-color: white;
    box-sizing: border-box;
}

.mainContainer.small {
    // Padding is now set via inline style from global settings
}

.mainContainer.twoByFive {
    // Padding is now set via inline style from global settings
}


// - TESTING - //
.mainContainer {
    // > div:nth-child(1)  { visibility: hidden; }
    // > div:nth-child(2)  { visibility: hidden; }
    // > div:nth-child(3)  { visibility: hidden; }
    // > div:nth-child(4)  { visibility: hidden; }
    // > div:nth-child(5)  { visibility: hidden; }
    // > div:nth-child(6)  { visibility: hidden; }
    // > div:nth-child(7)  { visibility: hidden; }
    // > div:nth-child(8)  { visibility: hidden; }
    // > div:nth-child(9)  { visibility: hidden; }
    // > div:nth-child(10) { visibility: hidden; }
}
// - ------- - //