"use client"

import styles from "./JC_Spinner.module.scss";
import React from 'react';
import Image from "next/image";
import { JC_Utils } from "@/app/Utils";

export default function JC_Spinner(_: Readonly<{

    overrideClass?: string;
    isPageBody?: boolean;
    isSmall?: boolean;

}>) {
    return (
        <div className={`
            ${styles.spinnerContainer}
            ${_.isPageBody ? styles.emptyPage : ''}
            ${_.isSmall ? styles.small : ''}
            ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}
        `}>
            <Image
                className={styles.spinnerImage}
                src={`/icons/CK.webp`}
                width={0}
                height={0}
                alt="CKLogo"
                unoptimized
            />
        </div>
    );
}
