import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen - Stockists",
    description: "View Casella Kitchen's list of stockists."
};

export default async function Layout_Stockists(_: Readonly<{

    children: React.ReactNode;

}>) {

    // - AUTH - //

    const session = await auth();
    if (!session) {
        redirect("/");
    }

    // - MAIN - //

    return _.children;
    
}
