"use client"

import styles from "./tempComingSoon.module.scss";
import Image from "next/image";
import JC_Title from "./components/JC_Title/JC_Title";
import { LocalStorageKeyEnum } from "./enums/LocalStorageKey";
import { signIn, useSession } from "next-auth/react";

var currentCode = "";
async function addToCode(newLetter:string) {
    currentCode += newLetter;
    if (currentCode == "1211212112") {
        localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
        await signIn("credentials", { email: "<EMAIL>", password: "Letsgo.123", callbackUrl: "/" });
    }
}

export default function TempComingSoon() {

    return (
        <div className={styles.comingSoonContainer}>
            <div onClick={() => addToCode("1")}>
                <Image
                    src="/logos/Main.webp"
                    width={0}
                    height={0}
                    className={styles.logo}
                    alt="CasellaKitchenLogo"
                    unoptimized
                />
            </div>
            <div onClick={() => addToCode("2")}><JC_Title title="Coming soon!" /></div>
        </div>
    );
}
