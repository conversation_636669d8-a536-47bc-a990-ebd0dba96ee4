import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen - Forgot Password",
    description: "Enter an email to being the process of resetting your password."
};

export default async function Layout_ForgotPassword(_: Readonly<{

    children: React.ReactNode;

}>) {

    // - AUTH - //

    const session = await auth();
    if (!session) {
        redirect("/");
    }

    // - MAIN - //

    return _.children;
    
}
