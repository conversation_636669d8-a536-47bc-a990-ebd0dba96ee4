import { sql } from "@vercel/postgres";
import { ItemSettingsModel } from "@/app/models/ItemSettings";
import { JC_Utils } from "@/app/Utils";

export class ItemSettingsBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll() {
        const results = (await sql<ItemSettingsModel>`
            SELECT "settings"."Id",
                   "settings"."ProductId",
                   "settings"."ProductVariationId",
                   "settings"."ServingSizeGrams",
                   "settings"."ServingsPerPack",
                   "settings"."WeightChange",
                   "settings"."LabelSize",
                   "settings"."PackWeightText",
                   "settings"."FrontLabelNameOverride",
                   "settings"."FrontLabelNameXOffset",
                   "settings"."BackLabelNameOverride",
                   "settings"."IngredientsFontSize",
                   "settings"."IngredientsYOffset",
                   "settings"."InstructionsText",
                   "settings"."InstructionsFontSize",
                   "settings"."NoteText",
                   "settings"."NoteFontSize",
                   "settings"."AllergensText",
                   "settings"."FreeFromText",
                   "settings"."BiodegOverrideText",
                   "settings"."StoreFrozenOverride",
                   "settings"."IconName",
                   "settings"."IconVisible",
                   "settings"."IconX",
                   "settings"."IconY",
                   "settings"."IconSize",
                   "settings"."NutValsEnabled",
                   "settings"."CreatedAt",
                   "settings"."ModifiedAt",
                   "settings"."Deleted",
                   "product"."Name" "Ex_ProductName",
                   "variation"."Name" "Ex_ProductVariationName"
            FROM public."ItemSettings" "settings"
            LEFT JOIN public."Product" "product" ON "settings"."ProductId" = "product"."Id"
            LEFT JOIN public."ProductVariation" "variation" ON "settings"."ProductVariationId" = "variation"."Id"
            WHERE "settings"."Deleted" = 'False'
              AND (
                  ("settings"."ProductId" IS NOT NULL AND "product"."Deleted" = 'False') OR
                  ("settings"."ProductId" IS NULL)
              )
              AND (
                  ("settings"."ProductVariationId" IS NOT NULL AND "variation"."Deleted" = 'False') OR
                  ("settings"."ProductVariationId" IS NULL)
              )
        `).rows;

        // Log some of the results for debugging
        if (results.length > 0) {
            console.log(`Retrieved ${results.length} ItemSettings records`);

            // Log a few records with non-zero IngredientsYOffset for debugging
            const nonZeroOffsets = results.filter(r => r.IngredientsYOffset !== 0 && r.IngredientsYOffset !== null);
            if (nonZeroOffsets.length > 0) {
                console.log(`Found ${nonZeroOffsets.length} records with non-zero IngredientsYOffset:`);
                nonZeroOffsets.slice(0, 5).forEach(r => {
                    console.log(`  - ID: ${r.Id}, ProductId: ${r.ProductId}, ProductVariationId: ${r.ProductVariationId}, IngredientsYOffset: ${r.IngredientsYOffset}, Type: ${typeof r.IngredientsYOffset}`);
                });
            } else {
                console.log(`No records found with non-zero IngredientsYOffset`);
            }
        }

        // Ensure numeric values are properly converted to numbers
        results.forEach(item => {
            item.IngredientsYOffset = Number(item.IngredientsYOffset || 0);
            item.IngredientsFontSize = Number(item.IngredientsFontSize || 10);
            item.InstructionsFontSize = Number(item.InstructionsFontSize || 10);
            item.NoteFontSize = Number(item.NoteFontSize || 10);
            item.IconX = Number(item.IconX || 9);
            item.IconY = Number(item.IconY || -12);
            item.IconSize = Number(item.IconSize || 32);
            item.FrontLabelNameXOffset = Number(item.FrontLabelNameXOffset || 0);
        });

        return results;
    }

    static async GetForProduct(productId: string) {
        if (!productId) {
            return [];
        }

        const results = (await sql<ItemSettingsModel>`
            SELECT "settings"."Id",
                   "settings"."ProductId",
                   "settings"."ProductVariationId",
                   "settings"."ServingSizeGrams",
                   "settings"."ServingsPerPack",
                   "settings"."WeightChange",
                   "settings"."LabelSize",
                   "settings"."PackWeightText",
                   "settings"."FrontLabelNameOverride",
                   "settings"."FrontLabelNameXOffset",
                   "settings"."BackLabelNameOverride",
                   "settings"."IngredientsFontSize",
                   "settings"."IngredientsYOffset",
                   "settings"."InstructionsText",
                   "settings"."InstructionsFontSize",
                   "settings"."NoteText",
                   "settings"."NoteFontSize",
                   "settings"."AllergensText",
                   "settings"."FreeFromText",
                   "settings"."BiodegOverrideText",
                   "settings"."StoreFrozenOverride",
                   "settings"."IconName",
                   "settings"."IconVisible",
                   "settings"."IconX",
                   "settings"."IconY",
                   "settings"."IconSize",
                   "settings"."NutValsEnabled",
                   "settings"."CreatedAt",
                   "settings"."ModifiedAt",
                   "settings"."Deleted",
                   "product"."Name" "Ex_ProductName"
            FROM public."ItemSettings" "settings"
            LEFT JOIN public."Product" "product" ON "settings"."ProductId" = "product"."Id"
            WHERE "settings"."ProductId" = ${productId}
              AND "settings"."ProductVariationId" IS NULL
              AND "settings"."Deleted" = 'False'
              AND "product"."Deleted" = 'False'
        `).rows;

        // Log the results for debugging
        if (results.length > 0) {
            console.log(`Retrieved ${results.length} ItemSettings records for product ${productId}`);
            results.forEach(r => {
                console.log(`  - ID: ${r.Id}, IngredientsYOffset: ${r.IngredientsYOffset}, Type: ${typeof r.IngredientsYOffset}`);
            });
        }

        // Ensure numeric values are properly converted to numbers
        results.forEach(item => {
            item.IngredientsYOffset = Number(item.IngredientsYOffset || 0);
            item.IngredientsFontSize = Number(item.IngredientsFontSize || 10);
            item.InstructionsFontSize = Number(item.InstructionsFontSize || 10);
            item.NoteFontSize = Number(item.NoteFontSize || 10);
            item.IconX = Number(item.IconX || 9);
            item.IconY = Number(item.IconY || -12);
            item.IconSize = Number(item.IconSize || 32);
            item.FrontLabelNameXOffset = Number(item.FrontLabelNameXOffset || 0);
        });

        return results;
    }

    static async GetForProductVariation(productVariationId: string) {
        if (!productVariationId) {
            return [];
        }

        const results = (await sql<ItemSettingsModel>`
            SELECT "settings"."Id",
                   "settings"."ProductId",
                   "settings"."ProductVariationId",
                   "settings"."ServingSizeGrams",
                   "settings"."ServingsPerPack",
                   "settings"."WeightChange",
                   "settings"."LabelSize",
                   "settings"."PackWeightText",
                   "settings"."FrontLabelNameOverride",
                   "settings"."FrontLabelNameXOffset",
                   "settings"."BackLabelNameOverride",
                   "settings"."IngredientsFontSize",
                   "settings"."IngredientsYOffset",
                   "settings"."InstructionsText",
                   "settings"."InstructionsFontSize",
                   "settings"."NoteText",
                   "settings"."NoteFontSize",
                   "settings"."AllergensText",
                   "settings"."FreeFromText",
                   "settings"."BiodegOverrideText",
                   "settings"."StoreFrozenOverride",
                   "settings"."IconName",
                   "settings"."IconVisible",
                   "settings"."IconX",
                   "settings"."IconY",
                   "settings"."IconSize",
                   "settings"."NutValsEnabled",
                   "settings"."CreatedAt",
                   "settings"."ModifiedAt",
                   "settings"."Deleted",
                   "product"."Name" "Ex_ProductName",
                   "variation"."Name" "Ex_ProductVariationName"
            FROM public."ItemSettings" "settings"
            LEFT JOIN public."Product" "product" ON "settings"."ProductId" = "product"."Id"
            LEFT JOIN public."ProductVariation" "variation" ON "settings"."ProductVariationId" = "variation"."Id"
            WHERE "settings"."ProductVariationId" = ${productVariationId}
              AND "settings"."Deleted" = 'False'
              AND (
                  ("settings"."ProductId" IS NOT NULL AND "product"."Deleted" = 'False') OR
                  ("settings"."ProductId" IS NULL)
              )
              AND "variation"."Deleted" = 'False'
        `).rows;

        // Log the results for debugging
        if (results.length > 0) {
            console.log(`Retrieved ${results.length} ItemSettings records for variation ${productVariationId}`);
            results.forEach(r => {
                console.log(`  - ID: ${r.Id}, IngredientsYOffset: ${r.IngredientsYOffset}, Type: ${typeof r.IngredientsYOffset}`);
            });
        }

        // Ensure numeric values are properly converted to numbers
        results.forEach(item => {
            item.IngredientsYOffset = Number(item.IngredientsYOffset || 0);
            item.IngredientsFontSize = Number(item.IngredientsFontSize || 10);
            item.InstructionsFontSize = Number(item.InstructionsFontSize || 10);
            item.NoteFontSize = Number(item.NoteFontSize || 10);
            item.IconX = Number(item.IconX || 9);
            item.IconY = Number(item.IconY || -12);
            item.IconSize = Number(item.IconSize || 32);
            item.FrontLabelNameXOffset = Number(item.FrontLabelNameXOffset || 0);
        });

        return results;
    }

    static async RecordExists(id: string) {
        const result = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."ItemSettings"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `;
        return result.rows[0]["Count"] > 0;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newItem: ItemSettingsModel) {
        // Validate that either ProductId or ProductVariationId is set (but not both)
        if ((newItem.ProductId === null || newItem.ProductId === "") &&
            (newItem.ProductVariationId === null || newItem.ProductVariationId === "")) {
            throw new Error("Either ProductId or ProductVariationId must be set for ItemSettings");
        }

        // If both are set, prioritize ProductVariationId and set ProductId to null
        if (newItem.ProductId && newItem.ProductVariationId) {
            console.warn("Both ProductId and ProductVariationId are set for ItemSettings. Using ProductVariationId only.");
            newItem.ProductId = null;
        }

        // Log the item being created for debugging
        console.log(`Creating new ItemSettings:`, {
            id: newItem.Id,
            productId: newItem.ProductId,
            productVariationId: newItem.ProductVariationId,
            ingredientsYOffset: newItem.IngredientsYOffset,
            ingredientsYOffsetType: typeof newItem.IngredientsYOffset
        });

        // Ensure numeric values are stored as numbers with default values if null/undefined
        newItem.IngredientsYOffset = Number(newItem.IngredientsYOffset || 0);
        newItem.IngredientsFontSize = Number(newItem.IngredientsFontSize || 10);
        newItem.InstructionsFontSize = Number(newItem.InstructionsFontSize || 10);
        newItem.NoteFontSize = Number(newItem.NoteFontSize || 10);
        newItem.IconX = Number(newItem.IconX || 9);
        newItem.IconY = Number(newItem.IconY || -12);
        newItem.IconSize = Number(newItem.IconSize || 32);
        newItem.FrontLabelNameXOffset = Number(newItem.FrontLabelNameXOffset || 0);

        await sql`
            INSERT INTO public."ItemSettings"
            (
                "Id",
                "ProductId",
                "ProductVariationId",
                "ServingSizeGrams",
                "ServingsPerPack",
                "WeightChange",
                "LabelSize",
                "PackWeightText",
                "FrontLabelNameOverride",
                "FrontLabelNameXOffset",
                "BackLabelNameOverride",
                "IngredientsFontSize",
                "IngredientsYOffset",
                "InstructionsText",
                "InstructionsFontSize",
                "NoteText",
                "NoteFontSize",
                "AllergensText",
                "FreeFromText",
                "BiodegOverrideText",
                "StoreFrozenOverride",
                "IconName",
                "IconVisible",
                "IconX",
                "IconY",
                "IconSize",
                "NutValsEnabled",
                "CreatedAt"
            )
            VALUES
            (
                ${newItem.Id},
                ${newItem.ProductId},
                ${newItem.ProductVariationId},
                ${newItem.ServingSizeGrams},
                ${newItem.ServingsPerPack},
                ${newItem.WeightChange},
                ${newItem.LabelSize},
                ${newItem.PackWeightText},
                ${newItem.FrontLabelNameOverride},
                ${newItem.FrontLabelNameXOffset},
                ${newItem.BackLabelNameOverride},
                ${newItem.IngredientsFontSize},
                ${newItem.IngredientsYOffset},
                ${newItem.InstructionsText},
                ${newItem.InstructionsFontSize},
                ${newItem.NoteText},
                ${newItem.NoteFontSize},
                ${newItem.AllergensText},
                ${newItem.FreeFromText},
                ${newItem.BiodegOverrideText},
                ${newItem.StoreFrozenOverride},
                ${newItem.IconName},
                ${newItem.IconVisible},
                ${newItem.IconX},
                ${newItem.IconY},
                ${newItem.IconSize},
                ${newItem.NutValsEnabled},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(items: ItemSettingsModel[]) {
        for (const item of items) {
            await this.Create(item);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(item: ItemSettingsModel) {
        // Validate that either ProductId or ProductVariationId is set (but not both)
        if ((item.ProductId === null || item.ProductId === "") &&
            (item.ProductVariationId === null || item.ProductVariationId === "")) {
            throw new Error("Either ProductId or ProductVariationId must be set for ItemSettings");
        }

        // If both are set, prioritize ProductVariationId and set ProductId to null
        if (item.ProductId && item.ProductVariationId) {
            console.warn("Both ProductId and ProductVariationId are set for ItemSettings. Using ProductVariationId only.");
            item.ProductId = null;
        }

        // Log the item being updated for debugging
        console.log(`Updating ItemSettings:`, {
            id: item.Id,
            productId: item.ProductId,
            productVariationId: item.ProductVariationId,
            ingredientsYOffset: item.IngredientsYOffset,
            ingredientsYOffsetType: typeof item.IngredientsYOffset
        });

        // Ensure numeric values are stored as numbers with default values if null/undefined
        item.IngredientsYOffset = Number(item.IngredientsYOffset || 0);
        item.IngredientsFontSize = Number(item.IngredientsFontSize || 10);
        item.InstructionsFontSize = Number(item.InstructionsFontSize || 10);
        item.NoteFontSize = Number(item.NoteFontSize || 10);
        item.IconX = Number(item.IconX || 9);
        item.IconY = Number(item.IconY || -12);
        item.IconSize = Number(item.IconSize || 32);
        item.FrontLabelNameXOffset = Number(item.FrontLabelNameXOffset || 0);

        // First check if the record exists by ID
        const exists = await this.RecordExists(item.Id);

        if (exists) {
            // Log the SQL parameters for debugging
            console.log(`Updating existing ItemSettings with ID ${item.Id}:`, {
                ingredientsYOffset: item.IngredientsYOffset,
                ingredientsYOffsetType: typeof item.IngredientsYOffset
            });

            // Update existing settings by ID
            await sql`
                UPDATE public."ItemSettings"
                SET "ProductId"              = ${item.ProductId},
                    "ProductVariationId"     = ${item.ProductVariationId},
                    "ServingSizeGrams"       = ${item.ServingSizeGrams},
                    "ServingsPerPack"        = ${item.ServingsPerPack},
                    "WeightChange"           = ${item.WeightChange},
                    "LabelSize"              = ${item.LabelSize},
                    "PackWeightText"         = ${item.PackWeightText},
                    "FrontLabelNameOverride" = ${item.FrontLabelNameOverride},
                    "FrontLabelNameXOffset"  = ${item.FrontLabelNameXOffset},
                    "BackLabelNameOverride"  = ${item.BackLabelNameOverride},
                    "IngredientsFontSize"    = ${item.IngredientsFontSize},
                    "IngredientsYOffset"     = ${item.IngredientsYOffset},
                    "InstructionsText"       = ${item.InstructionsText},
                    "InstructionsFontSize"   = ${item.InstructionsFontSize},
                    "NoteText"               = ${item.NoteText},
                    "NoteFontSize"           = ${item.NoteFontSize},
                    "AllergensText"          = ${item.AllergensText},
                    "FreeFromText"           = ${item.FreeFromText},
                    "BiodegOverrideText"     = ${item.BiodegOverrideText},
                    "StoreFrozenOverride"    = ${item.StoreFrozenOverride},
                    "IconName"               = ${item.IconName},
                    "IconVisible"            = ${item.IconVisible},
                    "IconX"                  = ${item.IconX},
                    "IconY"                  = ${item.IconY},
                    "IconSize"               = ${item.IconSize},
                    "NutValsEnabled"         = ${item.NutValsEnabled},
                    "ModifiedAt"             = ${new Date().toUTCString()},
                    "Deleted"                = ${item.Deleted ?? false}
                WHERE "Id" = ${item.Id}
            `;
            return;
        }

        // If the record doesn't exist by ID, check if settings exist for this product/variation
        let existingSettings = null;

        if (item.ProductVariationId) {
            existingSettings = await this.GetForProductVariation(item.ProductVariationId);
        } else if (item.ProductId) {
            existingSettings = await this.GetForProduct(item.ProductId);
        }

        if (existingSettings && existingSettings.length > 0) {
            // Log the SQL parameters for debugging
            console.log(`Updating existing ItemSettings by product/variation ID ${existingSettings[0].Id}:`, {
                ingredientsYOffset: item.IngredientsYOffset,
                ingredientsYOffsetType: typeof item.IngredientsYOffset
            });

            // Update existing settings
            await sql`
                UPDATE public."ItemSettings"
                SET "ProductId"              = ${item.ProductId},
                    "ProductVariationId"     = ${item.ProductVariationId},
                    "ServingSizeGrams"       = ${item.ServingSizeGrams},
                    "ServingsPerPack"        = ${item.ServingsPerPack},
                    "WeightChange"           = ${item.WeightChange},
                    "LabelSize"              = ${item.LabelSize},
                    "PackWeightText"         = ${item.PackWeightText},
                    "FrontLabelNameOverride" = ${item.FrontLabelNameOverride},
                    "FrontLabelNameXOffset"  = ${item.FrontLabelNameXOffset},
                    "BackLabelNameOverride"  = ${item.BackLabelNameOverride},
                    "IngredientsFontSize"    = ${item.IngredientsFontSize},
                    "IngredientsYOffset"     = ${item.IngredientsYOffset},
                    "InstructionsText"       = ${item.InstructionsText},
                    "InstructionsFontSize"   = ${item.InstructionsFontSize},
                    "NoteText"               = ${item.NoteText},
                    "NoteFontSize"           = ${item.NoteFontSize},
                    "AllergensText"          = ${item.AllergensText},
                    "FreeFromText"           = ${item.FreeFromText},
                    "BiodegOverrideText"     = ${item.BiodegOverrideText},
                    "StoreFrozenOverride"    = ${item.StoreFrozenOverride},
                    "IconName"               = ${item.IconName},
                    "IconVisible"            = ${item.IconVisible},
                    "IconX"                  = ${item.IconX},
                    "IconY"                  = ${item.IconY},
                    "IconSize"               = ${item.IconSize},
                    "NutValsEnabled"         = ${item.NutValsEnabled},
                    "ModifiedAt"             = ${new Date().toUTCString()},
                    "Deleted"                = ${item.Deleted ?? false}
                WHERE "Id" = ${existingSettings[0].Id}
            `;
        } else {
            // Create new settings
            await this.Create(item);
        }
    }

    static async UpdateList(items: ItemSettingsModel[]) {
        for (const item of items) {
            await this.Update(item);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."ItemSettings"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteForProduct(productId: string) {
        await sql`
            UPDATE public."ItemSettings"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductId" = ${productId}
        `;
    }

    static async DeleteForProductVariation(productVariationId: string) {
        await sql`
            UPDATE public."ItemSettings"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductVariationId" = ${productVariationId}
        `;
    }
}
