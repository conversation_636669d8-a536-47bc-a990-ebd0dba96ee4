import { sql } from "@vercel/postgres";
import { StockistModel } from "@/app/models/Stockist";

export class StockistBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll() {
        return (await sql<StockistModel>`
            SELECT "Id",
                    "Name",
                    "AddressString",
                    "AddressLat",
                    "AddressLong",
                    "Phone",
                    "LogoImageName",
                    "SiteUrl",
                    "MapsUrl",
                    "IsFullRange",
                    "SortOrder",
                    "Deleted"
            FROM public."Stockist"
            WHERE "Deleted" = 'False'
            ORDER BY "SortOrder"
        `).rows;
    }


    static async GetNextSortOrder() {
        let latestSortOrder = (await sql`
            SELECT MAX("SortOrder") "LatestSortOrder"
            FROM public."Stockist"
            WHERE "Deleted" = 'False'
        `).rows[0]["LatestSortOrder"];
        return latestSortOrder + 1;
    }


    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(item:StockistModel) {
        await sql`
            INSERT INTO public."Stockist"
            (
                "Id",
                "Name",
                "AddressString",
                "AddressLat",
                "AddressLong",
                "Phone",
                "LogoImageName",
                "SiteUrl",
                "MapsUrl",
                "IsFullRange",
                "SortOrder",
                "CreatedAt"
            )
            VALUES
            (
                ${item.Id},
                ${item.Name},
                ${item.AddressString},
                ${item.AddressLat},
                ${item.AddressLong},
                ${item.Phone},
                ${item.LogoImageName},
                ${item.SiteUrl},
                ${item.MapsUrl},
                ${item.IsFullRange},
                ${item.SortOrder || await this.GetNextSortOrder()},
                ${new Date().toUTCString()}
            )
        `;
    }


    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(item:StockistModel) {
        await sql`
            UPDATE public."Stockist"
            SET "Name"          = ${item.Name},
                "AddressString" = ${item.AddressString},
                "AddressLat"    = ${item.AddressLat},
                "AddressLong"   = ${item.AddressLong},
                "Phone"         = ${item.Phone},
                "LogoImageName" = ${item.LogoImageName},
                "SiteUrl"       = ${item.SiteUrl},
                "MapsUrl"       = ${item.MapsUrl},
                "IsFullRange"   = ${item.IsFullRange},
                "SortOrder"     = ${item.SortOrder},
                "ModifiedAt"    = ${new Date().toUTCString()},
                "Deleted"       = ${item.Deleted ?? false}
            WHERE "Id" = ${item.Id}
        `;
    }

}