import { NextRequest, NextResponse } from "next/server";
import { ProductModel } from "@/app/models/Product";
import { ProductBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const requestData = await request.json();
        const products: ProductModel[] = requestData.products.map((p: any) => new ProductModel(p));
        
        // Delete each product
        for (const product of products) {
            await ProductBusiness.Delete(product.Id);
        }
        
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
