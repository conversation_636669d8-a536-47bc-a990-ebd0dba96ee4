"use client"

import styles from "./page.module.scss";
import React from "react";
import { useEffect, useState } from "react";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import JC_Button from "../components/JC_Button/JC_Button";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Modal from "../components/JC_Modal/JC_Modal";
import JC_StripeCheckout from "../components/JC_StripeCheckout/JC_StripeCheckout";
import JC_BagView from "../components/JC_BagView/JC_BagView";
import JC_DatePicker from "../components/JC_DatePicker/JC_DatePicker";
import { JC_GetList } from "../services/JC_GetList";
import { GetBag } from "../services/Bag";
import { useSession } from "next-auth/react";
import { ProductModel } from "../models/Product";
import { OrderModel } from "../models/Order";
import { FieldTypeEnum } from "../enums/FieldType";
import { ButtonIconPosition } from "../enums/ButtonIconPosition";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils } from "../Utils";


export default function Page_Checkout() {

    const session = useSession();

    // - STATE - //

    const [products, setProducts] = useState<ProductModel[]>([]);
    const [bag, setBag] = useState<OrderModel>();
    const [firstName, setFirstName] = useState<string>(session.data?.user.FirstName ?? "");
    const [lastName, setLastName] = useState<string>(session.data?.user.LastName ?? "");
    const [email, setEmail] = useState<string>(session.data?.user.Email ?? "");
    const [phone, setPhone] = useState<string>(session.data?.user.Phone ?? "");
    const [company, setCompany] = useState<string>(session.data?.user.CompanyName ?? "");
    const [pickUpSelected, setPickUpSelected] = useState<boolean>(true);
    const [testDate, setTestDate] = useState<Date>(new Date());
    const [deliverySelected, setDeliverySelected] = useState<boolean>(false);
    const [paymentModalOpen, setPaymentModalOpen] = useState<boolean>(false);


    // - INITIALISE - //

    useEffect(() => {
        // Set Products
        JC_GetList<ProductModel>(ProductModel.apiRoute, {}, ProductModel).then(list => setProducts(list));
        // Set Bag
        GetBag(session.data).then(bag => setBag(bag));
        // IF came here from performing action, show relavant success
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowBagItemsAdded) == "1") {
            JC_Utils.showToastSuccess(`Items have been added!`);
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowBagItemsAdded, "0");
        }
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowBagItemsReplaced) == "1") {
            JC_Utils.showToastSuccess(`Your bag has been set!`);
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowBagItemsReplaced, "0");
        }
    }, []);


    // - MAIN - //

    return products.length == 0 || bag == null
        ? (<JC_Spinner isPageBody />)
        : (
            <div className={styles.mainContainer}>

                {/* Title */}
                <JC_Title overrideClass={styles.title} title="Checkout" />

                {/* Body With 2 Columns */}
                <div className={styles.mainBody}>

                    {/* Bag Container */}
                    <div className={styles.bagContainer}>

                        {/* Your Items */}
                        <JC_Title overrideClass={styles.checkoutSubTitleOverride} title="Your Items" />

                        {/* Bag */}
                        <JC_BagView enableScroll bag={bag} bagChanged={async (newBag:OrderModel) => await setBag(newBag)} />

                    </div>

                    {/* Details Form */}
                    <JC_Form
                        overrideClass={styles.detailsFormOverride}
                        columns={2}
                        submitButtonText="Go To Payment"
                        onSubmit={() => setPaymentModalOpen(true)}
                        mainValidate={() => !bag.Ex_OrderItems || bag.Ex_OrderItems.length == 0
                                                ? "You have no items!"
                                                : (!pickUpSelected && !deliverySelected)
                                                    ? "You must select Pick-Up or Delivery!"
                                                    : ""}
                        fields={[
                            {
                                overrideClass: styles.fieldOverride,
                                inputId: "details-&-payment-title",
                                type: FieldTypeEnum.Text,
                                customNode: <JC_Title key="details-&-payment-title" overrideClass={styles.checkoutSubTitleOverride} title="Details & Payment" />
                            },
                            // First Name
                            {
                                ...D_FieldModel_FirstName(),
                                overrideClass: styles.fieldOverride,
                                value: firstName,
                                onChange: (newValue) => setFirstName(newValue),
                                readOnly: session.data != null
                            },
                            // Last Name
                            {
                                ...D_FieldModel_LastName(),
                                overrideClass: styles.fieldOverride,
                                value: lastName,
                                onChange: (newValue) => setLastName(newValue),
                                readOnly: session.data != null
                            },
                            // Email
                            {
                                ...D_FieldModel_Email(),
                                overrideClass: styles.fieldOverride,
                                value: email,
                                onChange: (newValue) => setEmail(newValue),
                                readOnly: session.data != null
                            },
                            // Phone
                            {
                                ...D_FieldModel_Phone(),
                                overrideClass: styles.fieldOverride,
                                value: phone,
                                onChange: (newValue) => setPhone(newValue),
                                readOnly: !JC_Utils.stringNullOrEmpty(session.data?.user.Phone)
                            },
                            // Company
                            {
                                overrideClass: `${styles.fieldOverride} ${styles.companyFieldOverride}`,
                                inputId: "company-input",
                                type: FieldTypeEnum.Text,
                                label: "Company (optional)",
                                onChange: (newValue) => setCompany(newValue),
                                value: company,
                                readOnly: !JC_Utils.stringNullOrEmpty(session.data?.user.CompanyName)
                            },
                            // Pick-Up / Delivery Buttons
                            {
                                inputId: "pick-up-delivery-buttons",
                                type: FieldTypeEnum.Text,
                                customNode: <div key="pick-up-delivery-buttons" className={styles.pickUpDeliveryButtonsContainer}>
                                    <JC_Button
                                        overrideClass={pickUpSelected ? styles.pickUpDeliveryButtonSelectedOverride : styles.pickUpDeliveryButtonOverride}
                                        iconOverrideClass={styles.pickUpIconOverride}
                                        text="Pick Up"
                                        iconName="CkHouse"
                                        iconPosition={ButtonIconPosition.Top}
                                        onClick={() => { setPickUpSelected(true); setDeliverySelected(false); }}
                                    />
                                    <JC_Button
                                        overrideClass={deliverySelected ? styles.pickUpDeliveryButtonSelectedOverride : styles.pickUpDeliveryButtonOverride}
                                        iconOverrideClass={styles.deliveryIconOverride}
                                        text="Delivery"
                                        iconName="CkTruck"
                                        iconPosition={ButtonIconPosition.Top}
                                        onClick={() => { setPickUpSelected(false); setDeliverySelected(true); }}
                                    />
                                </div>
                            },
                            ...(pickUpSelected ? [
                                {
                                    inputId: "pick-up-delivery-buttons",
                                    type: FieldTypeEnum.Text,
                                    customNode: <JC_DatePicker
                                        fieldOverrideClass={styles.fieldOverride}
                                        inputId="test-date-input"
                                        theDate={testDate}
                                        onChange={(newDate:Date) => setTestDate(newDate)}
                                    />
                                }
                            ] : []),
                            ...(deliverySelected ? [
                                {
                                    overrideClass: `${styles.fieldOverride} ${styles.companyFieldOverride}`,
                                    inputId: "company-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Company",
                                    iconName: "User",
                                    validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a company." : ""
                                }
                            ] : [])
                        ]}
                    />

                </div>

                {/* Payment Modal */}
                <JC_Modal title="Make Payment" isOpen={paymentModalOpen} onCancel={() => setPaymentModalOpen(false)}>

                    {/* Card Details */}
                    <JC_StripeCheckout fullName={`${firstName} ${lastName}`} email={email} orderId={bag.Id} />

                </JC_Modal>

            </div>
        );
}
