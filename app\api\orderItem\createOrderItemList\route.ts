import { NextRequest, NextResponse } from "next/server";
import { OrderItemModel } from "@/app/models/OrderItem";
import { OrderItemBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const { orderItemList } = await request.json() as { orderItemList:OrderItemModel[] };
        await OrderItemBusiness.CreateList(orderItemList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}