import { NextRequest, NextResponse } from "next/server";
import { ItemIngredientModel } from "@/app/models/ItemIngredient";
import { ItemIngredientBusiness } from "./business";

export const dynamic = 'force-dynamic';

// - --- - //
// - GET - //
// - --- - //

export async function GET(request: NextRequest) {
    try {
        const productId = new URL(request.url).searchParams.get("productId");
        const productVariationId = new URL(request.url).searchParams.get("productVariationId");
        
        if (productId && !productVariationId) {
            const items = await ItemIngredientBusiness.GetForProduct(productId);
            return NextResponse.json(items);
        } else if (productVariationId) {
            const items = await ItemIngredientBusiness.GetForProductVariation(productVariationId);
            return NextResponse.json(items);
        } else {
            const items = await ItemIngredientBusiness.GetAll();
            return NextResponse.json(items);
        }
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const newItem: ItemIngredientModel = new ItemIngredientModel(await request.json());
        await ItemIngredientBusiness.Create(newItem);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - UPDATE - //
// - ------ - //

export async function POST(request: NextRequest) {
    try {
        const item: ItemIngredientModel = new ItemIngredientModel(await request.json());
        await ItemIngredientBusiness.Update(item);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - DELETE - //
// - ------ - //

export async function DELETE(request: NextRequest) {
    try {
        const id: string = new URL(request.url).searchParams.get("id")!;
        await ItemIngredientBusiness.Delete(id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
