"use client"

import React, { useState, useEffect } from "react";
import JC_LabelFront from "@/app/components/JC_LabelFront/JC_LabelFront";
import { JC_LabelPageModel } from "@/app/models/ComponentModels/JC_LabelPage";
import { LabelSizeEnum } from "@/app/enums/LabelSize";
import { JC_Utils } from "@/app/Utils";
import styles from "./JC_LabelFrontPage.module.scss";

export default function JC_LabelFrontPage(_: Readonly<JC_LabelPageModel & {
    onRenderComplete?: () => void;
}>) {

    // Determine number of labels based on label size
    const numLabels = _.labelSize === LabelSizeEnum.TwoByFive ? 10 : 8;

    // Track which labels have completed font size adjustment
    const [completedLabels, setCompletedLabels] = useState<Set<number>>(new Set());

    // Handle when a label completes font size adjustment
    const handleLabelComplete = (labelIndex: number) => {
        setCompletedLabels(prev => {
            const newSet = new Set(prev);
            newSet.add(labelIndex);
            return newSet;
        });
    };

    // Check if all labels are ready and notify parent
    useEffect(() => {
        if (completedLabels.size === numLabels && _.onRenderComplete) {
            _.onRenderComplete();
        }
    }, [completedLabels, numLabels, _.onRenderComplete]);

    // No auto-print functionality here - moved to Page_PrintFrontLabel

    // Get the appropriate class based on label size
    const getLabelSizeClass = () => {
        if (_.labelSize === LabelSizeEnum.Small) return styles.small;
        if (_.labelSize === LabelSizeEnum.TwoByFive) return styles.twoByFive;
        return "";
    };

    return (
        <div
            className={`${styles.mainContainer} ${getLabelSizeClass()} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}
            style={_.paddingStyle}
        >
            {_.product != null && _.varData != null &&
            [...Array(numLabels)].map((num, i) =>
                <JC_LabelFront
                    key={i}
                    product={_.product}
                    variations={_.variations}
                    varData={_.varData}
                    onRenderComplete={() => handleLabelComplete(i)}
                />
            )}
        </div>
    );
}
