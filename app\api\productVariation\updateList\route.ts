import { NextRequest, NextResponse } from "next/server";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const requestData = await request.json();
        const items: ProductVariationModel[] = requestData.items.map((p: any) => new ProductVariationModel(p));
        await ProductVariationBusiness.UpdateList(items);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
