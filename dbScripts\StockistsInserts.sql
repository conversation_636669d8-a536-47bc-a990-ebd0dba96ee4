
TRUNCATE TABLE public."Stockist";

-- SQL INSERT statements for Stockist table

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('1E078A89-B3EE-47F0-B189-94CA7932C3A3', 'Ellenbrook Life Balance Chiropractic & Health', '38 Ellen Stirling Parade, Ellenbrook WA 6069', -31.7808226, 115.9658767, '08 6296 6597', 'EllenbrookLifeBalance', 'https://ellenbrooklifebalance.com', 'https://www.google.com.au/maps/place/Ellenbrook+Life+Balance+Chiropractic+and+Health/@-31.7807178,115.9632781,17z/data=!3m1!4b1!4m6!3m5!1s0x2a32b560b7f44ccb:0xd08004448b37ea7!8m2!3d-31.7807178!4d115.9658584!16s%2Fg%2F11h20hpjvx?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', TRUE, 1);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('0E5DC81A-8E0A-4A5B-823F-F620C3F04F01', 'The Little Big Store Organics', '1/53 Prindiville Dr, Wangara WA 6065', -31.7907295, 115.8222716, '**********', 'LittleBigStore', 'http://www.thelittlebigstore.com.au', 'https://www.google.com.au/maps/place/The+Little+Big+Store+Organics/@-31.7907295,115.8222716,17z/data=!3m1!4b1!4m6!3m5!1s0x2a32ad093c3685b7:0x2f9f547721b43c22!8m2!3d-31.7907295!4d115.8248519!16s%2Fg%2F11bbw_pjz2?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', TRUE, 2);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('F29C550C-D871-4C1E-9830-75E013BFA536', 'Earth Wholefoods', '3/25 Delage St, Joondalup WA 6027', -31.745566, 115.7578516, '08 9301 1141', 'EarthWholefoods', 'http://earthwholefood.com.au', 'https://www.google.com.au/maps/place/Earth+Wholefoods/@-31.745566,115.7578516,17z/data=!3m1!4b1!4m6!3m5!1s0x2bcd53687101dc47:0xa7d058eafd581b84!8m2!3d-31.745566!4d115.7604319!16s%2Fg%2F11cnrdhm2d?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', TRUE, 3);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('F5592AA0-C66D-4896-8177-ADCB86F77596', 'Precious Organics (Nedlands)', '28/88 Broadway, Perth WA 6009', -31.983923, 115.8127687, '08 9386 7670', 'PreciousOrganics', 'http://www.preciousorganics.com.au', 'https://www.google.com.au/maps/place/Precious+Organics+Nedlands/@-31.983923,115.8127687,16z/data=!3m1!4b1!4m6!3m5!1s0x2a32a4f28b8fdc93:0x699aec4e3eed050f!8m2!3d-31.983923!4d115.815349!16s%2Fg%2F11byttx112?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', TRUE, 4);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('27AB95FE-B27A-4C33-A01F-023DEA44E2D4', 'Precious Organics (Willagee)', '92 Bawdan St, Willagee WA 6156', -32.050274, 115.7954904, '08 9317 7333', 'PreciousOrganics', 'http://www.preciousorganics.com.au', 'https://www.google.com.au/maps/place/Precious+Organics+Willagee/@-32.050274,115.7954904,17z/data=!3m1!4b1!4m6!3m5!1s0x2a32a30f885432b9:0xf8a0b2492d8464d5!8m2!3d-32.050274!4d115.7980707!16s%2Fg%2F12lk4hdg7?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', TRUE, 5);


INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('C2C09ECE-4BA9-4574-AF8B-13CD6CA176FE', 'Loose Produce', '171 Albany Hwy, Victoria Park WA 6100', -31.970517, 115.889053, '08 6180 7810', 'LooseProduce', 'https://www.looseproduce.com.au/', 'https://www.google.com.au/maps/place/Loose+Produce/@-31.970517,115.889053,17z/data=!3m1!4b1!4m6!3m5!1s0x2a32bb595434fc9d:0x6d74b70d703a412b!8m2!3d-31.970517!4d115.8916333!16s%2Fg%2F1tgz7s4s?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', FALSE, 6);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('B501B79F-2AD0-4EF9-A088-D7AA6E4D40C3', 'Nourish Me Wholefoods', '38 George St, East Fremantle WA 6158', -32.043894, 115.7571295, '08 6285 5597', 'NourishMeWholefoods', 'https://www.facebook.com/profile.php?id=100089835717565&mibextid=LQQJ4d', 'https://www.google.com.au/maps/place/Nourish+Me+Wholefoods/@-32.043894,115.7571295,17z/data=!3m1!4b1!4m6!3m5!1s0x2a32a37245eb3f8b:0x45c4bca61119fd6d!8m2!3d-32.043894!4d115.7597098!16s%2Fg%2F11sscytmdb?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', FALSE, 7);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('3B94C6E6-118B-460C-860E-6872BD549D38', 'The Sacred Earth Pantry', '1540 Bussell Hwy, Stratham WA 6237', -33.4686387, 115.5920626, '0413 165 685', 'SacredEarthPantry', 'https://www.facebook.com/thesacredearthpantry', 'https://www.google.com.au/maps/place/The+Sacred+Earth+Pantry/@-33.4686387,115.5920626,17z/data=!3m1!4b1!4m6!3m5!1s0x2a2e239d7e94cae9:0xfc56d9b6d883a6df!8m2!3d-33.4686387!4d115.5946429!16s%2Fg%2F11h2hbjjzw?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', FALSE, 8);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('E59A6860-4E30-41BA-9691-AA0B7705D6D0', 'Moore & Moore Cafe', '46 Henry St, Fremantle WA 6160', -32.056319, 115.7421838, '08 9335 8825', 'MoreAndMore', 'http://www.mooreandmoorecafe.com.au', 'https://www.google.com.au/maps/place/Moore+%26+Moore+Cafe/@-32.056319,115.7421838,16z/data=!3m1!4b1!4m6!3m5!1s0x2a32a1706c1f2c9b:0x7e0c4b85bcac5e66!8m2!3d-32.056319!4d115.7447641!16s%2Fg%2F1tf92dps?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', FALSE, 9);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('6E38FB6B-A242-445A-A235-E2E4504625C1', 'Raw Raw Raw', '74 South Terrace, Fremantle WA 6160', -32.0558191, 115.7472169, '0434 676 787', 'RawRawRaw', 'https://www.facebook.com/RawRawRaw111', 'https://www.google.com.au/maps/place/Raw+Raw+Raw/@-32.0558191,115.7472169,17z/data=!3m1!4b1!4m6!3m5!1s0x2a32a3f93fce1627:0x775ea3f1b753bfcf!8m2!3d-32.0558191!4d115.7497972!16s%2Fg%2F11rsq4t9kp?entry=ttu&g_ep=EgoyMDI1MDQxNi4xIKXMDSoASAFQAw%3D%3D', FALSE, 10);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('B3BC74F0-5AA9-428A-81AF-272F161941BA', 'Safety Bay Health Foods', 'shop 1/222 Safety Bay Rd, Safety Bay WA 6169', -32.3029181, 115.709055, '0400 039 337', 'SafetyBayHealthFoods', 'https://www.instagram.com/safetybayhealthfoods_', 'https://www.google.com.au/maps/place/Safety+Bay+Health+Foods/@-32.3029181,115.709055,17z/data=!3m1!4b1!4m6!3m5!1s0x2a3283be2eaff615:0x4c0e40d6d75647bd!8m2!3d-32.3029181!4d115.7116353!16s%2Fg%2F11gy2hpwgc?entry=ttu&g_ep=EgoyMDI1MDQyMC4wIKXMDSoASAFQAw%3D%3D', FALSE, 11);

INSERT INTO "Stockist" ("Id", "Name", "AddressString", "AddressLat", "AddressLong", "Phone", "LogoImageName", "SiteUrl", "MapsUrl", "IsFullRange", "SortOrder")
VALUES ('6BBA723E-FB9D-4D56-B2CE-A9120A2AA98B', 'Little Hawk Freo', '14 Strang St, Beaconsfield WA 6162', -32.0729684, 115.7593739, '0403 989 237', 'LittleHawkFreo', 'https://www.littlehawkfreo.com.au/', 'https://maps.app.goo.gl/my5iNjnqL883kceS7', FALSE, 12);