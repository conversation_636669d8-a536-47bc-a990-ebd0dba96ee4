@import '../../global';

$leftGap: 10px;

.mainContainer {
    width: max-content;
    display: flex;
    align-items: center;
    column-gap: 12px;
    user-select: none;
    &.clickable {
        &, .checkbox, label {
            cursor: pointer !important;
        }
    }

    .checkbox {
        width: 14px;
        height: 14px;
        outline: solid $tinyBorderWidth $offBlack;
        border-radius: 1px;
        position: relative;

        .innerCheckedSquare {
            position: absolute;
            left: 50%; top: 50%; transform: translate(-50%, -50%);
            z-index: 999;
            width: 60%;
            height: 60%;
            border-radius: 1px;
            background-color: $primaryColor;
        }
    }
}