-- SQL script to migrate "(no option)" variations to Product-level ItemSettings and ItemIngredients
-- This script:
-- 1. Finds all "(no option)" variations
-- 2. Creates ItemSettings records for the corresponding Products (if they don't exist)
-- 3. Creates ItemIngredients records for the corresponding Products (migrating from variation)
-- 4. Deletes the "(no option)" variations

-- Start a transaction to ensure all operations succeed or fail together
BEGIN;

-- Create a temporary table to store the variations we need to process
CREATE TEMP TABLE temp_no_option_variations AS
SELECT
    v."Id" AS variation_id,
    v."ProductId" AS product_id,
    v."Name" AS variation_name
FROM
    public."ProductVariation" v
WHERE
    v."Name" = '(no option)'
    AND v."Deleted" = FALSE;

-- Log the number of variations we're processing
DO $$
DECLARE
    variation_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO variation_count FROM temp_no_option_variations;
    RAISE NOTICE 'Processing % "(no option)" variations', variation_count;
END $$;

-- 1. Migrate ItemSettings from variations to products
-- First, identify which products already have ItemSettings
CREATE TEMP TABLE temp_products_with_settings AS
SELECT DISTINCT "ProductId"
FROM public."ItemSettings"
WHERE "ProductId" IS NOT NULL AND "Deleted" = FALSE;

-- Insert ItemSettings for products that don't have them yet
INSERT INTO public."ItemSettings" (
    "Id",
    "ProductId",
    "ProductVariationId",
    "ServingSizeGrams",
    "ServingsPerPack",
    "WeightChange",
    "LabelSize",
    "PackWeightText",
    "FrontLabelNameOverride",
    "FrontLabelNameXOffset",
    "BackLabelNameOverride",
    "IngredientsFontSize",
    "IngredientsYOffset",
    "InstructionsText",
    "InstructionsFontSize",
    "NoteText",
    "NoteFontSize",
    "AllergensText",
    "FreeFromText",
    "BiodegOverrideText",
    "StoreFrozenOverride",
    "IconName",
    "IconX",
    "IconY",
    "IconSize",
    "NutValsEnabled",
    "CreatedAt"
)
SELECT
    gen_random_uuid() AS "Id",
    nov.product_id AS "ProductId",
    NULL AS "ProductVariationId",
    COALESCE(vs."ServingSizeGrams", 0) AS "ServingSizeGrams",
    COALESCE(vs."ServingsPerPack", 0) AS "ServingsPerPack",
    COALESCE(vs."WeightChange", 1) AS "WeightChange",
    COALESCE(vs."LabelSize", 'Large') AS "LabelSize",
    vs."PackWeightText",
    vs."FrontLabelNameOverride",
    vs."FrontLabelNameXOffset",
    vs."BackLabelNameOverride",
    vs."IngredientsFontSize",
    vs."IngredientsYOffset",
    vs."InstructionsText",
    vs."InstructionsFontSize",
    vs."NoteText",
    vs."NoteFontSize",
    vs."AllergensText",
    vs."FreeFromText",
    vs."BiodegOverrideText",
    vs."StoreFrozenOverride",
    vs."IconName",
    vs."IconX",
    vs."IconY",
    vs."IconSize",
    COALESCE(vs."NutValsEnabled", FALSE) AS "NutValsEnabled",
    NOW() AS "CreatedAt"
FROM
    temp_no_option_variations nov
LEFT JOIN
    public."ItemSettings" vs ON vs."ProductVariationId" = nov.variation_id AND vs."Deleted" = FALSE
LEFT JOIN
    temp_products_with_settings ps ON ps."ProductId" = nov.product_id
WHERE
    ps."ProductId" IS NULL; -- Only for products that don't already have settings

-- 2. Migrate ItemIngredients from variations to products
-- First, identify which product-ingredient combinations already exist
CREATE TEMP TABLE temp_existing_product_ingredients AS
SELECT "ProductId", "IngredientId"
FROM public."ItemIngredient"
WHERE "ProductId" IS NOT NULL AND "Deleted" = FALSE;

-- Insert ItemIngredients for products, migrating from variations
INSERT INTO public."ItemIngredient" (
    "Id",
    "ProductId",
    "ProductVariationId",
    "IngredientId",
    "AmountGrams",
    "ShowPercent",
    "CreatedAt"
)
SELECT
    gen_random_uuid() AS "Id",
    nov.product_id AS "ProductId",
    NULL AS "ProductVariationId",
    vi."IngredientId",
    vi."AmountGrams",
    vi."ShowPercent",
    NOW() AS "CreatedAt"
FROM
    temp_no_option_variations nov
JOIN
    public."ItemIngredient" vi ON vi."ProductVariationId" = nov.variation_id AND vi."Deleted" = FALSE
LEFT JOIN
    temp_existing_product_ingredients epi ON epi."ProductId" = nov.product_id AND epi."IngredientId" = vi."IngredientId"
WHERE
    epi."ProductId" IS NULL; -- Only for product-ingredient combinations that don't already exist

-- 3. Update any ProductVariationsData records that reference these variations
-- First, find all ProductVariationsData records that include these variations
CREATE TEMP TABLE temp_pvd_to_update AS
SELECT
    pvd."Id" AS pvd_id,
    pvd."ProductVariationIds" AS old_variation_ids,
    nov.variation_id
FROM
    public."ProductVariationsData" pvd
JOIN
    temp_no_option_variations nov ON nov.variation_id::text = ANY(pvd."ProductVariationIds")
WHERE
    pvd."Deleted" = FALSE;

-- Update ProductVariationsData records to remove the "(no option)" variation IDs
-- If the array becomes empty, set it to NULL to indicate it's just for the Product
UPDATE public."ProductVariationsData" pvd
SET
    "ProductVariationIds" = CASE
        WHEN array_length(array_remove(pvd."ProductVariationIds", tu.variation_id::text), 1) IS NULL
        THEN NULL
        ELSE array_remove(pvd."ProductVariationIds", tu.variation_id::text)
    END,
    "ModifiedAt" = NOW()
FROM
    temp_pvd_to_update tu
WHERE
    pvd."Id" = tu.pvd_id;

-- 4. Finally, mark the "(no option)" variations as deleted
UPDATE public."ProductVariation"
SET
    "Deleted" = TRUE,
    "ModifiedAt" = NOW()
FROM
    temp_no_option_variations nov
WHERE
    "ProductVariation"."Id" = nov.variation_id;

-- Log the results
DO $$
DECLARE
    settings_count INTEGER;
    ingredients_count INTEGER;
    pvd_count INTEGER;
    deleted_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO settings_count FROM public."ItemSettings" WHERE "CreatedAt" > NOW() - INTERVAL '1 minute';
    SELECT COUNT(*) INTO ingredients_count FROM public."ItemIngredient" WHERE "CreatedAt" > NOW() - INTERVAL '1 minute';
    SELECT COUNT(*) INTO pvd_count FROM temp_pvd_to_update;
    SELECT COUNT(*) INTO deleted_count FROM temp_no_option_variations;

    RAISE NOTICE 'Migration complete:';
    RAISE NOTICE '- Created % new ItemSettings records', settings_count;
    RAISE NOTICE '- Created % new ItemIngredient records', ingredients_count;
    RAISE NOTICE '- Updated % ProductVariationsData records', pvd_count;
    RAISE NOTICE '- Deleted % "(no option)" variations', deleted_count;
END $$;

-- Clean up temporary tables
DROP TABLE temp_no_option_variations;
DROP TABLE temp_products_with_settings;
DROP TABLE temp_existing_product_ingredients;
DROP TABLE temp_pvd_to_update;

-- Commit the transaction
COMMIT;
