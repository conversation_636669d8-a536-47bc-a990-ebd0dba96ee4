"use client"

import styles from "./JC_Field.module.scss";
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { Color } from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import { EditorProvider, useCurrentEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { JC_FieldModel } from '@/app/models/ComponentModels/JC_Field';
import { FieldTypeEnum } from '@/app/enums/FieldType';
import { JC_Utils } from "@/app/Utils";
import JC_ColorPicker from "../JC_ColorPicker/JC_ColorPicker";
import JC_Dropdown from "../JC_Dropdown/JC_Dropdown";
import JC_Radio from "../JC_Radio/JC_Radio";
import { DropdownTypeEnum } from "@/app/enums/DropdownType";

// Format on change
function formatNumberInputChange(event:any) : string {
    let inputString = event.target.value;
    // Only allow numbers and .'s
    if (!['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.'].includes(inputString.slice(-1))) {
        inputString = inputString.slice(0, inputString.length-1);
    }
    // Only allow 2dp
    if (inputString.indexOf('.') > -1) {
        inputString = inputString.substring(0, inputString.indexOf('.') + 3);
    }
    return inputString;
}

// Format on blur
function formatNumberInputBlur(event:any, defaultValue:number, onChange?:(newValue:string) => void) : number {
    let inputString = event.target.value;
    // Remove . at end
    if (inputString.slice(-1) == '.') {
        inputString = inputString.slice(0, inputString.length-1);
    }
    // IF two .'s, only keep 1st one and remove rest of string after 2nd .
    if (inputString.split('.').length > 2) {
        inputString = inputString.split('.')[0] + '.' + inputString.split('.')[1];
    }
    if (JC_Utils.stringNullOrEmpty(inputString) && defaultValue != null) {
        onChange != null && onChange(String(defaultValue));
        return defaultValue;
    } else {
        return +inputString;
    }
}


// NumberStepper component
interface NumberStepperFieldProps {
    value: string | number;
    setValue: (value: string | number) => void;
    onChange?: (value: string) => void;
    increment: number;
    inverted?: boolean;
    inputId: string;
    disabled?: boolean;
    minValue?: number;
    maxValue?: number;
}

const NumberStepperField = ({ value, setValue, onChange, increment, inverted = false, inputId, disabled = false, minValue, maxValue, autoFocus }: NumberStepperFieldProps & { autoFocus?: boolean }) => {
    // Function to increment value
    const incrementValue = () => {
        const currentValue = Number(value);
        const newValue = currentValue + increment;

        // Check if the new value exceeds the maximum
        if (maxValue !== undefined && newValue > maxValue) {
            return; // Don't update if exceeding max
        }

        setValue(newValue);
        onChange && onChange(newValue.toString());
    };

    // Function to decrement value
    const decrementValue = () => {
        const currentValue = Number(value);
        const newValue = currentValue - increment;

        // Check if the new value is below the minimum
        if (minValue !== undefined && newValue < minValue) {
            return; // Don't update if below min
        }

        setValue(newValue);
        onChange && onChange(newValue.toString());
    };

    // If inverted, swap the functions
    const upButtonClick = inverted ? decrementValue : incrementValue;
    const downButtonClick = inverted ? incrementValue : decrementValue;

    return (
        <>
            <div className={styles.inputContainer}>
                <input
                    type="text"
                    placeholder=""
                    value={value}
                    onChange={(e) => {
                        const newValue = e.target.value;
                        setValue(newValue);
                        onChange && onChange(newValue);
                    }}
                    onBlur={() => {
                        // If needed, add blur handling here
                    }}
                    onKeyDown={(event) => {
                        // Handle Enter key for incrementing and decrementing
                        if (event.key === 'ArrowUp') {
                            inverted ? decrementValue() : incrementValue();
                            event.preventDefault();
                        } else if (event.key === 'ArrowDown') {
                            inverted ? incrementValue() : decrementValue();
                            event.preventDefault();
                        }
                    }}
                    disabled={disabled}
                    id={inputId}
                    autoFocus={autoFocus}
                />
            </div>
            <div className={styles.stepperButtons}>
                <div
                    className={`${styles.stepButton} ${styles.upButton}`}
                    onClick={upButtonClick}
                >
                    <Image
                        src="/icons/Chevron.webp"
                        width={0}
                        height={0}
                        alt="Up"
                        style={{ transform: 'rotate(180deg)' }}
                        unoptimized
                    />
                </div>
                <div
                    className={styles.stepButton}
                    onClick={downButtonClick}
                >
                    <Image
                        src="/icons/Chevron.webp"
                        width={0}
                        height={0}
                        alt="Down"
                        unoptimized
                    />
                </div>
            </div>
        </>
    );
};

// NumberStepperArrowsOnly component
const NumberStepperArrowsOnlyField = ({ value, setValue, onChange, increment, inverted = false, minValue, maxValue }: NumberStepperFieldProps) => {
    // Function to increment value
    const incrementValue = () => {
        const currentValue = Number(value);
        const newValue = currentValue + increment;

        // Check if the new value exceeds the maximum
        if (maxValue !== undefined && newValue > maxValue) {
            return; // Don't update if exceeding max
        }

        setValue(newValue);
        onChange && onChange(newValue.toString());
    };

    // Function to decrement value
    const decrementValue = () => {
        const currentValue = Number(value);
        const newValue = currentValue - increment;

        // Check if the new value is below the minimum
        if (minValue !== undefined && newValue < minValue) {
            return; // Don't update if below min
        }

        setValue(newValue);
        onChange && onChange(newValue.toString());
    };

    // If inverted, swap the functions
    const upButtonClick = inverted ? decrementValue : incrementValue;
    const downButtonClick = inverted ? incrementValue : decrementValue;

    return (
        <div className={styles.stepperButtons}>
            <div
                className={`${styles.stepButton} ${styles.upButton}`}
                onClick={upButtonClick}
            >
                <Image
                    src="/icons/Chevron.webp"
                    width={0}
                    height={0}
                    alt="Up"
                    style={{ transform: 'rotate(180deg)' }}
                    unoptimized
                />
            </div>
            <div
                className={styles.stepButton}
                onClick={downButtonClick}
            >
                <Image
                    src="/icons/Chevron.webp"
                    width={0}
                    height={0}
                    alt="Down"
                    unoptimized
                />
            </div>
        </div>
    );
};

// Color picker
let colorPickerOpen = false;


export default function JC_Field(_: Readonly<JC_FieldModel>) {

    // - STATE - //

    const [thisValue, setThisValue] = useState<string | number>(_.value ?? "");
    const [showColorPicker, setShowColorPicker] = useState<boolean>(false);

    // Combine direct richText props with richTextOptions if they exist
    const combinedRichTextOptions = _.type === FieldTypeEnum.RichText ? {
        ..._.richTextOptions,
        enableColor: _.richTextEnableColor !== undefined ? _.richTextEnableColor : _.richTextOptions?.enableColor,
        enableBold: _.richTextEnableBold !== undefined ? _.richTextEnableBold : _.richTextOptions?.enableBold,
        enableItalic: _.richTextEnableItalic !== undefined ? _.richTextEnableItalic : _.richTextOptions?.enableItalic,
        enableDegree: _.richTextEnableDegree !== undefined ? _.richTextEnableDegree : _.richTextOptions?.enableDegree,
        width: _.richTextWidth !== undefined ? _.richTextWidth : _.richTextOptions?.width,
        height: _.richTextHeight !== undefined ? _.richTextHeight : _.richTextOptions?.height,
    } : undefined;


    // - STYLES - //

    let inputStyle = '';
    switch(_.type) {
        case FieldTypeEnum.Text:                    inputStyle = styles.textType;                break;
        case FieldTypeEnum.Email:                   inputStyle = styles.textType;                break;
        case FieldTypeEnum.Number:                  inputStyle = styles.numberType;              break;
        case FieldTypeEnum.NumberStepper:           inputStyle = styles.numberStepper;           break;
        case FieldTypeEnum.NumberStepperArrowsOnly: inputStyle = styles.numberStepperArrowsOnly; break;
        case FieldTypeEnum.Password:                inputStyle = styles.passwordType;            break;
        case FieldTypeEnum.Textarea:                inputStyle = styles.textareaType;            break;
        case FieldTypeEnum.RichText:                inputStyle = styles.richTextType;            break;
        case FieldTypeEnum.Radio:                   inputStyle = styles.radioType;               break;
    }


    // - HANDLES - //

    // onChange
    function handleOnChange(event:any) {
        if (_.type == FieldTypeEnum.Number) {
            event.target.value = formatNumberInputChange(event);
        }
        setThisValue(event.target.value);
        _.onChange != null && _.onChange(event.target.value);
    }

    // onBlur
    function handleOnBlur(event:any) {
        if (_.type == FieldTypeEnum.Number) {
            event.target.value = formatNumberInputBlur(event, _.defaultValue as number, _.onChange)
        }
        _.onBlur != null && _.onBlur(event.target.value);
    }

    // onKeyDown
    function handleKeyDown(event:any) {
        if (event.code == "Enter" && _.onEnter != null)
            _.onEnter(event);
        if (event.code == "Escape" && _.onEscape != null)
            _.onEscape(event);
    }


    // - BUILD - //

    // Rich Text Buttons
    const MenuBar = () => {
        const { editor } = useCurrentEditor()

        if (!editor) {
            return <div className={styles.richTextButtons}></div>;
        }

        // Determine if features are enabled using combinedRichTextOptions
        const enableColor = combinedRichTextOptions?.enableColor ?? false;
        const enableBold = combinedRichTextOptions?.enableBold ?? false;
        const enableItalic = combinedRichTextOptions?.enableItalic ?? false;
        const enableDegree = combinedRichTextOptions?.enableDegree ?? false;

        // Handle color change function
        const handleColorChange = (color: string) => {
            // Apply the color to the selection
            editor.chain().focus().setColor(color).run();

            // Apply the change to the editor and trigger the onChange
            if (_.onChange) {
                _.onChange(editor.getHTML());
            }
        };

        // Create color picker component
        const colorPicker = showColorPicker ? (
            <JC_ColorPicker
                value={editor.getAttributes('textStyle').color || '#000000'}
                onChange={handleColorChange}
                onClose={() => {
                    // Color is added to history when picker closes
                    setShowColorPicker(false);
                    colorPickerOpen = false;
                }}
            />
        ) : null;

        return (
            <div className={styles.richTextButtons}>
                {enableColor && (
                <div className={styles.colorPickerWrapper}>
                    {/* Color button */}
                    <button
                        className={styles.colorButton}
                        style={{
                            backgroundColor: editor.getAttributes('textStyle').color || '#000000',
                            border: '1px solid #303030'
                        }}
                        onMouseDown={(e) => {
                            e.preventDefault(); // Prevent editor from losing focus
                            setShowColorPicker(!showColorPicker);
                            colorPickerOpen = true;
                        }}
                    />

                    {/* Custom color picker */}
                    {colorPicker}
                </div>)}
                {enableBold &&
                <button
                    className={editor.isActive('bold') ? styles.isActive : ''}
                    onMouseDown={(e) => {
                        e.preventDefault(); // Prevent editor from losing focus
                        editor.chain().focus().toggleBold().run();
                    }}
                    disabled={!editor.can().chain().focus().toggleBold().run()}
                >
                    B
                </button>}
                {enableItalic &&
                <button
                    className={editor.isActive('italic') ? styles.isActive : ''}
                    onMouseDown={(e) => {
                        e.preventDefault(); // Prevent editor from losing focus
                        editor.chain().focus().toggleItalic().run();
                    }}
                    disabled={!editor.can().chain().focus().toggleItalic().run()}
                >
                    <i>I</i>
                </button>}
                {enableDegree &&
                <button
                    onMouseDown={(e) => {
                        e.preventDefault(); // Prevent editor from losing focus
                        editor.chain().focus().insertContent('°').run();
                    }}
                >
                    °
                </button>}
                {/* <button
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={!editor.can().chain().focus().undo().run()}
                >
                    Undo
                </button>
                <button
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={!editor.can().chain().focus().redo().run()}
                >
                    Redo
                </button> */}
            </div>
        );
    }


    // - MAIN - //

    return (
        <div className={`${styles.mainContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            {/* Label */}
            {!JC_Utils.stringNullOrEmpty(_.label) &&
            <div className={`${styles.label} ${_.type === FieldTypeEnum.NumberStepper ? styles.numberStepperLabel : ''}`}>
                {_.label}
                {_.validate != null && !JC_Utils.stringNullOrEmpty(_.validate(_.value)) && <span className={styles.errorSpan}>{_.validate(_.value)}</span>}
            </div>}

            {/* Input */}
            <div
                className={`
                    ${styles.inputContainer} ${inputStyle}
                    ${_.readOnly ? styles.readOnly : ''}
                    ${!JC_Utils.stringNullOrEmpty(_.inputOverrideClass) ? _.inputOverrideClass : ''}
                `}
                onClick={_.onClick}
                style={_.type === FieldTypeEnum.RichText && combinedRichTextOptions ? {
                    width: combinedRichTextOptions.width ? `${combinedRichTextOptions.width}px` : undefined,
                    height: combinedRichTextOptions.height ? `${combinedRichTextOptions.height}px` : undefined
                } : undefined}
            >

                {_.type == FieldTypeEnum.RichText
                    ?
                    <EditorProvider
                        slotBefore={<MenuBar />}
                        extensions={[StarterKit.configure(), Color, TextStyle]}
                        content={_.value?.toString()}
                        onBlur={(props) => {
                            // Always update on blur to ensure changes are saved
                            _.onChange!(props.editor.getHTML());
                            if (colorPickerOpen) {
                                colorPickerOpen = false;
                            }
                        }}
                        onUpdate={(props:any) => _.onChange!(props.editor.getHTML())}
                        editorProps={{
                            attributes: {
                                ...(combinedRichTextOptions?.height || combinedRichTextOptions?.width ? {
                                    style: `${combinedRichTextOptions?.height ? `height: ${combinedRichTextOptions.height}px;` : ''}
                                           ${combinedRichTextOptions?.width ? `width: ${combinedRichTextOptions.width}px;` : ''}`.trim()
                                } : {})
                            }
                        }}
                    />
                    :
                    _.type == FieldTypeEnum.Textarea
                        ?
                        <textarea
                            defaultValue={_.value}
                            onChange={handleOnChange}
                            onBlur={handleOnBlur}
                            disabled={_.readOnly}
                            id={_.inputId}
                            autoFocus={_.autoFocus}
                        />
                        :
                        _.type == FieldTypeEnum.NumberStepper
                        ?
                        <NumberStepperField
                            value={thisValue}
                            setValue={setThisValue}
                            onChange={_.onChange}
                            increment={_.numberStepperOptions?.increment || 1}
                            inverted={_.numberStepperOptions?.inverted}
                            inputId={_.inputId}
                            disabled={_.readOnly}
                            minValue={_.numberStepperOptions?.minValue}
                            maxValue={_.numberStepperOptions?.maxValue}
                            autoFocus={_.autoFocus}
                        />
                        :
                        _.type == FieldTypeEnum.NumberStepperArrowsOnly
                        ?
                        <NumberStepperArrowsOnlyField
                            value={thisValue}
                            setValue={setThisValue}
                            onChange={_.onChange}
                            increment={_.numberStepperOptions?.increment || 1}
                            inverted={_.numberStepperOptions?.inverted}
                            inputId={_.inputId}
                            disabled={_.readOnly}
                            minValue={_.numberStepperOptions?.minValue}
                            maxValue={_.numberStepperOptions?.maxValue}
                        />
                        :
                        _.type == FieldTypeEnum.Dropdown
                        ?
                        <JC_Dropdown
                            type={DropdownTypeEnum.Default}
                            placeholder="Select an option"
                            options={_.options || []}
                            selectedOptionId={thisValue as string}
                            enableSearch={_.enableSearch !== undefined ? _.enableSearch : false}
                            onSelection={(newValue) => {
                                console.log("JC_Field - onSelection called with newValue:", newValue, "for field:", _.inputId);
                                // Update local state first
                                setThisValue(newValue);
                                // Then call parent onChange handler immediately
                                if (_.onChange) {
                                    console.log("JC_Field - calling parent onChange with newValue:", newValue);
                                    _.onChange(newValue);
                                }
                            }}
                            validate={_.validate}
                        />
                        :
                        _.type == FieldTypeEnum.Radio
                        ?
                        <JC_Radio
                            options={_.options || []}
                            selectedOptionId={thisValue as string}
                            onSelection={(newValue) => {
                                console.log(thisValue);
                                // Update local state first
                                setThisValue(newValue);
                                // Then call parent onChange handler immediately
                                if (_.onChange) {
                                    _.onChange(newValue);
                                }
                            }}
                            validate={_.validate}
                        />
                        :
                        <input
                            type={ _.type == FieldTypeEnum.Email    ? "email"
                                :  _.type == FieldTypeEnum.Password ? "password"
                                :                                     "text"}
                            placeholder={_.placeholder}
                            value={_.readOnly ? _.value : thisValue}
                            onChange={handleOnChange}
                            onBlur={handleOnBlur}
                            onKeyDown={(event) => handleKeyDown(event)}
                            disabled={_.readOnly}
                            id={_.inputId}
                            autoFocus={_.autoFocus}
                        />}

            </div>

        </div>
    );
}