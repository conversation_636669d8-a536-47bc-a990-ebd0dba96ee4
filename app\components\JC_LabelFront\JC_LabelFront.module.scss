@import '../../global';

.mainContainer {
    position: relative;
    width: 385px;
    height: 250px;
    box-sizing: border-box;
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    background-color: white;
    overflow: hidden;
    // outline: solid 1px green;

    .dietaryContainer {
        position: absolute;
        top: 12px;
        left: 7px;
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        row-gap: 4px;
        font-size: 11px;
        font-weight: bold;
        color: $secondaryColor;
    }

    .logo {
        margin-top: -10px;
        width: 220px;
        height: auto;
    }

    .kangaroo {
        position: absolute;
        top: 10px;
        right: 24px;
        width: auto;
        height: 75px;
    }

    .title {
        position: relative;
        display: flex !important;
        justify-content: center;
        align-items: flex-end;
        column-gap: 5px;
        font-weight: bold;
        text-align: center;
        .titleIcon {
            position: absolute;
            transform: translateX(-100%);
            height: auto;
        }
        .packWeightText {
            transform: scale(0.7);
            transform-origin: left bottom;
            position: relative;
            display: inline-block;
            vertical-align: bottom;
            bottom: 0.08em;
        }
        p { margin: 0; } // Make sure override text's 'p' does not have margin
    }

    .bodyText {
        padding: 0 2px;
        width: 100%;
        display: flex;
        text-align: left;
        box-sizing: border-box;
        line-height: 130%;
        > p {
            margin: 0;
            margin-left: 3px;
        }
        .organicStar {
            color: $miscellaneousColor2;
            font-weight: bold;
        }
    }

    .containsOrganicText {
        margin-top: -3px;
        color: $miscellaneousColor2;
        font-size: 10px;
        font-weight: bold;
    }

    .allergenAdviceText {
        font-weight: bold;
    }

    .freeFromText {
        font-size: 12px;
        font-weight: bold;
    }

    .storeFrozenText {
        margin-bottom: 5px;
        text-align: right;
        margin-left: -120px;
        color: $primaryColor;
        font-weight: bold;
        p { margin: 0; } // Make sure override text's 'p' does not have margin
    }
}


// --------- //
// - SMALL - //
// --------- //

.mainContainer.small {
    width: 350px;
    height: 240px;
    padding: 0 6px 15px 6px;

    .dietaryContainer {
        top: 7px;
        row-gap: 3px;
        font-size: 10px;
    }

    .logo {
        width: 200px;
    }

    .title {
        // Icon size is now controlled by inline style
    }

    .containsOrganicText {
        font-size: 9px;
    }

    .freeFromText {
        font-size: 11px;
    }

    .storeFrozenText {
        font-size: 10px;
    }
}


// ------- //
// - 2x5 - //
// ------- //

.mainContainer.twoByFive {
    width: 350px;
    height: 205px;
    padding: 0 4px 10px 4px;

    .dietaryContainer {
        top: 1px;
        row-gap: 3px;
        font-size: 9px;
    }

    .logo {
        width: 180px;
    }

    .title {
        // Icon size is now controlled by inline style
    }

    .kangaroo {
        height: 70px;
    }

    .freeFromText {
        font-size: 10px;
    }

    .storeFrozenText {
        font-size: 9px;
    }
}