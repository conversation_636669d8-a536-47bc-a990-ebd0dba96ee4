@import '../global';

$mainImageSize: 445px;

.mainContainer {
    @include mainPageStyles;

    // Back Button
    .backButton {
        position: absolute;
        top: 30px;
        width: max-content;
    }

    // Body Container
    .bodyContainer {
        display: flex;
        justify-content: center;
        column-gap: 20px;

        // Images + Nut Vals
        .imagesNutValsContainer {
            width: max-content;
            height: 790px;
            display: flex;
            justify-items: center;
            gap: 20px;

            // Small Images
            .smallImagesList {
                margin-top: -6px;
                height: 100%;
                width: min-content;
                text-align: center;
                overflow-y: auto;
                padding: 4px;

                .smallImage {
                    margin-top: 5px;
                    width: $smallImageWidth;
                    height: $smallImageWidth; // Same as width so is square
                    object-fit: cover;
                    border-radius: $tinyBorderRadius;
                    cursor: pointer;
                    &:hover, &.hovering {
                        outline: solid $smallBorderWidth $secondaryColor;
                    }
                    &.selected {
                        outline: solid $smallBorderWidth $primaryColor;
                        cursor: default;
                    }
                }
            }

            // Main Image + Nut Vals Container
            .mainImageNutValsContainer {
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                // Main Image
                .mainImageContainer {

                    .mainImage {
                        position: relative;
                        width: $mainImageSize;
                        height: $mainImageSize;
                        object-fit: cover;
                        box-sizing: border-box;
                        border: solid $smallBorderWidth $offBlack;
                        border-radius: $tinyBorderRadius;
                        z-index: 91;
                        transition: transform 0.2s ease-out;
                    }

                    .darkenDiv {
                        @include outsideClickDiv;
                        opacity: 0;
                        pointer-events: none;
                    }
                }

                // Servings
                .servings {
                    margin-top: 5px;
                    margin-bottom: 9px;
                    padding: 8px 12px;
                    border: solid $smallBorderWidth $offBlack;
                    border-radius: $tinyBorderRadius;
                    background-color: $greyHover;
                    font-size: 14px;
                    line-height: 130%;
                }

                // Nutritional Values
                .nutValsContainer {
                    width: $mainImageSize;
                    height: 320px;
                    padding: 6px 15px;
                    display: flex;
                    flex-direction: column;
                    box-sizing: border-box;
                    border: solid $smallBorderWidth $offBlack;
                    border-radius: $tinyBorderRadius;
                    background-color: $greyHover;
                    font-size: 14px;
                    line-height: 18px;
                    position: relative;
                    user-select: none;

                    .headingsContainer {
                        width: 100%;
                        display: grid;
                        grid-template-columns: 1fr 1fr 1fr;
                        text-align: center;
                        font-weight: bold;
                        padding-bottom: 4px;
                        position: relative;
                    }

                    .nutVals{
                        flex-grow: 1;
                        display: grid;
                        grid-template-columns: 30% 20% 10% 23.5% 1fr;
                        align-content: space-between;
                        align-items: center;
                        position: relative;
                        > div:nth-child(5n-4) { text-align: left; font-weight: bold;  }
                        > div:nth-child(5n-3) { text-align: right; padding-right: 3px; }
                        > div:nth-child(5n-2) { text-align: left;  }
                        > div:nth-child(5n-1) { text-align: right; padding-right: 3px; }
                        > div:nth-child(5n-0) { text-align: left;  }
                    }

                    // Apply darkening to the entire container
                    &.darkened {
                        position: relative;

                        &:after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background-color: rgba(0, 0, 0, 0.2);
                            pointer-events: none;
                            z-index: 1;
                            border-radius: calc($tinyBorderRadius - 1px);
                        }
                    }

                    .notAvailableOverlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 20;
                        pointer-events: none;

                        .notAvailableText {
                            background-color: $greyHover;
                            color: $offBlack;
                            padding: 15px 30px;
                            border-radius: $largeBorderRadius;
                            font-size: 18px;
                            font-weight: bold;
                            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
                        }
                    }
                }
            }

        }

        // Selections + Add To Cart
        .selectionsContainer {
            width: 230px;
            height: max-content;
            padding: 30px;
            display: flex;
            flex-direction: column;
            row-gap: 30px;
            border-radius: $smallBorderRadius;
            border: solid $smallBorderWidth $offBlack;
            background-color: $lightGrey;

            // Variation Dropdown
            .variationDropdown {
                width: 100%;
            }

            // Price / Unit
            .pricePerUnit {
                // font-weight: bold;
                font-size: 20px;
                .discountPrice {
                    text-decoration: line-through;
                }
            }

            // Quantity
            .quantityContainer {
                padding: 14px 5px;
                display: flex;
                font-size: 20px;
                font-weight: bold;
                border-top: solid $smallBorderWidth $offBlack;
                border-bottom: solid $smallBorderWidth $offBlack;

                .quantityButton {
                    flex-shrink: 0;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    border: solid $smallBorderWidth $offBlack;
                    text-align: center;
                    font-size: 16px;
                    cursor: pointer;
                    user-select: none;
                }
                .quantityTake:hover { border-color: $primaryColor; }
                .quantityAdd:hover  { border-color: $secondaryColor; }

                .quantityNum {
                    width: 35px;
                    text-align: center;
                }

                .quantityTotal {
                    margin-left: 16px;
                    width: 80px;
                }
            }

            // Add To Bag Button
            .addToBagButtonOverride {
                width: 154px; // So spinner is centered with button
            }
        }
    }
}

// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .backButton {
        left: 38px !important;
    }
    .titleOverride {
        margin-top: 42px !important;
    }
}

@media (max-width: $smallScreenSize) {
    .bodyContainer {
        flex-direction: column;
        align-items: center;
        row-gap: 30px;
        .selectionsContainer {
            order: -1;
            margin-top: -20px;
            align-items: center;
            .quantityContainer {
                width: 100%;
                box-sizing: border-box;
                justify-content: center;
            }
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .smallImagesList {
        display: none;
    }
}