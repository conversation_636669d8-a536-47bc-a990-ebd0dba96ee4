import { Resend } from "resend";
import Template_ContactEmail from '@/templates/email/Contact';
import Template_OrderSubmittedCkEmail, { OrderSubmittedCkEmailModel } from "@/templates/email/OrderSubmittedCk";
import Template_OrderSubmittedUserEmail from "@/templates/email/OrderSubmittedUser";
import Template_ResetPassswordEmail from "@/templates/email/ResetPassword";
import Template_UserVerificationEmail from "@/templates/email/UserVerification";
import Template_WelcomeEmail from "@/templates/email/Welcome";
import { ContactModel } from "@/app/models/Contact";

export class EmailBusiness {

    // Contact
    static async SendContactEmail(emailData:ContactModel) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: 'Casella Kitchen <<EMAIL>>',
            to: '<EMAIL>',
            subject: 'TEST MATE',
            react: Template_ContactEmail(emailData)
        });
    }

    // Welcome
    static async SendWelcomeEmail(name:string, email:string) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: 'Casella Kitchen <<EMAIL>>',
            to: email,
            subject: 'TEST MATE',
            react: Template_WelcomeEmail(name, email)
        });
    }

    // Order Submitted CK
    static async SendOrderSubmittedCkEmail(emailData:OrderSubmittedCkEmailModel) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: 'Casella Kitchen <<EMAIL>>',
            // to: '<EMAIL>',
            to: '<EMAIL>',
            cc: '<EMAIL>',
            subject: `Order Submitted (${emailData.fullName})`,
            react: Template_OrderSubmittedCkEmail(emailData)
        });
    }

    // Order Submitted User
    static async SendOrderSubmittedUserEmail(emailData:OrderSubmittedCkEmailModel) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: 'Casella Kitchen <<EMAIL>>',
            to: emailData.email,
            subject: 'Order Submitted',
            react: Template_OrderSubmittedUserEmail(emailData)
        });
    }

    // Reset Password
    static async SendResetPasswordEmail(email:string, theToken:string) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: 'Casella Kitchen <<EMAIL>>',
            to: email,
            subject: 'CK Reset Password',
            react: Template_ResetPassswordEmail({ token: theToken })
        });
    }

    // User Verification
    static async SendUserVerificationEmail(email:string, theToken:string) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: 'Casella Kitchen <<EMAIL>>',
            to: email,
            subject: 'Casella Kitchen Verification',
            react: Template_UserVerificationEmail({ token: theToken })
        });
    }

}