import { _Base } from "./_Base";
import { ProductModel } from "./Product";
import { ProductVariationModel } from "./ProductVariation";

export class _OrderValuesAtSubmissionModel {
    
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Product: ProductModel;
    Variations: ProductVariationModel[];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<_OrderValuesAtSubmissionModel>) {
        this.Product = new ProductModel();
        this.Variations = [];
        Object.assign(this, init);
    }
}
