import { NextRequest, NextResponse } from 'next/server';
import { unstable_noStore } from 'next/cache';
import { StockistModel } from '@/app/models/Stockist';
import { StockistBusiness } from './business';


// - --- - //
// - GET - //
// - --- - //

export async function GET() {
    try {
        unstable_noStore();
        const result = await StockistBusiness.GetAll();
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const stockistData:StockistModel = await request.json();
        await StockistBusiness.Create(stockistData);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - ------ - //
// - UPDATE - //
// - ------ - //

export async function PATCH(request: NextRequest) {
    try {
        const stockistData:StockistModel = await request.json();
        await StockistBusiness.Update(stockistData);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}