@import '../../global';

.glowContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    pointer-events: none;
    z-index: -1;
}

.glow {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba($secondaryColor, 0.8) 0%, rgba($secondaryColor, 0) 70%);
    will-change: transform;
    transition: width 0.3s ease, height 0.3s ease;
}
