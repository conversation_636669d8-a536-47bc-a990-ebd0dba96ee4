@import '../../global';

$backLabelBorder: solid 1px $offBlack;

.mainContainer {
    position: relative;
    width: 370px;
    height: 250px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    font-size: 9px;
    text-align: center;
    border: $backLabelBorder;
    border-radius: 4px;
    background-color: white;
    overflow: hidden;

    .leftSide {
        flex: 6;
        height: 100%;
        display: flex;
        flex-direction: column;

        .logoAndContact {
            flex-grow: 1;
            padding: 6px 3px 12px 3px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;

            .productOf {
                font-size: 14px;
                font-weight: bold;
                color: $primaryColor;
            }

            .logo {
                width: 60px;
                height: auto;
            }

            .address {
                font-size: 11px;
                font-weight: 500;
                color: #33a2ff;
            }

            .phone {
                font-size: 11px;
                font-weight: 500;
                color: $primaryColor;
            }

            .email {
                color: black;
            }

            .social {
                display: flex;
                align-items: center;
                justify-content: center;
                column-gap: 4px;
                font-size: 10px;
                .facebookIcon,
                .instaIcon {
                    width: 13px;
                    height: 13px;
                }
            }
        }

        .biodegradable {
            padding: 2px 0 2px 0;
            color: #4566ff;
            font-size: 6px;
            border-top: $backLabelBorder;
            border-bottom: $backLabelBorder;
        }

        .barcodeContainer {
            height: 60px;
            padding: 4px;
            .barcode {
                width: 100%;
                height: 100%;
            }
        }
    }

    .rightSide {
        flex-grow: 10;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-left: $backLabelBorder;
        box-sizing: border-box;

        .name {
            padding: 4px 0;
            border-bottom: $backLabelBorder;
            font-weight: bold;
            color: #b0783a;
            p { margin: 0; } // Make sure override text's 'p' does not have margin
        }

        .nutInfoTitle {
            padding: 3px 0;
            font-size: 14px;
            font-weight: bold;
        }

        .servingsDataContainer {
            position: relative;
            padding: 0 0 3px 4px;
            display: grid;
            grid-template-columns: 5fr 4fr;
            row-gap: 3px;
            text-align: left;
            border-bottom: $backLabelBorder;
            .cookedText {
                position: absolute;
                right: 22px;
                bottom: 2px;
                font-weight: 500;
                color: $primaryColor;
            }
        }

        .nutritionalValuesHeadingsContainer {
            padding: 2px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            border-bottom: $backLabelBorder;
            .cookedPastaText {
                font-weight: 500;
                color: $primaryColor;
            }
        }

        .nutritionalValues {
            flex-grow: 1;
            padding: 3px 4px;
            display: grid;
            grid-template-columns: 100px 32px 54px 30px 1fr;
            align-content: space-between;
            align-items: center;
            > div:nth-child(5n-4) { text-align: left;  }
            > div:nth-child(5n-3) { text-align: right; padding-right: 3px; }
            > div:nth-child(5n-2) { text-align: left;  }
            > div:nth-child(5n-1) { text-align: right; padding-right: 3px; }
            > div:nth-child(5n-0) { text-align: left;  }
        }

        .handmadeWithLove {
            position: relative;
            background-color: #ffc1ff;
            padding: 2px 0 2px 0;
            line-height: 10px;
            .heartIcon {
                opacity: 0.8;
                position: absolute;
                top: 3px;
                right: 72px;
                width: 10px;
                height: 9px;
            }
        }
    }
}

.mainContainer.small {
    width: 350px;
    height: 240px;
    font-size: 8px;

    .leftSide {
        flex: 6;

        .logoAndContact {
            padding: 6px 3px 10px 3px;

            .productOf {
                font-size: 14px;
            }

            .logo {
                width: 50px;
            }

            .address {
                font-size: 10px;
                font-weight: 500;
            }

            .phone {
                font-size: 10px;
                font-weight: 500;
            }

            .social {
                column-gap: 4px;
                font-size: 9px;
                .facebookIcon,
                .instaIcon {
                    width: 12px;
                    height: 12px;
                }
            }
        }

        .biodegradable {
            padding: 2px 0 2px 0;
            font-size: 5px;
        }

        .barcodeContainer {
            height: 55px;
            padding: 4px;
        }
    }

    .rightSide {
        flex-grow: 10;

        .name {
            padding: 3px 0;
            font-size: 16px;
        }

        .nutInfoTitle {
            padding: 2px 0;
            font-size: 13px;
        }

        .servingsDataContainer {
            padding: 0 0 3px 4px;
            grid-template-columns: 5fr 4fr;
            row-gap: 2px;
            .cookedText {
                position: absolute;
                right: 23px;
                bottom: 2px;
                font-weight: 500;
                color: $primaryColor;
            }
        }

        .nutritionalValuesHeadingsContainer {
            padding: 2px;
            line-height: 10px;
        }

        .nutritionalValues {
            padding: 3px 4px;
            grid-template-columns: 85px 32px 47px 30px 1fr;
            > div:nth-child(5n-3) { padding-right: 2px; }
            > div:nth-child(5n-1) { padding-right: 2px; }
        }

        .handmadeWithLove {
            padding: 2px 0 2px 0;
            line-height: 9px;
            .heartIcon {
                top: 2px;
                right: 74px;
                width: 9px;
                height: 8px;
            }
        }
    }
}

.mainContainer.twoByFive {
    width: 350px;
    height: 195px;
    font-size: 7px;

    .leftSide {
        flex: 6;

        .logoAndContact {
            padding: 4px 3px 7px 3px;

            .productOf {
                font-size: 13px;
            }

            .logo {
                width: 40px;
            }

            .address {
                font-size: 9px;
                font-weight: 500;
            }

            .phone {
                font-size: 9px;
                font-weight: 500;
            }

            .social {
                column-gap: 4px;
                font-size: 8px;
                .facebookIcon,
                .instaIcon {
                    width: 11px;
                    height: 11px;
                }
            }
        }

        .biodegradable {
            padding: 2px 0 2px 0;
            font-size: 5px;
        }

        .barcodeContainer {
            height: 48px;
            padding: 4px;
        }
    }

    .rightSide {
        flex-grow: 10;

        .name {
            padding: 3px 0;
            font-size: 15px;
        }

        .nutInfoTitle {
            padding: 2px 0;
            font-size: 11px;
        }

        .servingsDataContainer {
            padding: 0 0 3px 5px;
            grid-template-columns: 4fr 4fr;
            row-gap: 2px;
            .cookedText {
                position: absolute;
                right: 27px;
                bottom: 2px;
                font-weight: 500;
                color: $primaryColor;
            }
        }

        .nutritionalValuesHeadingsContainer {
            padding: 2px;
            line-height: 8px;
        }

        .nutritionalValues {
            padding: 1px 5px;
            grid-template-columns: 91px 32px 51px 30px 1fr;
            > div:nth-child(5n-3) { padding-right: 2px; }
            > div:nth-child(5n-1) { padding-right: 2px; }
        }

        .handmadeWithLove {
            padding: 2px 0 2px 0;
            line-height: 7px;
            .heartIcon {
                top: 2px;
                right: 85px;
                width: 9px;
                height: 8px;
            }
        }
    }
}