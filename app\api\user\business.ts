import { sql } from "@vercel/postgres";
import { UserModel } from "../../models/User";
import { JC_Utils_Dates } from "@/app/Utils";

export class UserBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(userId:string) {
        return (await sql<UserModel>`
            SELECT "Id",
                "FirstName",
                "LastName",
                "Email",
                "PasswordHash",
                "LoginFailedAttempts",
                "LoginLockoutDate",
                "Phone",
                "IsAdmin",
                "IsWholesale",
                "CompanyName",
                "IsEmailSubscribed",
                "IsDiscountUser",
                "StripeCustomerId",
                "IsVerified",
                "CreatedAt",
                "ModifiedAt",
                "Deleted"
            FROM public."User"
            WHERE "Id" = ${userId}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetUserByStripeId(stripeCustomerId:string) {
        return (await sql<UserModel>`
            SELECT "Id",
                "FirstName",
                "LastName",
                "Email",
                "PasswordHash",
                "LoginFailedAttempts",
                "LoginLockoutDate",
                "Phone",
                "IsAdmin",
                "IsWholesale",
                "CompanyName",
                "IsEmailSubscribed",
                "IsDiscountUser",
                "StripeCustomerId",
                "IsVerified",
                "CreatedAt",
                "ModifiedAt",
                "Deleted"
            FROM public."User"
            WHERE "StripeCustomerId" = ${stripeCustomerId}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetByEmail(userEmail:string) {
        return (await sql<UserModel>`
            SELECT "Id",
                "FirstName",
                "LastName",
                "Email",
                "PasswordHash",
                "LoginFailedAttempts",
                "LoginLockoutDate",
                "Phone",
                "IsAdmin",
                "IsWholesale",
                "CompanyName",
                "IsEmailSubscribed",
                "IsDiscountUser",
                "StripeCustomerId",
                "IsVerified",
                "CreatedAt",
                "ModifiedAt",
                "Deleted"
            FROM public."User"
            WHERE "Email" = ${userEmail}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetByToken(userToken:string) {
        return (await sql<UserModel>`
            SELECT "Id",
                "FirstName",
                "LastName",
                "Email",
                "PasswordHash",
                "LoginFailedAttempts",
                "LoginLockoutDate",
                "ChangePasswordToken",
                "ChangePasswordTokenDate",
                "Phone",
                "IsAdmin",
                "IsWholesale",
                "CompanyName",
                "IsEmailSubscribed",
                "IsDiscountUser",
                "StripeCustomerId",
                "IsVerified",
                "CreatedAt",
                "ModifiedAt",
                "Deleted"
            FROM public."User"
            WHERE "ChangePasswordToken" = ${userToken}
              AND "Deleted" = 'False'
        `).rows[0];
    }


    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(userData:UserModel) {
        await sql`
            INSERT INTO public."User"
            (
                "Id",
                "FirstName",
                "LastName",
                "Email",
                "PasswordHash",
                "Phone",
                "IsAdmin",
                "IsWholesale",
                "CompanyName",
                "IsEmailSubscribed",
                "IsDiscountUser",
                "StripeCustomerId",
                "IsVerified",
                "CreatedAt"
            )
            VALUES
            (
                ${userData.Id},
                ${userData.FirstName},
                ${userData.LastName},
                ${userData.Email},
                ${userData.PasswordHash},
                ${userData.Phone},
                ${userData.IsAdmin},
                ${userData.IsWholesale},
                ${userData.CompanyName},
                ${userData.IsEmailSubscribed},
                ${userData.IsDiscountUser},
                ${userData.StripeCustomerId},
                ${userData.IsVerified},
                ${new Date().toUTCString()}
            )
        `
    }


    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(userData:UserModel) {
        await sql`
            UPDATE public."User"
            SET "FirstName"         = ${userData.FirstName},
                "LastName"          = ${userData.LastName},
                "Email"             = ${userData.Email},
                "Phone"             = ${userData.Phone},
                "CompanyName"       = ${userData.CompanyName},
                "IsEmailSubscribed" = ${userData.IsEmailSubscribed},
                "StripeCustomerId"  = ${userData.StripeCustomerId},
                "ModifiedAt"        = ${new Date().toUTCString()},
                "Deleted"           = ${userData.Deleted}
            WHERE "Id" = ${userData.Id}
        `;
    }

    static async UpdatePassword(userId:string, newHash:string) {
        await sql`
            UPDATE public."User"
            SET "PasswordHash" = ${newHash}
            WHERE "Id" = ${userId}
        `;
    }

    static async UpdateStripeCustomerId(userId:string, newCustomerId:string) {
        await sql`
            UPDATE public."User"
            SET "StripeCustomerId" = ${newCustomerId}
            WHERE "Id" = ${userId}
        `;
    }

    static async IncrementFailedAttemptsByEmail(email:string) {
        await sql`
            UPDATE public."User"
            SET "LoginFailedAttempts" = "LoginFailedAttempts"+1
            WHERE "Email" = ${email};
        `;
        let newFaildAttempts = (await sql`
            SELECT "LoginFailedAttempts"
            FROM public."User"
            WHERE "Email" = ${email};
              AND "Deleted" = 'False'
        `).rows[0].LoginFailedAttempts;
        // IF new LoginFailedAttempts >= 5, lockout user and reset LoginFailedAttempts
        if (newFaildAttempts >= 5) {
            await sql`
                UPDATE public."User"
                SET "LoginLockoutDate" = ${new Date().toUTCString()},
                    "LoginFailedAttempts" = 0
                WHERE "Email" = ${email};
            `;
            return true; // Return true if now locked out
        }
        return false;
    }

    static async ResetFailedAttemptsByEmail(email:string) {
        await sql`
            UPDATE public."User"
            SET "LoginFailedAttempts" = 0
            WHERE "Email" = ${email};
        `;
    }

    static async SetResetPasswordToken(email:string, newToken:string) {
        await sql`
            UPDATE public."User"
            SET "ChangePasswordToken" = ${newToken},
                "ChangePasswordTokenDate" = ${JC_Utils_Dates.formatDateForPostgres(new Date())}
            WHERE "Email" = ${email};
        `;
    }

    static async SetUserVerificationToken(userId:string, verificationToken:string) {
        await sql`
            UPDATE public."User"
            SET "VerificationToken" = ${verificationToken}
            WHERE "Id" = ${userId}
        `;
    }

    static async SetUserIsVerified(userId:string) {
        await sql`
            UPDATE public."User"
            SET "IsVerified" = true
            WHERE "Id" = ${userId}
        `;
    }


    // - ------ - //
    // - DELETE - //
    // - ------ - //
    
    static async Delete(id:string) {
        await sql`
            UPDATE public."User"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

}