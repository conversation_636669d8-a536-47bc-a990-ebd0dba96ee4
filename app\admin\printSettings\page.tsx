"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";
import { useSearchParams } from 'next/navigation';

import J<PERSON>_Spinner from "../../components/JC_Spinner/JC_Spinner";
import <PERSON><PERSON><PERSON><PERSON>ton from "@/app/components/JC_Button/JC_Button";
import JC_Field from "@/app/components/JC_Field/JC_Field";
import JC_ToggleButtons from "@/app/components/JC_ToggleButtons/JC_ToggleButtons";
import JC_LabelFrontPage from "@/app/components/JC_LabelFrontPage/JC_LabelFrontPage";
import JC_LabelBackPage from "@/app/components/JC_LabelBackPage/JC_LabelBackPage";
import { D_FieldOption_LabelSizes } from "@/app/models/ComponentModels/JC_FieldOption";
import { JC_GetList } from "@/app/services/JC_GetList";
import { JC_Post } from "@/app/services/JC_Post";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import { JC_Utils, JC_Utils_CSS } from "@/app/Utils";

import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { ItemSettingsModel } from "@/app/models/ItemSettings";
import { LabelSizeEnum } from "@/app/enums/LabelSize";
import { SettingsTypeEnum } from "@/app/enums/VariationSettings";
import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";

export default function Page_PrintSettings() {

    // - ----- - //
    // - STATE - //
    // - ----- - //

    const searchParams = useSearchParams();

    // We don't directly use globalSettings, but we need the setter for the API response
    const [, setGlobalSettings] = useState<GlobalSettingsModel[]>([]);
    const [originalGlobalSettings, setOriginalGlobalSettings] = useState<GlobalSettingsModel[]>([]);

    // Label size selection - check URL parameter first, then use default
    const labelSizeParam = searchParams.get('labelSize');

    // Convert URL parameter to enum value
    const getEnumFromUrlParam = (param: string | null): LabelSizeEnum => {
        if (param === 'small') return LabelSizeEnum.Small;
        if (param === '2x5') return LabelSizeEnum.TwoByFive;
        return LabelSizeEnum.Large;
    };

    // Convert enum value to URL parameter
    const getUrlParamFromEnum = (enumValue: LabelSizeEnum): string => {
        if (enumValue === LabelSizeEnum.Small) return 'small';
        if (enumValue === LabelSizeEnum.TwoByFive) return '2x5';
        return 'large';
    };

    // Initialize label size enum from URL parameter or default
    const initialLabelSizeEnum = labelSizeParam && ['large', 'small', '2x5'].includes(labelSizeParam)
        ? getEnumFromUrlParam(labelSizeParam)
        : LabelSizeEnum.Large;

    // State for label size enum
    const [labelSizeEnum, setLabelSizeEnum] = useState<LabelSizeEnum>(initialLabelSizeEnum);

    // State for URL parameter (for maintaining URL state)
    const [selectedLabelSize, setSelectedLabelSize] = useState<string>(
        labelSizeParam && ['large', 'small', '2x5'].includes(labelSizeParam)
            ? labelSizeParam
            : 'large'
    );

    // Toggle buttons options
    const labelSizeOptions = D_FieldOption_LabelSizes();

    // Padding settings state
    const [paddingSettings, setPaddingSettings] = useState<{ [key: string]: number }>({});

    // State for product and variations data
    const [product, setProduct] = useState<ProductModel | null>(null);
    const [variations, setVariations] = useState<ProductVariationModel[]>([]);
    const [variationsData, setVariationsData] = useState<ProductVariationsDataModel | null>(null);
    const [initialised, setInitialised] = useState<boolean>(false);

    // Helper function to update the variation's label size
    const updateVariationLabelSize = (newLabelSizeEnum: LabelSizeEnum) => {
        if (!product || !variations || variations.length === 0) return;

        // Get the selected variation IDs
        const selectedIds = variations.map(v => v.Id);

        // Get the variation that has the LabelSize setting
        const labelSizeVar = product.getVariationForSettings(SettingsTypeEnum.LabelSize, selectedIds);
        if (labelSizeVar) {
            // Update the label size in memory only (for UI display)
            if (!labelSizeVar.Ex_Settings) {
                labelSizeVar.Ex_Settings = new ItemSettingsModel({ ProductVariationId: labelSizeVar.Id });
            }
            labelSizeVar.Ex_Settings.LabelSize = newLabelSizeEnum;
            // Create a new product model to force a re-render
            const updatedProduct = new ProductModel(product);
            setProduct(updatedProduct);
        }
    };

    // Function to get product and variations data
    const getProductData = async () => {
        setInitialised(false);
        try {
            // Get product, selected variations, and variations data from localStorage
            const productToPrintData = JSON.parse(localStorage.getItem(LocalStorageKeyEnum.JC_ProductToPrint) ?? "{}");

            if (!productToPrintData || Object.keys(productToPrintData).length === 0) {
                console.error('No product data found in localStorage');
                JC_Utils.showToastError('No product data found. Please select a product on the Products page first.');
                return;
            }

            // Create a new ProductModel from the stored data
            let tempProduct = new ProductModel(productToPrintData);

            // Extract selectedVariation1, selectedVariation2 from the stored data
            const selectedVariation1 = productToPrintData.selectedVariation1;
            const selectedVariation2 = productToPrintData.selectedVariation2;

            // Get selected variation IDs for use with getVariationForSettings
            const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

            // Get the selected variations based on the IDs
            let selectedVars = tempProduct.Ex_Variations.filter(v => selectedIds.includes(v.Id));

            // Get the variations data using the product's getVarDataForSelections method
            // This ensures we get the correct data for the selected variations
            let tempVarData = tempProduct.getVarDataForSelections(
                selectedVariation1 ? new ProductVariationModel(selectedVariation1) : undefined,
                selectedVariation2 ? new ProductVariationModel(selectedVariation2) : undefined
            );

            if (tempProduct && tempVarData) {
                // Set the product, variations data, and selected variations
                setProduct(tempProduct);
                setVariationsData(tempVarData);
                setVariations(selectedVars);

                // Get the label size from the product's settings
                const variation = tempProduct.getVariationForSettings(SettingsTypeEnum.LabelSize, selectedIds);
                const productLabelSize = variation?.Ex_Settings?.LabelSize || LabelSizeEnum.Large;

                // Only update if URL parameter doesn't override it
                if (!labelSizeParam) {
                    // Convert the enum value to the URL parameter format
                    const urlParam = getUrlParamFromEnum(productLabelSize);

                    // Update the selected label size for URL purposes
                    setSelectedLabelSize(urlParam);

                    // Set the label size enum directly
                    setLabelSizeEnum(productLabelSize);

                    // Update URL to match the selected label size
                    const url = new URL(window.location.href);
                    url.searchParams.set('labelSize', urlParam);
                    window.history.pushState({}, '', url);
                }

                // Set initialised to true
                setInitialised(true);
            } else {
                console.error('Could not find the specified variations data');
                JC_Utils.showToastError('Failed to find the specified variations data');
            }
        } catch (error) {
            console.error('Error fetching product data:', error);
            JC_Utils.showToastError('Failed to load print settings data. Please try again later.');
        }
    };

    // Helper function to get padding value
    const getPaddingValue = (paddingType: string): number => {
        let prefix = selectedLabelSize.toUpperCase();
        // Handle the special case for 2x5 which is stored as TWOBYFIVE in the database
        if (prefix === '2X5') {
            prefix = 'TWOBYFIVE';
        }
        const key = `PADDING_${prefix}_${paddingType}`;
        return paddingSettings[key] || 0;
    };

    // Helper function to generate padding style for label pages
    const getFrontPaddingStyle = (): React.CSSProperties => {
        let prefix = selectedLabelSize.toUpperCase();
        // Handle the special case for 2x5 which is stored as TWOBYFIVE in the database
        if (prefix === '2X5') {
            prefix = 'TWOBYFIVE';
        }
        const topPadding = paddingSettings[`PADDING_${prefix}_FRONT_TOP`] || 0;
        const rightPadding = paddingSettings[`PADDING_${prefix}_FRONT_RIGHT`] || 0;
        const bottomPadding = paddingSettings[`PADDING_${prefix}_FRONT_BOTTOM`] || 0;
        const leftPadding = paddingSettings[`PADDING_${prefix}_FRONT_LEFT`] || 0;

        return {
            padding: `${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px`
        };
    };

    const getBackPaddingStyle = (): React.CSSProperties => {
        let prefix = selectedLabelSize.toUpperCase();
        // Handle the special case for 2x5 which is stored as TWOBYFIVE in the database
        if (prefix === '2X5') {
            prefix = 'TWOBYFIVE';
        }
        const topPadding = paddingSettings[`PADDING_${prefix}_BACK_TOP`] || 0;
        const rightPadding = paddingSettings[`PADDING_${prefix}_BACK_RIGHT`] || 0;
        const bottomPadding = paddingSettings[`PADDING_${prefix}_BACK_BOTTOM`] || 0;
        const leftPadding = paddingSettings[`PADDING_${prefix}_BACK_LEFT`] || 0;

        return {
            padding: `${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px`
        };
    };

    // Note: The actual label pages have fixed dimensions of 771px width and 1090px height
    // These dimensions are maintained in the components



    // Helper function to handle padding change
    const handlePaddingChange = (paddingType: string, value: string | number) => {
        let prefix = selectedLabelSize.toUpperCase();
        // Handle the special case for 2x5 which is stored as TWOBYFIVE in the database
        if (prefix === '2X5') {
            prefix = 'TWOBYFIVE';
        }
        const key = `PADDING_${prefix}_${paddingType}`;
        const numValue = typeof value === 'string' ? parseInt(value) || 0 : value;
        setPaddingSettings(prev => ({
            ...prev,
            [key]: numValue
        }));
    };

    // All padding settings are now managed through the paddingSettings object
    // Individual state variables for each padding setting have been removed

    // - ---------- - //
    // - INITIALIZE - //
    // - ---------- - //

    useEffect(() => {
        JC_Utils_CSS.forceHideHeaderFooter(styles);

        // Initialize all data
        const initializeData = async () => {
            // First fetch global settings
            await fetchGlobalSettings();

            // Then get product data - this will also set the label size based on product settings
            // if there's no URL parameter
            await getProductData();

            // After product data is loaded, if we have a label size parameter,
            // make sure the label size enum is set correctly and update the variation
            if (labelSizeParam && ['large', 'small', '2x5'].includes(labelSizeParam)) {
                const newLabelSizeEnum = getEnumFromUrlParam(labelSizeParam);
                setLabelSizeEnum(newLabelSizeEnum);

                // Update the variation's label size
                updateVariationLabelSize(newLabelSizeEnum);
            }
        };

        initializeData();
    }, []);



    // Function to handle label size change
    const handleLabelSizeChange = (id: string) => {
        // Temporarily set initialised to false to show loading state
        setInitialised(false);

        // Convert the toggle button ID to URL parameter format
        const urlParam = getUrlParamFromEnum(id as LabelSizeEnum);

        // Set the selected label size for URL purposes
        setSelectedLabelSize(urlParam);

        // Update the URL with the new label size without reloading the page
        // This makes it easier to maintain state when the page is refreshed
        const url = new URL(window.location.href);
        url.searchParams.set('labelSize', urlParam);
        window.history.pushState({}, '', url);

        // Set the label size enum directly from the toggle button ID
        setLabelSizeEnum(id as LabelSizeEnum);

        // Update the variation's label size
        updateVariationLabelSize(id as LabelSizeEnum);

        // Re-enable the UI after a short delay to allow state updates to complete
        setTimeout(() => setInitialised(true), 10);
    };

    // - --------- - //
    // - FUNCTIONS - //
    // - --------- - //

    // Check if there are any changes
    const checkForChanges = (): boolean => {
        if (originalGlobalSettings.length === 0) return false;

        // Check if any of the padding values have changed
        for (const [code, value] of Object.entries(paddingSettings)) {
            const originalSetting = originalGlobalSettings.find(s => s.Code === code);
            if (originalSetting && parseInt(originalSetting.Value) !== value) {
                return true;
            }
        }

        return false;
    };

    const fetchGlobalSettings = async () => {
        try {
            const response = await JC_GetList<GlobalSettingsModel>("globalSettings/all", {}, GlobalSettingsModel);
            if (response) {
                // Set the global settings
                setGlobalSettings(response);

                // Set original settings for change detection
                setOriginalGlobalSettings(JSON.parse(JSON.stringify(response)));

                // Initialize padding settings
                const newPaddingSettings: { [key: string]: number } = {};

                // Process all padding settings
                response.forEach(setting => {
                    if (setting.Code.startsWith('PADDING_')) {
                        newPaddingSettings[setting.Code] = parseInt(setting.Value) || 0;
                    }
                });

                // Set the padding settings state
                setPaddingSettings(newPaddingSettings);
            }
        } catch (error) {
            console.error("Error fetching global settings:", error);
            JC_Utils.showToastError("Failed to load global settings. Please try again later.");
        }
    };

    // Cancel function removed as requested

    const handleSave = async () => {
        // Check if in demo mode
        const isDemoMode = window.location.pathname.includes('/demo/');
        if (isDemoMode) {
            JC_Utils.showToastWarning("Cannot save in Demo mode!");
            return;
        }

        setInitialised(false);
        try {
            // Create updated settings array from paddingSettings
            const updatedSettings: GlobalSettingsModel[] = [];

            // Get descriptions from original settings
            const getDescription = (code: string): string => {
                const setting = originalGlobalSettings.find(s => s.Code === code);
                return setting?.Description || `Padding setting for ${code}`;
            };

            // Add all padding settings to the updated settings array
            Object.entries(paddingSettings).forEach(([code, value]) => {
                updatedSettings.push({
                    Code: code,
                    Description: getDescription(code),
                    Value: value.toString()
                });
            });

            // Update all padding settings
            // We're only updating existing settings, not creating new ones
            await JC_Post("globalSettings", updatedSettings, { list: "true" });

            // Update the originalGlobalSettings to match the current paddingSettings
            // This resets the change detection without requiring a page reload
            const updatedOriginalSettings = [...originalGlobalSettings];

            // Update the values in the original settings to match the current values
            Object.entries(paddingSettings).forEach(([code, value]) => {
                const settingIndex = updatedOriginalSettings.findIndex(s => s.Code === code);
                if (settingIndex !== -1) {
                    updatedOriginalSettings[settingIndex] = {
                        ...updatedOriginalSettings[settingIndex],
                        Value: value.toString()
                    };
                }
            });

            // Set the updated original settings to reset change detection
            setOriginalGlobalSettings(updatedOriginalSettings);

            // Show success message
            JC_Utils.showToastSuccess("Print settings saved successfully!");

            setInitialised(true);
        } catch (error) {
            console.error("Error saving print settings:", error);
            JC_Utils.showToastError("Failed to save print settings. Please try again.");
            setInitialised(true); // Re-enable the UI if there's an error
        }
    };

    // - ---- - //
    // - MAIN - //
    // - ---- - //

    // If data is not initialised, just show a loading spinner
    if (!initialised) {
        return <JC_Spinner />;
    }

    return (
        <div className={styles.pageWrapper}>
            <div className={styles.mainContainer}>
            <div className={styles.pageTitle}>Print Settings</div>

            {/* Save button at top right */}
            <div className={styles.saveButton}>
                <JC_Button
                    text="Save"
                    iconName="Save"
                    onClick={handleSave}
                    isDisabled={!checkForChanges()}
                />
            </div>

            {/* Label Size Toggle Buttons */}
            <div className={styles.toggleButtonsContainer}>
                <JC_ToggleButtons
                    options={labelSizeOptions}
                    selectedId={labelSizeEnum}
                    onChange={handleLabelSizeChange}
                />
            </div>

            <div className={styles.contentContainer}>
                {/* Label Preview Section */}
                <div className={styles.previewSection}>
                    {/* Front Label Preview */}
                    <div className={styles.labelPreviewContainer}>
                        <div className={styles.labelPreviewTitle}>Front Label</div>
                        <div className={styles.labelPreview}>
                            <div className={styles.paddingTop}>
                                <JC_Field
                                    inputId="front-top-padding"
                                    type={FieldTypeEnum.NumberStepper}
                                    value={getPaddingValue('FRONT_TOP')}
                                    onChange={(value) => handlePaddingChange('FRONT_TOP', value)}
                                    numberStepperOptions={{ increment: 1, minValue: 0 }}
                                />
                            </div>
                            <div className={styles.paddingHorizontal}>
                                <div className={styles.paddingLeft}>
                                    <JC_Field
                                        inputId="front-left-padding"
                                        type={FieldTypeEnum.NumberStepper}
                                        value={getPaddingValue('FRONT_LEFT')}
                                        onChange={(value) => handlePaddingChange('FRONT_LEFT', value)}
                                        numberStepperOptions={{ increment: 1, minValue: 0 }}
                                    />
                                </div>
                                <div className={styles.labelPage}>
                                    {initialised && (
                                        <JC_LabelFrontPage
                                            key={`front-${labelSizeEnum}`}
                                            product={product!}
                                            variations={variations}
                                            varData={variationsData!}
                                            labelSize={labelSizeEnum}
                                            disablePrint={true}
                                            paddingStyle={getFrontPaddingStyle()}
                                        />
                                    )}
                                </div>
                                <div className={styles.paddingRight}>
                                    <JC_Field
                                        inputId="front-right-padding"
                                        type={FieldTypeEnum.NumberStepper}
                                        value={getPaddingValue('FRONT_RIGHT')}
                                        onChange={(value) => handlePaddingChange('FRONT_RIGHT', value)}
                                        numberStepperOptions={{ increment: 1, minValue: 0 }}
                                    />
                                </div>
                            </div>
                            <div className={styles.paddingBottom}>
                                <JC_Field
                                    inputId="front-bottom-padding"
                                    type={FieldTypeEnum.NumberStepper}
                                    value={getPaddingValue('FRONT_BOTTOM')}
                                    onChange={(value) => handlePaddingChange('FRONT_BOTTOM', value)}
                                    numberStepperOptions={{ increment: 1, minValue: 0 }}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Back Label Preview */}
                    <div className={styles.labelPreviewContainer}>
                        <div className={styles.labelPreviewTitle}>Back Label</div>
                        <div className={styles.labelPreview}>
                            <div className={styles.paddingTop}>
                                <JC_Field
                                    inputId="back-top-padding"
                                    type={FieldTypeEnum.NumberStepper}
                                    value={getPaddingValue('BACK_TOP')}
                                    onChange={(value) => handlePaddingChange('BACK_TOP', value)}
                                    numberStepperOptions={{ increment: 1, minValue: 0 }}
                                />
                            </div>
                            <div className={styles.paddingHorizontal}>
                                <div className={styles.paddingLeft}>
                                    <JC_Field
                                        inputId="back-left-padding"
                                        type={FieldTypeEnum.NumberStepper}
                                        value={getPaddingValue('BACK_LEFT')}
                                        onChange={(value) => handlePaddingChange('BACK_LEFT', value)}
                                        numberStepperOptions={{ increment: 1, minValue: 0 }}
                                    />
                                </div>
                                <div className={styles.labelPage}>
                                    {initialised && (
                                        <JC_LabelBackPage
                                            key={`back-${labelSizeEnum}`}
                                            product={product!}
                                            varData={variationsData!}
                                            labelSize={labelSizeEnum}
                                            disablePrint={true}
                                            paddingStyle={getBackPaddingStyle()}
                                        />
                                    )}
                                </div>
                                <div className={styles.paddingRight}>
                                    <JC_Field
                                        inputId="back-right-padding"
                                        type={FieldTypeEnum.NumberStepper}
                                        value={getPaddingValue('BACK_RIGHT')}
                                        onChange={(value) => handlePaddingChange('BACK_RIGHT', value)}
                                        numberStepperOptions={{ increment: 1, minValue: 0 }}
                                    />
                                </div>
                            </div>
                            <div className={styles.paddingBottom}>
                                <JC_Field
                                    inputId="back-bottom-padding"
                                    type={FieldTypeEnum.NumberStepper}
                                    value={getPaddingValue('BACK_BOTTOM')}
                                    onChange={(value) => handlePaddingChange('BACK_BOTTOM', value)}
                                    numberStepperOptions={{ increment: 1, minValue: 0 }}
                                />
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            {/* Save button moved to top right */}
            </div>
        </div>
    );
}
