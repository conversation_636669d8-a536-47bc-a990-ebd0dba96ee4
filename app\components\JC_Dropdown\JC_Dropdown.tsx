"use client"

import styles from "./JC_Dropdown.module.scss";
import React, { useState, useEffect } from 'react';
import Image from "next/image";
import JC_Checkbox from '../JC_Checkbox/JC_Checkbox';
import { JC_FieldOption } from '@/app/models/ComponentModels/JC_FieldOption';
import { DropdownTypeEnum } from '@/app/enums/DropdownType';
import { JC_Utils } from "@/app/Utils";

export default function JC_Dropdown(_: Readonly<{
    overrideClass?: string;
    type: DropdownTypeEnum;
    label?: string;
    placeholder?: string;
    options: JC_FieldOption[];
    onOptionMouseOver?: (optionId:string) => void;
    onOptionMouseOut?: (optionId:string) => void;
    selectedOptionId?: string;
    removeSelectedInDropdown?: boolean;
    enableSearch?: boolean;
    onSelection: (newOptionId:string) => void;
    validate?: (value:string|number|undefined) => string;
    dataType?: string;
    scrollContainerToBottom?: string; // CSS selector for container to scroll to bottom when dropdown opens
}>) {

    // - STATE - //

    const [thisInputId] = useState<string>(JC_Utils.generateGuid());
    const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
    const [searchBoxText, setSearchBoxText] = useState<string>();
    const [selectedOption, setSelectedOption] = useState<JC_FieldOption | undefined>(
        !JC_Utils.stringNullOrEmpty(_.selectedOptionId) ? _.options.find(x => x.OptionId === _.selectedOptionId) : undefined
    );
    const [selectedOptions, setSelectedOptions] = useState<string[]>([]);


    // Update selectedOption when selectedOptionId prop changes
    useEffect(() => {
        const newSelectedOption = !JC_Utils.stringNullOrEmpty(_.selectedOptionId)
            ? _.options.find(x => x.OptionId === _.selectedOptionId)
            : undefined;

        setSelectedOption(newSelectedOption);
    }, [_.selectedOptionId, _.options]);


    // - INITIALISE - //

    let optionsList = _.options;
    if (_.removeSelectedInDropdown && selectedOption) {
        optionsList = optionsList.filter(o => o.OptionId !== selectedOption.OptionId);
    }
    if (!JC_Utils.stringNullOrEmpty(searchBoxText)) {
        optionsList = optionsList.filter(o => JC_Utils.searchMatches(searchBoxText!, o.Label));
    }


    // - BUILD - //

    // Dropdown Option Content
    function buildOptionContent(option?: JC_FieldOption, isMain?: boolean) {
        if (!option) {
            return <React.Fragment>
                {!JC_Utils.stringNullOrEmpty(_.placeholder) &&
                <div className={styles.optionLabel}>{_.placeholder}</div>}
            </React.Fragment>;
        }

        return <React.Fragment>
            {!JC_Utils.stringNullOrEmpty(option.IconName) &&
            <Image
                src={`/icons/${option.IconName}.webp`}
                width={0}
                height={0}
                className={styles.optionIcon}
                alt="Icon"
                unoptimized
            />}

            {!JC_Utils.stringNullOrEmpty(option.Label) &&
            <div className={styles.optionLabel}>{option.Label}</div>}

            {_.type == DropdownTypeEnum.Multi && !isMain &&
            <div className={styles.checkbox}>
                <JC_Checkbox
                    checked={option.Selected ?? false}
                    onChange={() => _.onSelection(option.OptionId)}
                />
            </div>}

        </React.Fragment>
    }


    // - HANDLES - //

    // Option selection
    function selectOption(optionId:string) {
        _.onSelection(optionId);
        setSearchBoxText("");
        if (_.type == DropdownTypeEnum.Default) {
            setDropdownOpen(false);
        }
    }

    function defaultIsOptionSelected(optionId:string) {
        if (!selectedOption) return false;
        return _.type == DropdownTypeEnum.Default && optionId === selectedOption.OptionId;
    }


    // - MAIN - //

    return (
        <div className={`${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            {/* Label */}
            {!JC_Utils.stringNullOrEmpty(_.label) && <div className={styles.label}>
                {_.label}
                {_.validate != null && !JC_Utils.stringNullOrEmpty(_.validate(_.selectedOptionId)) && <span className={styles.errorSpan}>{_.validate(_.selectedOptionId)}</span>}
            </div>}

            {/* Dropdown */}
            <div className={styles.mainContainer}>

                {/* Outside Click Div */}
                {dropdownOpen && <div className={styles.outsideClickDiv} onClick={() => setDropdownOpen(false)} />}

                {/* Selected Option */}
                <div
                    className={`${styles.mainButton} ck-dropdown`}
                    style={dropdownOpen ? { zIndex: 99 } : {}}
                    data-type={_.dataType}
                    onClick={() => {
                        if (!dropdownOpen) {
                            setDropdownOpen(true);

                            // Focus on search input if search is enabled
                            setTimeout(() => document.getElementById(`dropdown-input-${thisInputId}`)?.focus(), 20);

                            // Scroll container to bottom if specified
                            if (_.scrollContainerToBottom) {
                                setTimeout(() => {
                                    const container = document.querySelector(_.scrollContainerToBottom!);
                                    if (container) {
                                        container.scrollTo({
                                            top: container.scrollHeight,
                                            behavior: 'smooth'
                                        });
                                    }
                                }, 50); // Slight delay to ensure dropdown is fully rendered
                            }
                        } else {
                            setSearchBoxText("");
                            setDropdownOpen(false);
                        }
                    }}
                >

                    {/* Selected Option */}
                    {buildOptionContent(selectedOption, true)}

                    {/* Chevron */}
                    <Image
                        className={styles.chevronIcon}
                        src="/icons/Chevron.webp"
                        style={dropdownOpen ? {rotate: "180deg", top: "-3%"} : {}}
                        width={0}
                        height={0}
                        alt="Bag"
                        unoptimized
                    />

                    {/* Search */}
                    {_.enableSearch && dropdownOpen &&
                    <input id={`dropdown-input-${thisInputId}`} className={styles.searchBox} type="text" placeholder="Search..." onChange={(event) => setSearchBoxText(event.target.value)} />}

                </div>

                {/* Dropdown List */}
                {dropdownOpen &&
                <div
                    className={styles.dropdown}
                    style={dropdownOpen ? { zIndex: 99 } : {}}
                >
                    {optionsList.map(option => (
                        <div
                            key={option.OptionId}
                            className={`${styles.dropdownOption} ${defaultIsOptionSelected(option.OptionId) ? styles.selected : ''}`}
                            onClick={() => !defaultIsOptionSelected(option.OptionId) ? selectOption(option.OptionId) : null}
                            onMouseOver={() => _.onOptionMouseOver != null ? _.onOptionMouseOver(option.OptionId) : null}
                            onMouseOut={() => _.onOptionMouseOut != null ? _.onOptionMouseOut(option.OptionId) : null}
                        >
                            {buildOptionContent(option)}
                        </div>
                    ))}
                </div>}
                {/* {dropdownOpen && options.length > 8 && <div className={styles.bottomFade} id="bottomFade"/>} */}

            </div>

        </div>
    );
}