import { NextRequest, NextResponse } from "next/server";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { ProductVariationsDataBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const requestData = await request.json();
        const items: ProductVariationsDataModel[] = requestData.items.map((p: any) => new ProductVariationsDataModel(p));
        await ProductVariationsDataBusiness.UpdateList(items);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
