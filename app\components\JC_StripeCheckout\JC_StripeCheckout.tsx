"use client"

import React, { useEffect, useState } from 'react';
import styles from "./JC_StripeCheckout.module.scss";
import getStripe from '@/app/services/GetStripePromise';
import { Elements, PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import <PERSON><PERSON>_Spinner from '../JC_Spinner/JC_Spinner';
import J<PERSON>_Button from '../JC_Button/JC_Button';
import { JC_GetList } from '@/app/services/JC_GetList';
import { JC_Post } from '@/app/services/JC_Post';
import { GetBag } from '@/app/services/Bag';
import { useSession } from 'next-auth/react';
import { ProductModel } from '@/app/models/Product';
import { LocalStorageKeyEnum } from '@/app/enums/LocalStorageKey';
import { JC_Get } from '@/app/services/JC_Get';
import { JC_Utils } from '@/app/Utils';


const stripePromise = getStripe();


export default function JC_StripeCheckout(_: Readonly<{
    overrideClass?: string;
    fullName: string;
    email: string;
    orderId: string;
}>) {


    // - STATE - //

    const session = useSession();
    const [amount, setAmount] = useState<number>(100);
    const [clientSecret, setClientSecret] = useState<string>("");


    // - VARIABLES - //

    let hasDiscount = session.data?.user.IsDiscountUser;


    // - INITIALISE - //

    useEffect(() => {
        JC_Post("stripe/createPaymentIntent", { orderId: _.orderId, enteredEmail: _.email, enteredFullName: _.fullName }).then(result => {
            setClientSecret(result.clientSecret);
            if (hasDiscount) {
                JC_Get("globalSettings", { code: "UserDiscount" }).then((discountResult:any) => {
                    setAmount(JC_Utils.roundAndCutZeroes(result.amount * Number(discountResult.Value), 2));
                });
            } else {
                setAmount(result.amount);
            }
        });
    }, []);


    // - MAIN - //

    return (

        <div className={`${styles.mainContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            <Elements
                stripe={stripePromise}
                options={{
                    mode: "payment",
                    amount: amount*100,
                    currency: "aud", // Assuming customer is in Aus,
                    appearance: {
                        variables: {
                            colorDanger: styles.errorColor,
                            // colorPrimary: styles.primaryColor
                        }
                    }
                }}
            >
                {JC_Utils.stringNullOrEmpty(clientSecret) || !amount
                    ? <JC_Spinner />
                    : <InnerCheckout orderId={_.orderId} amount={amount} email={_.email} clientSecret={clientSecret}  />}
            </Elements>

        </div>

    );
}

function InnerCheckout(_: Readonly<{
    orderId: string;
    amount: number,
    email: string;
    clientSecret: string;
}>) {

    // - STATE - //

    const session = useSession();
    const stripe = useStripe();
    const elements = useElements();
    const [errorMessage, setErrorMessage] = useState<string>();
    const [paymentLoading, setPaymentLoading] = useState<boolean>(false);


    // - HANDLES - //

    async function handleSubmit() {

        // Check if amount still matches amount on order, so user can't add items in another tab then confirm payment on this tab
        let allProducts = await JC_GetList<ProductModel>(ProductModel.apiRoute, {}, ProductModel);
        if (_.amount != (await GetBag(session?.data)).getTotalPrice(allProducts)) {
            setErrorMessage("The session has expired, please reload this page.");
        } else {
            if (!stripe || !elements) return;
            setPaymentLoading(true);
            // Submit card details
            const { error:submitError } = await elements.submit();
            if (submitError) {
                // setErrorMessage(submitError.message);
                setPaymentLoading(false);
                return;
            }
            // Make payment
            const { error } = await stripe.confirmPayment({
                elements,
                clientSecret: _.clientSecret,
                confirmParams: {
                    return_url: `${window.location.origin}/orderSuccessful?orderId=${_.orderId}`,
                }
            });
            if (error) {
                setErrorMessage(error.message);
                setPaymentLoading(false);
                return;
            }
        }

    }


    // - MAIN - //

    return <div className={styles.mainContainer}>

        {/* Stripe Payment Form */}
        <PaymentElement options={{ layout: "accordion" }} />

        {/* Total */}
        <div className={styles.total}>Total: ${_.amount}</div>

        {!JC_Utils.stringNullOrEmpty(errorMessage) &&
        <div className={styles.errorMessage}>{errorMessage}</div>}

        {/* Submit */}
        <JC_Button
            text="Confirm Payment"
            onClick={handleSubmit}
            isLoading={paymentLoading}
        />

    </div>;
}
