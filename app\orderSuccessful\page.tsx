"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_BagView from "../components/JC_BagView/JC_BagView";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import { JC_Get } from "../services/JC_Get";
import { JC_Post } from "../services/JC_Post";
import { useSession } from "next-auth/react";
import { OrderModel } from "../models/Order";

export default function Page_OrderSuccessful() {

    const session = useSession();
    const params = useSearchParams();
    let orderId = params.get("orderId");

    // - STATE - //

    const [submittedBag, setSubmittedBag] = useState<OrderModel>();


    // - INITIALISE - //

    useEffect(() => {
        // Delete paymentIntent cookie
        JC_Post("stripe/deletePaymentIntentCookie", {});
        // Get Bag from orderId passed in url
        JC_Get<OrderModel>(OrderModel.apiRoute, { orderId: orderId }, OrderModel).then(item => setSubmittedBag(item));
    }, []);


    // - MAIN - //

    return !submittedBag || !submittedBag?.Ex_OrderItems || submittedBag?.Ex_OrderItems?.length == 0
        ? (<JC_Spinner isPageBody />)
        : (
            <div className={styles.mainContainer}>

                <JC_Title title="Order Successful!" />

                <div className={styles.bodyContainer}>

                    {/* Charlotte */}
                    <Image
                        className={styles.charlotte}
                        src="/charlottes/Dough.webp"
                        width={700}
                        height={700}
                        alt="CasellaKitchenLogo"
                    />

                    {/* Bag */}
                    <JC_BagView readOnly enableScroll bag={submittedBag} />

                </div>
                
            </div>
        );
}
