SELECT "product"."Id"
      ,"product"."Name"
      ,"product"."Description"
      ,"product"."UnitName"
      ,"product"."Price"
      ,"product"."WholesalePrice"
      ,"product"."VariationAddedPricesJson"
      ,"product"."ImageFileName"
      ,"product"."EnabledOnWebsite"
      ,"product"."SortOrder"
      ,"product"."CreatedAt"
      ,"product"."ModifiedAt"
      ,"product"."Deleted"
FROM public."Product" "product"
WHERE 1=1
    AND "product"."Deleted" = 'False'
    AND "product"."Id" = '8f835d15-dc2a-48c3-974e-7d6c1b98fa11'
ORDER BY "product"."SortOrder";
-- ORDER BY "product"."CreatedAt" DESC;

-- Delete
-- UPDATE public."Product"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = '6c36e2bc-9cac-478d-bbdb-1e0f32e5ca82'

-- Un-delete
-- UPDATE public."Product"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'c915164a-afc7-4826-bb11-81c2e96da8ca'

