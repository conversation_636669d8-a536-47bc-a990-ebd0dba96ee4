import { _Base } from "./_Base";

export class DietaryAttributeModel extends _Base {
    
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Description: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<DietaryAttributeModel>) {
        super(init);
        this.Code = "";
        this.Description = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Description}`;
    }
}
