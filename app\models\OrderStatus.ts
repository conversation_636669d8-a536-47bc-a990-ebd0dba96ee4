export class OrderStatusModel {

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    Description: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<OrderStatusModel>) {
        this.Code = "";
        this.Name = "";
        this.Description = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name} | ${this.Description}`;
    }
}
