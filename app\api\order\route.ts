import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { OrderModel } from "@/app/models/Order";
import { OrderBusiness } from "./business";

// - --- - //
// - GET - //
// - --- - //

// Get by "Id"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const orderId = params.get("orderId")!;
        const result = await OrderBusiness.Get(orderId);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const orderData:OrderModel = await request.json();

        // Set json field from object
        if (orderData.Ex_ValuesAtSubmission != null) {
            orderData.ValuesAtSubmissionJson = JSON.stringify(orderData.Ex_ValuesAtSubmission);
        }

        // Create
        await OrderBusiness.Create(orderData);

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const orderData:OrderModel = await request.json();

        // Set json field from object
        if (orderData.Ex_ValuesAtSubmission != null) {
            orderData.ValuesAtSubmissionJson = JSON.stringify(orderData.Ex_ValuesAtSubmission);
        }

        // Update
        await OrderBusiness.Update(orderData);

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}