import { NextRequest, NextResponse } from "next/server";
import { ItemSettingsModel } from "@/app/models/ItemSettings";
import { ItemSettingsBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const requestData = await request.json();
        const items: ItemSettingsModel[] = requestData.items.map((p: any) => new ItemSettingsModel(p));
        await ItemSettingsBusiness.UpdateList(items);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
