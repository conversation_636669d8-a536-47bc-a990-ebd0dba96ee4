import { ContactModel } from "@/app/models/Contact";
import { JC_Utils } from "@/app/Utils"

export default function Template_ContactEmail(_:ContactModel) {
    return (

        <div>
            <table>

                <tr>
                    <td>NAME MATE:</td>
                    <td>{_.Name}</td>
                </tr>

                <tr>
                    <td>EMAIL MATE:</td>
                    <td>{_.Email}</td>
                </tr>

                {!JC_Utils.stringNullOrEmpty(_.Phone) && 
                <tr>
                    <td>PHONE MATE:</td>
                    <td>{_.Phone}</td>
                </tr>}

                <tr>
                    <td>MESSAGE MATE:</td>
                    <td>{_.Message}</td>
                </tr>

            </table>
        </div>

    );
}
