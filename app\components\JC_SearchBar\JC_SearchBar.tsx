"use client"

import styles from "./JC_SearchBar.module.scss";
import React from "react";
import { ChangeEvent, useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from 'next/navigation'
import J<PERSON>_<PERSON>ton from "../JC_Button/JC_Button";
import { JC_GetList } from "@/app/services/JC_GetList";
import { ProductModel } from "@/app/models/Product";
import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";
import { JC_Utils } from "@/app/Utils";

export default function JC_SearchBar() {

    // - STATE - //

    const router = useRouter();
    const [searchText, setSearchText] = useState<string>("");
    const [resultsOpen, setResultsOpen] = useState(false);
    const [products, setProducts] = useState<ProductModel[]>([]);
    const [filteredProducts, setFilteredProducts] = useState<ProductModel[] | null>(null);


    // - INITIALISE - //

    useEffect(() => {
        JC_GetList<ProductModel>("product", {}, ProductModel).then(productList => {
            productList.sort((a,b) => a.Name > b.Name ? 1 : -1);
            setProducts(productList);
            setFilteredProducts(productList);
        });
    }, []);


    // - HANDLES - //

    // Open Results
    function openResults() {
        setResultsOpen(true);
        // IF on mobile, move search bar to top of screen.
        if (JC_Utils.isOnMobile()) {
            document.getElementById("JC_footer")?.classList.add(styles.forceFullScreen);
            let inputElement = document.getElementById("searchBarInput");
            let currentTop = inputElement?.getBoundingClientRect().top!;
            let wantDistanceFromTop = 20;
            window.scrollBy({ top: currentTop - wantDistanceFromTop, behavior: "smooth" });
        }
    }

    // Get Results
    function getResults(search:string) {
        setSearchText(search);
        // Must have at least 3 characters to show results
        if (search == null || search.length == 0) {
            setFilteredProducts(products);
        // ELSE search on Product Name or Variant Name that contains any of the words in the search
        } else {
            let newList:ProductModel[] = products.filter(product =>
                   JC_Utils.searchMatches(search, product.Name)
                || product.Ex_Variations.some(variant => JC_Utils.searchMatches(search, variant.Name))
            );
            setFilteredProducts(newList);
        }
    }

    // Reset
    function resetBar() {
        setSearchText("");
        setResultsOpen(false);
        setFilteredProducts(products);
        document.getElementById("searchBarInput")!.blur();
        document.getElementById("JC_footer")?.classList.remove(styles.forceFullScreen);
    }


    // - MAIN - //

    return (

        <div className={styles.mainContainer}>

            {/* Outside Click Div */}
            {resultsOpen && <div className={styles.outsideClickDiv} onClick={() => resetBar()} />}

            {/* Bar */}
            <div className={styles.bar} style={resultsOpen ? { zIndex: 99 } : {}}>

                {/* Input */}
                <input
                    id="searchBarInput"
                    placeholder="Search..."
                    value={searchText}
                    onFocus={() => openResults()}
                    onChange={(event:ChangeEvent<HTMLInputElement>) => getResults(event.target?.value)}
                />

                {/* Icon */}
                <Image
                    src={`/icons/Search.webp`}
                    width={100}
                    height={100}
                    className={styles.icon}
                    alt="Search icon"
                />

            </div>

            {/* Results */}
            {resultsOpen &&
            <div className={styles.results}>

                <div className={styles.resultsScrollDiv}>

                    {/* Logo (not searching) */}
                    {filteredProducts == null &&
                    <Image
                        src={`/icons/CK.webp`}
                        width={0}
                        height={0}
                        className={styles.ckLogo}
                        alt="CKLogo"
                    />}

                    {/* Results Tiles */}
                    {filteredProducts != null && filteredProducts.length > 0 &&
                    filteredProducts.map((product:ProductModel) =>
                        <div
                            key={product.Id}
                            className={styles.resultTile}
                            onClick={() => {
                                router.push(`product?id=${product.Id}`);
                                resetBar();
                            }}
                        >
                            {/* Price */}
                            <div className={styles.resultPrice}>
                                ${23.50.toFixed(2)}
                            </div>
                            {/* Image */}
                            <Image
                                className={styles.resultImage}
                                src={!JC_Utils.stringNullOrEmpty(product.ImageFileName) ? `/products/products/${product.ImageFileName}.webp` : `/products/placeholder.webp`}
                                width={50}
                                height={50}
                                alt={product.Name}
                            />
                            {/* Name */}
                            <div className={styles.resultName}>
                                {product.Name}
                            </div>

                        </div>
                    )}

                    {/* No Results (searched but no results) */}
                    {filteredProducts != null && filteredProducts.length == 0 &&
                    <div className={styles.noResultsMessage}>
                        <Image
                            className={styles.charlotteImage}
                            src={`/icons/CK.webp`}
                            width={0}
                            height={0}
                            alt="CKLogo"
                        />
                        <div className={styles.noResultsText}>No Results</div>
                    </div>}

                </div>

            </div>}

        </div>

    );
}
