SELECT "user"."Id"
      ,"user"."FirstName"
      ,"user"."LastName"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "FullName"
      ,"user"."Email"
      ,"user"."PasswordHash"
      ,"user"."LoginFailedAttempts"
      ,"user"."LoginLockoutDate"
      ,"user"."ChangePasswordToken"
      ,"user"."ChangePasswordTokenDate"
      ,"user"."Phone"
      ,"user"."IsAdmin"
      ,"user"."IsWholesale"
      ,"user"."CompanyName"
      ,"user"."IsEmailSubscribed"
      ,"user"."IsDiscountUser"
      ,"user"."StripeCustomerId"
      ,"user"."IsVerified"
      ,"user"."VerificationToken"
      ,"user"."CreatedAt"
      ,"user"."ModifiedAt"
      ,"user"."Deleted"
FROM public."User" "user"
WHERE 1=1
      AND "user"."Deleted" = 'False'
ORDER BY "user"."LastName", "user"."FirstName";