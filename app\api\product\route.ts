import { NextRequest, NextResponse } from "next/server";
import { ProductModel } from "@/app/models/Product";
import { ProductBusiness } from "./business";

export const dynamic = 'force-dynamic';


// - --- - //
// - GET - //
// - --- - //

export async function GET(request: NextRequest) {
    try {
        const url = new URL(request.url);
        const id = url.searchParams.get("id");
        const includeAll = url.searchParams.get("includeAll") === "true";

        // If ID is provided, get a single product
        if (id) {
            const product = await ProductBusiness.Get(id);
            if (!product) {
                return NextResponse.json({ error: "Product not found" }, { status: 404 });
            }
            return NextResponse.json(product);
        }

        // Otherwise, get all products
        const products = await ProductBusiness.GetList(includeAll);
        return NextResponse.json(products);
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const newItem:ProductModel = await request.json();
        await ProductBusiness.Create(newItem);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - ------ - //
// - UPDATE - //
// - ------ - //

export async function POST(request: NextRequest) {
    try {
        const requestData = await request.json();
        const product:ProductModel = new ProductModel(requestData.product);
        await ProductBusiness.Update(product);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const id:string = new URL(request.url).searchParams.get("id")!;
        await ProductBusiness.Delete(id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}