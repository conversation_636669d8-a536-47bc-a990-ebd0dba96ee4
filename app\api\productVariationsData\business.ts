import { sql } from "@vercel/postgres";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";

export class ProductVariationsDataBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll() {
        const results = (await sql<ProductVariationsDataModel>`
            SELECT "Id",
                   "ProductId",
                   "ProductVariationIds",
                   "Code",
                   "BarcodeNumber",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."ProductVariationsData"
            WHERE "Deleted" = 'False'
        `).rows;

        // Parse ProductVariationIds from JSON string to array
        return results.map(item => {
            if (item.ProductVariationIds && typeof item.ProductVariationIds === 'string') {
                try {
                    item.ProductVariationIds = JSON.parse(item.ProductVariationIds as unknown as string);
                } catch (e) {
                    console.error("Error parsing ProductVariationIds:", e);
                    item.ProductVariationIds = null;
                }
            }
            return item;
        });
    }

    static async GetForProduct(productId: string) {
        const results = (await sql<ProductVariationsDataModel>`
            SELECT "Id",
                   "ProductId",
                   "ProductVariationIds",
                   "Code",
                   "BarcodeNumber",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."ProductVariationsData"
            WHERE "ProductId" = ${productId}
              AND "Deleted" = 'False'
        `).rows;

        // Parse ProductVariationIds from JSON string to array
        return results.map(item => {
            if (item.ProductVariationIds && typeof item.ProductVariationIds === 'string') {
                try {
                    item.ProductVariationIds = JSON.parse(item.ProductVariationIds as unknown as string);
                } catch (e) {
                    console.error("Error parsing ProductVariationIds:", e);
                    item.ProductVariationIds = null;
                }
            }
            return item;
        });
    }

    static async RecordExists(id: string) {
        const result = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."ProductVariationsData"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `;
        return result.rows[0]["Count"] > 0;
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Create(item: ProductVariationsDataModel) {
        // Note: Barcode is assumed to be already set when passed in

        // Prepare the SQL query based on whether ProductVariationIds is null or an array
        if (item.ProductVariationIds === null) {
            // For product-only data (no variations)
            await sql`
                INSERT INTO public."ProductVariationsData"
                (
                    "Id",
                    "ProductId",
                    "ProductVariationIds",
                    "Code",
                    "BarcodeNumber",
                    "CreatedAt"
                )
                VALUES
                (
                    ${item.Id},
                    ${item.ProductId},
                    NULL,
                    ${item.Code},
                    ${item.BarcodeNumber},
                    ${new Date().toUTCString()}
                )
            `;
        } else {
            // For variation combinations
            // Convert array to PostgreSQL array literal format
            const arrayLiteral = `{${item.ProductVariationIds.map(id => `"${id}"`).join(',')}}`;

            await sql`
                INSERT INTO public."ProductVariationsData"
                (
                    "Id",
                    "ProductId",
                    "ProductVariationIds",
                    "Code",
                    "BarcodeNumber",
                    "CreatedAt"
                )
                VALUES
                (
                    ${item.Id},
                    ${item.ProductId},
                    ${arrayLiteral}::varchar[],
                    ${item.Code},
                    ${item.BarcodeNumber},
                    ${new Date().toUTCString()}
                )
            `;
        }
    }

    static async Update(item: ProductVariationsDataModel) {
        // Check if the record exists
        const exists = await this.RecordExists(item.Id);

        // If the record doesn't exist, create it instead
        if (!exists) {
            await this.Create(item);
            return;
        }

        // Prepare the SQL query based on whether ProductVariationIds is null or an array
        if (item.ProductVariationIds === null) {
            // For product-only data (no variations)
            await sql`
                UPDATE public."ProductVariationsData"
                SET "ProductId"           = ${item.ProductId},
                    "ProductVariationIds" = NULL,
                    "Code"                = ${item.Code},
                    "BarcodeNumber"       = ${item.BarcodeNumber},
                    "ModifiedAt"          = ${new Date().toUTCString()},
                    "Deleted"             = ${item.Deleted ?? false}
                WHERE "Id" = ${item.Id}
            `;
        } else {
            // For variation combinations
            // Convert array to PostgreSQL array literal format
            const arrayLiteral = `{${item.ProductVariationIds.map(id => `"${id}"`).join(',')}}`;

            // Update existing record
            await sql`
                UPDATE public."ProductVariationsData"
                SET "ProductId"           = ${item.ProductId},
                    "ProductVariationIds" = ${arrayLiteral}::varchar[],
                    "Code"                = ${item.Code},
                    "BarcodeNumber"       = ${item.BarcodeNumber},
                    "ModifiedAt"          = ${new Date().toUTCString()},
                    "Deleted"             = ${item.Deleted ?? false}
                WHERE "Id" = ${item.Id}
            `;
        }
    }

    static async UpdateList(items: ProductVariationsDataModel[]) {
        for (const item of items) {
            await this.Update(item);
        }
    }


}
