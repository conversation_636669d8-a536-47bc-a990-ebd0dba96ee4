@import '../../global';
@import '../admin';

$pageSectionGap: 30px;

// Common reusable styles
// Common website checkbox styling
.websiteCheckbox {
    background-color: $greyHover;
    border-radius: $tinyBorderRadius;
    padding: 3px 8px;
    font-size: 13px;
    outline: solid $smallBorderWidth $greyHover;
    margin-top: 22px;
    margin-bottom: 10px;
    width: max-content;
    text-align: center;
    cursor: pointer !important;

    label {
        cursor: pointer !important;
    }
}

// Common settings row style
.productSettingsRow {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 30px;
    width: 100%;
    justify-content: center;
}

// Style for delete button
.deleteButton {
    margin-top: 10px;
}

// Common scrollable container style
@mixin scrollableContainer {
    @include hideScrollBar;
    overflow-y: auto;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-bottom: 15px; // Add padding at the bottom for better scrolling experience
}

.mainContainer {
    width: calc(100vw);
    height: 100vh;
    display: flex;
    grid-auto-flow: column;

    // Main Body
    .mainBody {
        flex-grow: 1;
        display: flex;
        flex-direction: row;
        height: 100%;

        // ProductsList wrapper
        .productsListWrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: $pageSectionGap 0 0 $pageSectionGap;
            width: 220px; // Match the width of the list

            // ProductsList component styles
            .productsListContainer {
                @include adminListContainerStyles;
                height: max-content;
                max-height: calc(100% - (4 * #{$pageSectionGap})); // Reduced by 2*pageSectionGap
                border-radius: $smallBorderRadius;
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                width: 220px; // Set to original width

                // Create a scrollable container for the product tiles
                .productTilesContainer {
                    @include scrollableContainer;
                }

                .selectionTile.selected {
                    background-color: $pastelSecondaryColor;
                }

                .inCopyOtherMode.selectionTile.selected {
                    background-color: rgba($pastelSecondaryColor, 0.4);
                }

                .inCopyOtherMode.selectionTile.copyOtherSelected {
                    background-color: $miscellaneousColor2;
                }

                .noProductsMessage {
                    padding: 15px;
                    text-align: center;
                    font-size: 16px;
                    color: $offBlack;
                    font-style: italic;
                }
            }
        }

        // Selections Container
        .selectionsContainer {
            display: flex;
            flex-direction: row;
            height: 100%;
            position: relative;
            width: 30%;
            min-width: max-content;
            text-align: center;
            border-right: $adminDividerBorder;
            column-gap: $pageSectionGap;
            overflow-y: hidden; // Prevent scrolling
            box-sizing: border-box;

            // Variation Details Container
            .variationDetailsContainer {
                height: 100%; // Set to 100% height
                padding: $pageSectionGap $pageSectionGap calc($pageSectionGap * 12) 0;
                flex-grow: 1;
                min-width: max-content;
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                row-gap: calc($pageSectionGap * 3);
                overflow-y: auto; // Add scrolling
                box-sizing: border-box;
                @include hideScrollBar; // Hide scrollbar

                // Product Details Container
                .productDetailsContainer {
                    display: flex;
                    justify-content: center;

                    .productSettings {
                        @include adminFormContainerStyles;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 15px;
                        padding: 20px;
                        width: max-content;
                    }
                }

                // ItemsList component styles
                .itemContentWrapper {
                    display: flex;
                    flex-direction: row;
                    gap: $pageSectionGap;
                    width: 100%;
                }

                // Item Container
                .itemContainer {
                    position: relative;
                    display: flex;
                    align-items: flex-start;
                    row-gap: 10px;

                    // Ensure proper spacing in Copy Other mode
                    &.inCopyOtherMode {
                        margin-bottom: 15px; // Add margin at the bottom in Copy Other mode
                    }

                    // Wrapper for the list and settings
                    &::before {
                        content: "";
                        display: flex;
                        flex-direction: row;
                        column-gap: $pageSectionGap;
                    }

                    // ItemsList wrapper
                    .itemListWrapper {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: 220px; // Match the width of the list

                        // Ensure proper spacing in Copy Other mode
                        &.inCopyOtherMode {
                            margin-bottom: 15px; // Add margin at the bottom in Copy Other mode
                        }

                        // ItemsList component
                        .itemsList {
                            @include adminListContainerStyles;
                            position: relative;
                            height: max-content; // Set to original height
                            max-height: calc(100% - #{$pageSectionGap} * 2); // Account for the padding in selectionsContainer
                            border-radius: $smallBorderRadius;
                            display: flex;
                            flex-direction: column;
                            box-sizing: border-box;
                            width: 220px; // Set to original width

                            // Create a scrollable container for the item tiles
                            .itemTilesContainer {
                                @include scrollableContainer;
                            }

                            // Ensure proper padding in Copy Other mode
                            &.inCopyOtherMode {
                                padding-bottom: 15px !important; // Maintain the same padding as normal mode
                            }

                            &.inEditMode {
                                width: 220px;
                                margin: 0 10px;
                                border: solid $smallBorderWidth $offBlack;
                            }

                            .selectionTile {
                                &:hover {
                                    background-color: $greyHover;
                                }

                                &.selected {
                                    background-color: $miscellaneousColor1;
                                }
                            }

                            .inCopyOtherMode.selectionTile.selected {
                                background-color: rgba($miscellaneousColor1, 0.4);
                            }

                            .inCopyOtherMode.selectionTile.copyOtherSelected {
                                background-color: $miscellaneousColor2;
                            }

                            .noneCheckbox {
                                margin-top: 10px;
                                margin-bottom: 5px;
                            }

                            .editCategoryIcon {
                                position: absolute;
                                top: 10px;
                                right: 10px;
                                cursor: pointer;
                                border-radius: 50%;
                                padding: 6px;
                                overflow: visible;
                                transition: background-color 0.1s ease;

                                &:hover {
                                    background-color: $primaryColor;
                                }
                            }
                        }
                    }

                    // Sort Order Arrows Container
                    .sortOrderArrowsContainer {
                        position: absolute;
                        right: -30px;
                        top: 50%;
                        transform: translateY(-50%);
                        display: flex;
                        flex-direction: column;
                        gap: 2px;
                    }

                    .sortOrderArrow {
                        cursor: pointer;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: $pastelSecondaryColor;
                        border-radius: 50%;
                        width: 24px;
                        height: 24px;

                        &:hover {
                            background-color: $secondaryColor;
                        }
                    }

                    // Variation Details and Settings Container
                    .variationDetailsAndSettingsContainer {
                        display: flex;
                        flex-direction: column;
                        gap: $pageSectionGap;
                        width: 100%;

                        // Container for Variation Details
                        .variationDetailsFormContainer {
                            @include adminFormContainerStyles;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 15px;
                            padding: 20px;
                            width: max-content;
                        }
                    }

                    // Settings Container
                    .settingsContainer {
                        display: flex;
                        padding-left: 0;
                        gap: $pageSectionGap;
                        flex-direction: column;

                        // Container for Label Size and Servings Settings
                        .labelSizeServingsContainer {
                            display: flex;
                            flex-direction: column;
                            gap: $pageSectionGap;
                            width: 100%;
                        }

                        // Item Settings Container
                        .itemSettingsContainer {
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            row-gap: $pageSectionGap;

                            // Each Settings
                            .settingsForm {
                                @include adminFormContainerStyles;
                                width: max-content;

                                &.labelSizeSettings {
                                    display: grid;
                                    grid-template-columns: max-content max-content;
                                    align-items: center;
                                    column-gap: 14px;

                                    .labelSizeLabel {
                                        @include labelLarge;
                                    }

                                    > div:last-child { width: 140px; }
                                }

                                &.servingsSettings {
                                    display: grid;
                                    grid-template-columns: max-content max-content;
                                    align-items: center;
                                    justify-items: end;
                                    column-gap: 14px;
                                    row-gap: 10px;

                                    .servingsLabel {
                                        @include labelLarge;
                                    }
                                }

                                &.labelSettings {
                                    display: grid;
                                    grid-template-columns: max-content;
                                    justify-items: center;
                                    gap: $pageSectionGap;
                                    width: max-content;

                                    .settingsRow {
                                        display: flex;
                                        flex-direction: row;
                                        gap: $pageSectionGap;
                                        width: 100%;
                                        justify-content: center;
                                    }

                                    .listTitle {
                                        margin-top: 0;
                                        margin-bottom: 5px;
                                        width: 250px;
                                        padding: 10px;
                                        text-align: center;
                                        font-size: 20px;
                                        font-weight: bold;
                                        border-bottom: solid $smallBorderWidth $offBlack;
                                        position: relative;

                                        &:not(:first-child) {
                                            margin-top: 10px;
                                        }

                                        .editCategoryIcon {
                                            position: absolute;
                                            right: 10px;
                                            top: 50%;
                                            transform: translateY(-50%);
                                            cursor: pointer;
                                            border-radius: 50%;
                                            padding: 4px;
                                            transition: background-color 0.2s ease;

                                            &:hover {
                                                background-color: $primaryColor;
                                            }
                                        }
                                    }

                                    .iconSettingsContainer {
                                        width: 100%;
                                        margin-top: 10px;
                                        margin-bottom: 10px;

                                        .iconSettingsTitle {
                                            margin-bottom: 15px;
                                            padding: 10px;
                                            text-align: center;
                                            font-size: 20px;
                                            font-weight: bold;
                                            position: relative;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;

                                            .iconVisibleCheckbox {
                                                position: absolute;
                                                right: 55px;
                                                top: 53%;
                                                transform: translateY(-50%);
                                                font-size: 14px;
                                                font-weight: normal;
                                            }
                                        }

                                        .iconSettingsFields {
                                            display: flex;
                                            justify-content: space-between;
                                            gap: 15px;
                                            width: 100%;
                                            margin-left: -10px;
                                        }
                                    }

                                    .iconSettingField {
                                        display: flex;
                                        flex-direction: column;
                                        align-items: center;
                                        justify-content: center;
                                        gap: 5px;
                                        width: calc(100% / 3);

                                        .iconSettingImage {
                                            margin-bottom: 5px;
                                            height: 30px; /* Fixed height for all images */
                                            display: flex;
                                            align-items: center;
                                            justify-content: center; /* Center the icons */

                                            /* Default size for Width and Height icons */
                                            width: auto;
                                            max-width: 25px;
                                            max-height: 25px;

                                            /* Smaller size for Size icon */
                                            &[alt="Size"] {
                                                margin-top: 4px;
                                                max-width: 18px;
                                                max-height: 18px;
                                            }
                                        }

                                        // For the standalone X offset field
                                        &.frontLabelXOffset {
                                            margin: 0 auto;
                                            width: auto;
                                            max-width: 120px;
                                            justify-content: center;

                                            .iconSettingImage {
                                                margin-bottom: 5px;
                                            }
                                        }
                                    }
                                }

                                .packWeightTextField {
                                    width: 218px;
                                }
                            }
                        }

                        // IngredientsList component styles
                        .ingListContainer {
                            @include adminFormContainerStyles;
                            position: relative;
                            display: grid;
                            grid-template-columns: 240px 90px 90px 100px;
                            justify-content: center;
                            justify-items: center;
                            column-gap: 10px;
                            row-gap: 10px;
                            padding: 20px 20px 64px 20px;

                            .showValuesCheckbox {
                                position: absolute;
                                bottom: 20px;
                                left: 20px;
                                text-align: left;
                                background-color: $greyHover;
                                border-radius: $tinyBorderRadius;
                                padding: 3px 8px;
                                font-size: 13px;
                                outline: solid $smallBorderWidth $greyHover;
                                width: max-content;
                                cursor: pointer !important;

                                label {
                                    cursor: pointer !important;
                                }
                            }

                            .ingListHeadingText {
                                width: 100%;
                                font-size: 16px;
                                font-weight: bold;

                                &.sortable {
                                    cursor: pointer;
                                    position: relative;
                                    user-select: none;

                                    &:hover {
                                        color: $secondaryColor;

                                        .sortIndicator {
                                            filter: invert(29%) sepia(94%) saturate(1946%) hue-rotate(189deg) brightness(96%) contrast(101%);
                                        }
                                    }

                                    .headerLabelContainer {
                                        position: relative;
                                        width: max-content;
                                        margin: 0 auto;
                                    }

                                    .headerLabel {
                                        width: max-content;
                                        height: max-content;
                                        display: inline-block;
                                        text-align: center;
                                    }

                                    .sortIndicator {
                                        display: inline-block;
                                        position: absolute;
                                        width: 11px;
                                        height: auto;
                                        right: -18px;
                                        top: 28%;
                                        transform: translateY(-50%);
                                        z-index: 1;
                                    }
                                }
                            }

                            .ingInputOverride {
                                height: 36px !important;
                            }

                            .ingListDropdown {
                                position: relative;
                                width: 100%;
                            }

                            .totalText {
                                width: 100px;
                                font-size: 14px;
                                font-weight: bold;
                            }

                            .totalCost {
                                width: 100px;
                                font-size: 14px;
                                font-weight: bold;
                                grid-column: 3;
                            }

                            .costText {
                                font-size: 14px;
                                color: $offBlack;
                                font-weight: bold;
                                display: flex;
                                align-items: center;
                                height: 36px; /* Match the height of the input field */
                            }

                            .editButtonsContainer {
                                width: max-content;
                                position: absolute;
                                bottom: 62px;
                                right: 42px;
                                display: flex;
                                column-gap: 14px;
                            }

                            .copyOtherButton {
                                width: max-content;
                                position: absolute;
                                bottom: 19px;
                                left: 50%;
                                transform: translateX(-50%);
                                display: flex;
                                column-gap: 14px;
                            }
                        }

                        // Container for Front Label and Back Label
                        .labelContainersRow {
                            display: flex;
                            flex-direction: row;
                            gap: $pageSectionGap;
                            width: 100%;
                        }

                        // Individual label containers
                        .frontLabelContainer, .backLabelContainer {
                            flex: 1;
                            min-width: 300px;
                        }
                    }
                }
            }
        }

        // Edit Mode Selections Container
        .selectionsContainer.inEditMode {
            border-right: none;
            width: 100%;
            overflow-y: hidden; // Ensure no scrolling

            .variationDetailsContainer {
                flex-direction: row;
                flex-wrap: wrap;
                column-gap: calc($pageSectionGap * 2);
                width: 100%;
                justify-content: center;
                height: 100%; // Maintain height
                overflow-y: auto; // Keep scrolling
                @include hideScrollBar; // Hide scrollbar
            }
        }

        // Variations: Edit Mode
        .productDetailsContainer.inEditMode {
            border-right: none;
            .variationDetailsContainer {
                display: flex;
                flex-direction: row;
                overflow-y: auto;
                box-sizing: border-box;
                column-gap: calc($pageSectionGap * 2);
                height: 100%; // Maintain height
                @include hideScrollBar; // Hide scrollbar
            }
        }
    }

    // No Items yet
    .noItemsYetContainer {
        margin: 70px 100px;
        height: max-content;
        width: max-content;
        padding: 20px 26px;
        border-radius: $smallBorderRadius;
        background-color: $miscellaneousColor1;
        font-size: 30px;
        font-weight: bold;
    }

    .saveButton {
        position: fixed;
        top: 30px;
        right: 34px;
    }

    .editIcon {
        position: fixed;
        bottom: 30px;
        left: 30px;
        border-radius: 50%;
        background-color: $primaryColor;
        width: 60px;
        height: 60px;
        box-sizing: border-box;
        cursor: pointer;
        z-index: 100;

        img {
            position: absolute;
            left: 51%;
            top: 49%;
            transform: translate(-50%, -50%);
            width: auto;
            max-width: 55%;
            height: auto;
            max-height: 55%;
        }

        &:hover {
            outline-color: $secondaryColor;
            outline-width: $smallBorderWidth;
            outline-style: solid;
        }
    }

    .editModeButtons {
        position: fixed;
        bottom: 30px;
        right: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 14px;
        z-index: 100;
    }
}

.addNewButton {
    margin-top: 15px;
    min-width: 30px;
    min-height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    outline: solid $smallBorderWidth $offBlack;
    background-color: $offWhite;
    user-select: none;
    cursor: pointer;

    > div {
        margin-top: -2px;
        font-size: 20px;
        font-weight: bold;
    }
}

// Add Option container
.addOptionContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    align-self: flex-start;

    .addOptionText {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 5px;
        text-align: center;
    }

    .addNewButton {
        margin-top: 5px;
        align-self: center;
    }
}

// Specific styling for the ingredient add button
.ingredientAddButton {
    margin-top: 10px; // Original margin-top value
}

// Mobile message container
.mobileMessageContainer {
    display: none;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background-color: $offWhite;
    z-index: 1000;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    color: $primaryColor;
    padding: 20px;
}

// Media queries for responsive design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        display: none;
    }

    .mobileMessageContainer {
        display: flex;
    }
}

// LabelsDisplay component styles
.labelsContainer {
    flex-grow: 1;
    width: 800px;
    position: relative;
    height: 100%;
    padding: $pageSectionGap $pageSectionGap calc($pageSectionGap * 18) $pageSectionGap;
    overflow-y: auto;
    box-sizing: border-box;
}

.labelContainer {
    width: 630px;
    height: 570px;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 8px;

    // Make label bigger
    padding-left: 30px;

    > div {
        margin-top: 120px;
        transform: scale(1.8);
        // Add very subtle shadow to the labels
        filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.07));
    }
}

// Barcode dropdown container
.barcodeDropdownContainer {
    width: max-content;
    margin-top: -18px;
    margin-left: 42px;
    align-self: flex-start;
}

.barcodeDropdown {
    width: 170px;
}

.printButton {
    width: 50px !important;
    height: 50px !important;
    padding: 0 !important;
    border-radius: 8px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.printIcon {
    width: 30px !important;
    height: 30px !important;
    margin-left: 0 !important; /* Remove any left margin */
}

// CopyOtherMode component styles
.copyOtherInfoContainer {
    margin-top: 40px;
    width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 20px;
    font-size: 24px;

    div:first-child {
        font-weight: bold;
    }
}

// NewProductModal component styles
.unitNameContainer {
    width: 100%;
}

// NewVariationModal component styles
.fieldOverride {
    width: 100%;
}

