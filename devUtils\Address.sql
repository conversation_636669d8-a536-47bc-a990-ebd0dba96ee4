SELECT "address"."Id"
      ,"address"."UserId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"user"."CompanyName" "__UserCompany"
      ,"address"."Line1"
      ,"address"."Line2"
      ,"address"."City"
      ,"address"."Postcode"
      ,"address"."Country"
      ,"address"."CreatedAt"
      ,"address"."ModifiedAt"
      ,"address"."Deleted"
FROM public."Address" "address"
INNER JOIN public."User" "user" ON "address"."UserId" = "user"."Id"
WHERE 1=1
      AND "address"."Deleted" = 'False'
ORDER BY "address"."CreatedAt" DESC;