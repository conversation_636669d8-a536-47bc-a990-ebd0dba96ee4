"use client"

import { JC_Utils } from "@/app/Utils";
import styles from "./JC_Checkbox.module.scss";
import React from 'react';

export default function JC_Checkbox(_: Readonly<{

    label?: string;
    checked?: boolean;
    onChange?: () => void;

}>) {

    return (
        <div className={`${styles.mainContainer} ${_.onChange != null ? styles.clickable : ""}`} onClick={_.onChange}>
            <div className={styles.checkbox}>
                {_.checked && <div className={styles.innerCheckedSquare} />}
            </div>
            {!JC_Utils.stringNullOrEmpty(_.label) && <label>{_.label}</label>}
        </div>
    );
}