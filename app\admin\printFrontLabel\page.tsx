"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";
import { JC_Utils, JC_Utils_CSS } from "@/app/Utils";
import JC_LabelFrontPage from "@/app/components/JC_LabelFrontPage/JC_LabelFrontPage";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";
import { SettingsTypeEnum } from "@/app/enums/VariationSettings";
import { LabelSizeEnum } from "@/app/enums/LabelSize";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";
import { JC_GetList } from "@/app/services/JC_GetList";


export default function Page_PrintFrontLabel() {

    // - STATE - //

    const [product, setProduct] = useState<ProductModel>();
    const [variations, setVariations] = useState<ProductVariationModel[]>([]);
    const [varData, setVarData] = useState<ProductVariationsDataModel>();
    const [labelSize, setLabelSize] = useState<LabelSizeEnum>();
    const [paddingStyle, setPaddingStyle] = useState<React.CSSProperties>({});
    const [paddingLoaded, setPaddingLoaded] = useState<boolean>(false);
    const [allLabelsReady, setAllLabelsReady] = useState<boolean>(false);

    // Print and close window after padding settings are loaded AND all labels are ready
    useEffect(() => {
        if (paddingLoaded && allLabelsReady) {
            // Wait an additional 0.5 seconds after all rendering processes complete
            setTimeout(() => {
                window?.print();
                setTimeout(window?.close, 0);
            }, 500);
        }
    }, [paddingLoaded, allLabelsReady]);


    // - INITIALISE - //

    useEffect(() => {
        async function initialize() {
            JC_Utils_CSS.forceHideHeaderFooter(styles);
            JC_Utils_CSS.forceWhiteBackground(styles);

            // Get product, selected variations, and variations data from localStorage
            const productToPrintData = JSON.parse(localStorage.getItem(LocalStorageKeyEnum.JC_ProductToPrint) ?? "{}");
            let tempProduct = new ProductModel(productToPrintData);

            // Extract selectedVariation1, selectedVariation2 from the stored data
            const selectedVariation1 = productToPrintData.selectedVariation1;
            const selectedVariation2 = productToPrintData.selectedVariation2;

            // Get selected variation IDs for use with getVariationForSettings
            const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

            // Get the selected variations based on the IDs
            let tempVariations = tempProduct.Ex_Variations.filter(v => selectedIds.includes(v.Id));

            // Get the variations data using the product's getVarDataForSelections method
            // This ensures we get the correct data for the selected variations
            let tempVarData = tempProduct.getVarDataForSelections(
                selectedVariation1 ? new ProductVariationModel(selectedVariation1) : undefined,
                selectedVariation2 ? new ProductVariationModel(selectedVariation2) : undefined
            );

            // If no varData was found and this is a product without variations, create a default one
            if (!tempVarData && (tempProduct.Ex_Variations.length === 0 || tempProduct.getVariationCategories().length === 0)) {
                console.log("Creating default varData for product without variations");
                // Create a default varData for a product without variations
                tempVarData = new ProductVariationsDataModel({
                    Id: JC_Utils.generateGuid(),
                    ProductId: tempProduct.Id,
                    ProductVariationIds: null, // null indicates this is for a product without variations
                    Code: "PRODUCT_ONLY",
                    BarcodeNumber: "",
                    UI_HasChanges: false,
                    Ex_NutValsEnabled: tempProduct.Ex_Settings?.NutValsEnabled
                });

                // Calculate nutritional values for the new record
                if (tempProduct.Ex_Ingredients && tempProduct.Ex_Ingredients.length > 0) {
                    tempVarData.calcNutVals(tempProduct.Ex_Settings?.WeightChange || 1, tempProduct.Ex_Ingredients);
                }
            }

            setProduct(tempProduct);
            setVariations(tempVariations);
            setVarData(tempVarData);

            const variation = tempProduct.getVariationForSettings(SettingsTypeEnum.LabelSize, selectedIds);
            // Add null check for variation
            const labelSizeValue = variation?.Ex_Settings?.LabelSize || LabelSizeEnum.Large;
            setLabelSize(labelSizeValue);
            console.log("= = = = = = = = = = = = = = = = = = = = = = = = = = = =");
            console.log(labelSizeValue);
            console.log("= = = = = = = = = = = = = = = = = = = = = = = = = = = =");

            // Fetch padding settings immediately after setting label size
            await fetchPaddingSettings(labelSizeValue);
            setPaddingLoaded(true);
        }

        initialize();
    }, []);



    // Fetch padding settings from global settings
    const fetchPaddingSettings = async (currentLabelSize: LabelSizeEnum | undefined = labelSize) => {
        try {
            const response = await JC_GetList<GlobalSettingsModel>("globalSettings/all", {}, GlobalSettingsModel);
            if (response) {
                // Find padding settings based on label size
                let paddingPrefix = "PADDING_LARGE";
                if (currentLabelSize === LabelSizeEnum.Small) {
                    paddingPrefix = "PADDING_SMALL";
                } else if (currentLabelSize === LabelSizeEnum.TwoByFive) {
                    paddingPrefix = "PADDING_TWOBYFIVE";
                }

                // Get the appropriate padding values for the current label size
                const topPadding = response.find(s => s.Code === `${paddingPrefix}_FRONT_TOP`)?.Value || "15";
                const rightPadding = response.find(s => s.Code === `${paddingPrefix}_FRONT_RIGHT`)?.Value || "0";
                const bottomPadding = response.find(s => s.Code === `${paddingPrefix}_FRONT_BOTTOM`)?.Value || "0";
                const leftPadding = response.find(s => s.Code === `${paddingPrefix}_FRONT_LEFT`)?.Value || "15";

                // Create padding style
                setPaddingStyle({
                    padding: `${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px`
                });
            }
        } catch (error) {
            console.error("Error fetching padding settings:", error);
        }
    };


    // - MAIN - //

    return (
        <div className={styles.printContainer}>
            {product != null && varData != null && labelSize != null && (
                <JC_LabelFrontPage
                    product={product}
                    variations={variations}
                    varData={varData}
                    labelSize={labelSize}
                    paddingStyle={paddingStyle}
                    disablePrint={true} // We'll handle printing in this page
                    onRenderComplete={() => setAllLabelsReady(true)}
                />
            )}
        </div>
    )

}