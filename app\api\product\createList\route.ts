import { NextRequest, NextResponse } from "next/server";
import { ProductModel } from "@/app/models/Product";
import { ProductBusiness } from "../business";

export async function PUT(request: NextRequest) {
    try {
        const requestData = await request.json();
        const products: ProductModel[] = requestData.products.map((p: any) => new ProductModel(p));

        // Create each product with all its child lists (variations, ingredients, etc.)
        for (const product of products) {
            await ProductBusiness.Create(product);
        }

        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
