import { VariationCategoryModel } from "./VariationCategory";
import { IngredientModel } from "./Ingredient";
import { ItemSettingsModel } from "./ItemSettings";
import { ItemIngredientModel } from "./ItemIngredient";
import { _Base } from "./_Base";
import { JC_Utils } from "../Utils";
import { _NutritionalValuesModel } from "./_NutritionalValues";

export class ProductVariationModel extends _Base {

    static apiRoute:string = "productVariation";
    static apiRoute_updateSortOrder:string = "productVariation/updateSortOrder";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ProductId: string;
    VariationCategoryCode: string;
    DependentOnProductVariationId: string | null;
    Name: string;
    AddedPrice: number;
    ImageFileName: string;
    SortOrder: number;
    EnabledOnWebsite: boolean;

    // Extended
    Ex_Category: VariationCategoryModel;
    Ex_Ingredients: ItemIngredientModel[];
    Ex_Settings: ItemSettingsModel;

    // UI
    UI_HasChanges?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ProductVariationModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.ProductId = "";
        this.VariationCategoryCode = "";
        this.DependentOnProductVariationId = null;
        this.Name = "";
        this.AddedPrice = 0;
        this.ImageFileName = "";
        this.SortOrder = 999;
        this.EnabledOnWebsite = false;
        Object.assign(this, init);
        // Extended
        this.Ex_Category = new VariationCategoryModel(init?.Ex_Category);
        this.Ex_Ingredients = (init?.Ex_Ingredients?.map(o => new ItemIngredientModel(o))) ?? [];
        this.Ex_Settings = new ItemSettingsModel(init?.Ex_Settings);
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.VariationCategoryCode} | ${this.Name}`;
    }


    // - ----- - //
    // - UTILS - //
    // - ----- - //

    // Get % change of Water considering overall % change
    static getWaterPercAfterChange(waterIng: ItemIngredientModel, allIngredients: ItemIngredientModel[], overallPercChange: number) : number {
        let overallWeightBefore = allIngredients.reduce((prev, cur) => prev + (cur.AmountGrams ?? 0), 0);
        let overallWeightAfter = overallWeightBefore * overallPercChange;
        let overallChangeGrams = overallWeightAfter - overallWeightBefore; // The overall change grams is equal to the water change grams, since only water is evaporating
        let waterWeightBefore = waterIng.AmountGrams!;
        let waterWeightAfter = waterWeightBefore + overallChangeGrams;
        let waterPercAfterChange = (waterWeightAfter / overallWeightAfter) * 100;
        return waterPercAfterChange;
    }

    // Get % of ingredient after weight change (assuming it is not water)
    static getIngPercAfterChange(ing: ItemIngredientModel, allIngredients: ItemIngredientModel[], overallPercChange: number) : number {
        let overallWeightBefore = allIngredients.reduce((prev, cur) => prev + (cur.AmountGrams ?? 0), 0);
        let overallWeightAfter = overallWeightBefore * overallPercChange;
        let ingPercAfterChange = ((ing.AmountGrams ?? 0) / overallWeightAfter) * 100;
        return ingPercAfterChange;
    }
}
