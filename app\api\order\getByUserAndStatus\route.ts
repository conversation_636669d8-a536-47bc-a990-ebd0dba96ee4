import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { OrderBusiness } from "../business";

// Get list by "UserId" and "OrderStatusCode"
export async function GET(request: NextRequest) {
    
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId")!;
        const orderStatusCode = params.get("orderStatusCode")!;
        const result = await OrderBusiness.GetByUserAndStatus(userId, orderStatusCode);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}