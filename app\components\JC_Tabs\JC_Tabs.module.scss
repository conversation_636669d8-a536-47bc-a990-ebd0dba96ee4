@import '../../global';

.mainContainer {

    // Tabs Container
    .tabsContainer {
        margin: auto;
        width: max-content;
        display: flex;
        column-gap: 22px;

        // Tab
        .tab {
            height: 38px;
            width: 200px;
            outline: solid $tinyBorderWidth $offBlack;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            user-select: none;
            cursor: pointer;

            &:hover {
                outline-width: 3px;
                outline-color: $secondaryColor;
                outline-offset: -1px;
            }

            &.selected {
                outline-width: 3px;
                outline-color: $primaryColor !important;
                outline-offset: -1px;
            }
        }
    }

    // Body
    .tabBody {
        margin: auto;
        margin-top: 40px;
        width: max-content;
    }
}


// - SCREEN SIZES - //

@media (max-width: $tinyScreenSize) {
    .tab {
        height: 34px !important;
        width: 180px !important;
        font-size: 14px;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .tab {
        height: 31px !important;
        width: 155px !important;
        font-size: 13px;
    }
}