SELECT "order"."Id"
      ,"order"."UserId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"user"."CompanyName" "__UserCompany"
      ,"user"."IsWholesale" "__UserIsWholesale"
      ,"order"."OrderStatusCode"
      ,"status"."Name" "__OrderStatus"
      ,"payment"."Id" "__PaymentId"
      ,"payment"."PaymentStatusCode" "__PaymentStatusCode"
      ,"paymentStatus"."Name" "__PaymentStatus"
      ,"order"."SubmittedDate"
      ,"order"."CompletedDate"
      ,"order"."ValuesAtSubmissionJson"
      ,"order"."CreatedAt"
      ,"order"."ModifiedAt"
      ,"order"."Deleted"
FROM public."Order" "order"
INNER JOIN public."User" "user" ON "order"."UserId" = "user"."Id"
INNER JOIN public."OrderStatus" "status" ON "order"."OrderStatusCode" = "status"."Code"
LEFT JOIN public."Payment" "payment" ON "order"."Id" = "payment"."OrderId"
LEFT JOIN public."PaymentStatus" "paymentStatus" ON "payment"."PaymentStatusCode" = "paymentStatus"."Code"
WHERE 1=1
      AND "order"."Deleted" = 'False'
ORDER BY "order"."CreatedAt" DESC;