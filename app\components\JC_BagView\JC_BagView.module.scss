@import '../../global';

$listBorder: solid $smallBorderWidth $offBlack;
$listBorderRadius: $tinyBorderRadius;
$listBackground: #e9e9e9;
$listHoverBackground: #e3e3e3;

.mainContainer {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
    position: relative;
    width: 550px;
    
    // List
    .bagList {
        overflow-y: auto;
        flex-grow: 1;
        flex-direction: column;
        outline: $listBorder;
        border-radius: $listBorderRadius;
        transition: background-color 0.1s ease-out;
        background-color: $offWhite;
        
        &.enableScroll {
            max-height: 700px;
            display: flex;
        }

        // No Items Text
        .noItemsContainer {
            height: 500px;
            .noItemsText {
                width: 300px;
                position: absolute;
                top: 44%; left: 50%; transform: translate(-50%, -50%);
                text-align: center;
                font-size: 56px;
                font-family: var(--title-font);
                color: $offBlack;
            }
        }

        // Item Container
        .itemContainer {
            height: 120px;
            padding: 10px 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            column-gap: 14px;
            user-select: none;
            background-color: #e8e8e8;
            &:nth-child(2n) {
                background-color: $offWhite;
            }

            // Image
            .itemImage {
                height: 100%;
                width: auto;
                aspect-ratio: 1 / 0.8;
                object-fit: cover;
                border-radius: $tinyBorderRadius;
            }

            // Main
            .itemBody {
                flex: 1;
                box-sizing: border-box;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                text-align: center;

                // Title
                .itemTitle {
                    max-width: 100%;
                    font-size: 18px;
                    cursor: pointer;
                    font-weight: bold;
                    &:hover {
                        color: $primaryColor;
                    }
                }

                // Price + Quantity Container
                .priceQuantityContainer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 0 auto 0 auto;
                    width: 85%;

                    // Show remove button
                    &:hover {
                        .removeButton { display: initial !important; }
                    }

                    // Price
                    .itemPrice {
                        width: 80px;
                        font-size: 18px;
                    }

                    // Quantity
                    .quantityContainer {
                        position: relative;
                        width: 90px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .removeButton {
                            display: none;
                            position: absolute;
                            top: 50%; transform: translateY(-50%);
                            left: -26px;
                            width: 15px;
                            height: auto;
                            cursor: pointer;
                        }

                        .decrementButton, .incrementButton {
                            outline: solid $smallBorderWidth $offBlack;
                            border-radius: 50%;
                            width: 20px;
                            height: 20px;
                            cursor: pointer;
                            user-select: none;
                        }
                        .decrementButton:hover { outline-color: $primaryColor; }
                        .incrementButton:hover { outline-color: $secondaryColor; }

                        .quantityText {
                            width: 30px;
                            font-size: 18px;
                        }
                    }
                }
            }

            // Item Total Price
            .itemTotalPrice {
                width: 90px;
                height: max-content;
                text-align: right;
                font-size: 18px;
                font-weight: bold;
            }
        }
    }

    .totalFooter {
        width: 100%;
        height: max-content;
        padding: 10px 28px;
        box-sizing: border-box;
        display: flex;
        outline: $listBorder;
        border-radius: $listBorderRadius;
        font-size: 20px;
        font-weight: bold;
        background-color: #e8e8e8;
        // background-color: $listBackground;
        transition: background-color 0.1s ease-out;
        & > div:first-child { flex: 10; }
        & > div:first-child { flex: 1;  }
        &:hover { background-color: $listHoverBackground; }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        width: 450px !important;
        .itemContainer {
            height: 110px !important;
            padding: 10px 16px !important;
            .itemBody {
                .itemTitle, .itemPrice, .quantityText {
                    font-size: 16px !important;
                }
                .priceQuantityContainer {
                    width: 92% !important;
                    .quantityContainer {
                        width: 80px !important;
                        .removeButton {
                            left: -20px !important;
                            width: 12px !important;
                        }
                    }
                }
            }
            .itemTotalPrice {
                width: 80px !important;
                font-size: 16px !important;
            }
        }
        .totalFooter {
            padding: 10px 16px !important;
            font-size: 16px;
        }
    }
}

// @media (max-width: $smallScreenSize) {
//     .importantInfoContainer {
//         height: 600px !important;
//         .impInfoText {
//             font-size: 36px !important;
//         }
//     }
// }

// @media (max-width: $tinyScreenSize) {
//     .importantInfoContainer {
//         height: 450px !important;
//         .impInfoText {
//             font-size: 30px !important;
//         }
//     }
// }