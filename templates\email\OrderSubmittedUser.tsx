import { OrderModel } from "@/app/models/Order";

export default function Template_OrderSubmittedUserEmail(_:OrderSubmittedUserEmailModel) {
    return (

        <div>
            <table>

                <tr>
                    <td>NAME MATE:</td>
                    <td>Thank you {_.fullName}! Your head looks like a dog's dinner mate.</td>
                </tr>

                <tr>
                    <td>EMAIL MATE:</td>
                    <td>{_.email}</td>
                </tr>

                <tr>
                    <td>AMOUNT MATE:</td>
                    <td>{_.amount}</td>
                </tr>

                <br/>
                <br/>
                <br/>

                {_.items.map(item =>
                    <tr key={`${item.productName} ${item.variationsString}`}>
                        <td>{item.productName}</td>
                        <td>{item.variationsString}</td>
                    </tr>
                )}

            </table>
        </div>

    );
}

export interface OrderSubmittedUserEmailModel {
    userId: string;
    fullName: string;
    email: string;
    items: {
        productName: string;
        variationsString: string;
        imagePath: string;
    }[],
    amount: number;
}