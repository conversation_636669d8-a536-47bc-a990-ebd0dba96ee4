import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen - Recipes",
    description: "Casella Kitchen's recipes."
};

export default async function Layout_Recipes(_: Readonly<{

    children: React.ReactNode;

}>) {

    // - AUTH - //

    const session = await auth();
    if (!session) {
        redirect("/");
    }

    // - MAIN - //

    return _.children;
    
}
