import { _Base } from "./_Base";
import { JC_Utils } from "../Utils";

export class ProductVariationIngredientModel extends _Base {

    static apiRoute:string = "productVariationIngredient";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ProductVariationId: string;
    IngredientId: string;
    AmountGrams: number;
    ShowPercent: boolean;

    // Extended
    Ex_ProductVariationName?: string;
    Ex_IngredientName?: string;

    // UI
    UI_HasChanges?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ProductVariationIngredientModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.ProductVariationId = "";
        this.IngredientId = "";
        this.AmountGrams = 0;
        this.ShowPercent = false;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Ex_ProductVariationName} | ${this.Ex_IngredientName} | ${this.AmountGrams}g`;
    }
}
