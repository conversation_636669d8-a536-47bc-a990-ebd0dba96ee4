"use client"

import styles from "./page.module.scss";
import { FocusEventHandler, useEffect, useState } from "react";
import J<PERSON>_Spinner from "../../components/JC_Spinner/JC_Spinner";
import <PERSON><PERSON>_<PERSON>ton from "@/app/components/JC_Button/JC_Button";
import JC_Checkbox from "@/app/components/JC_Checkbox/JC_Checkbox";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import J<PERSON>_Modal from "@/app/components/JC_Modal/JC_Modal";
import JC_Field from "@/app/components/JC_Field/JC_Field";
import JC_Title from "@/app/components/JC_Title/JC_Title";
import { IngredientModel } from "@/app/models/Ingredient";
import { GetIngredients } from "@/app/services/Ingredient";
import { JC_Post } from "@/app/services/JC_Post";
import { JC_Put } from "@/app/services/JC_Put";
import { J<PERSON>_Utils, JC_Utils_CSS } from "@/app/Utils";


export default function Page_Products() {

    // - ----- - //
    // - STATE - //
    // - ----- - //

    const [originalIngredients, setOriginalIngredients] = useState<IngredientModel[]>();
    const [ingredients, setIngredients] = useState<IngredientModel[]>();
    const [newIngredientModalOpen, setNewIngredientModalOpen] = useState<boolean>(false);
    const [tempNewIngredient, setTempNewIngredient] = useState<IngredientModel>(new IngredientModel());
    const [searchBoxText, setSearchBoxText] = useState<string>("");
    const [currentFlashOrangeIng, setCurrentFlashOrangeIng] = useState<string>();


    // - ---------- - //
    // - INITIALISE - //
    // - ---------- - //

    // Get Ingredients
    useEffect(() => {
        JC_Utils_CSS.forceHideHeaderFooter(styles);
        JC_Utils_CSS.forceRootOverflowYHidden(styles);
        GetIngredients().then(list => {
            setOriginalIngredients(list);
            setIngredients(JC_Utils.parseStringify(list));
        })
    }, []);


    // - ------- - //
    // - HANDLES - //
    // - ------- - //

    // Select Ingredient from side basic list
    function selectIngredient(ing:IngredientModel) {
        setCurrentFlashOrangeIng(ing.Id);
        document.getElementById(`ing-container-${ing.Id}`)?.scrollIntoView();
        document.getElementById(`main-list-container`)?.scrollBy({ top: -100 });
        setSearchBoxText("");
    }

    // Start editing Name
    function startEditingIngName(ing:IngredientModel) {
        if (isNew(ing.Id)) {
            tempNewIngredient.UI_EditingName = true;
            setTempNewIngredient({...tempNewIngredient});
        } else {
            setIngredients(ingredients!.map(i => {
                if (i.Id == ing.Id) { i.UI_EditingName = true; }
                return i;
            }));
        }
        setTimeout(() => (document.getElementById(`name-${ing.Id}`) as HTMLInputElement).focus(), 10);
    }

    // Apply value change on blur
    function handleIngInputBlur(ingId:string, updateFieldCallback:(ing:IngredientModel)=>void) {
        if (isNew(ingId)) {
            updateFieldCallback(tempNewIngredient);
            setTempNewIngredient({...tempNewIngredient});
        } else {
            setIngredients(
                ingredients!.map(i => {
                    if (i.Id == ingId) { updateFieldCallback(i); }
                    return i;
                })
            );
        }
    }

    // Update Ingredient's _NetCarbs
    function updateNetCarbs(ing:IngredientModel) {
        ing._NetCarbs = +(ing.Carbohydrate - ing.Fiber).toFixed(2);
        (document.getElementById(`ing-net-${ing.Id}`) as HTMLInputElement).value = `${ing._NetCarbs}`;
    }

    // Open "New Ingredient"
    function openNewIngredient() {
        let newIngredient = new IngredientModel();
        newIngredient.Name = "New Name";
        newIngredient.UI_EditingName = true;

        setTempNewIngredient(newIngredient)
        setNewIngredientModalOpen(true);
        setTimeout(() => (document.getElementById(`name-${newIngredient.Id}`) as HTMLInputElement).select(), 10);
    }

    // Check if Ingredient is new
    function isNew(ingId:string) { return ingId == tempNewIngredient.Id; }

    // Save new Ingredient
    function saveNewIngredient() {
        // Check name already exists
        if (ingredients!.some(i => i.Name == tempNewIngredient.Name)) {
            JC_Utils.showToastError(`The name '${tempNewIngredient.Name}' already exists!`)
        } else {
            JC_Put("ingredient", tempNewIngredient).then(() => {
                ingredients!.push(tempNewIngredient);
                setOriginalIngredients(JC_Utils.parseStringify(ingredients!.sort((a:IngredientModel, b:IngredientModel) => a.Name > b.Name ? 1 : -1)));
                setNewIngredientModalOpen(false);
                setTempNewIngredient(new IngredientModel());
                JC_Utils.showToastSuccess(`Created '${tempNewIngredient.Name}' ingredient!`);
            })
        }
    }

    // Save
    function saveIngredients() {
        let savingList = ingredients!.filter(i => {
            let orig = originalIngredients?.find(oI => oI.Id == i.Id);
            return JSON.stringify(i) != JSON.stringify(orig);
        });
        JC_Post<IngredientModel[]>(IngredientModel.apiRoute, savingList).then(() => {
            setOriginalIngredients(JC_Utils.parseStringify(ingredients));
            JC_Utils.showToastSuccess("Successfully saved ingredients!");
        });
    }


    // - ----- - //
    // - BUILD - //
    // - ----- - //

    // BUILD: Ingredient Form
    function buildIngredientForm(ing:IngredientModel, isNewIngredient:boolean = false) {
        let len = ing.Name.length;
        let titleSize = 0;
        if      (len < 13) titleSize = 22;
        if      (len < 15) titleSize = 20;
        else if (len < 17) titleSize = 18;
        else if (len < 19) titleSize = 17;
        else if (len < 21) titleSize = 16;
        else if (len < 23) titleSize = 15;
        else if (len < 25) titleSize = 14;
        else if (len < 27) titleSize = 13;
        else if (len < 29) titleSize = 12;
        else if (len < 31) titleSize = 11;
        else               titleSize = 10;

        return <div key={ing.Id} id={`ing-container-${ing.Id}`} className={`${styles.ingredientContainer} ${currentFlashOrangeIng == ing.Id ? styles.flashOrange : ''}`}>

            {/* Name */}
            <div className={styles.ingredientTitle}>
                {/* Read-Only Name */}
                {!ing.UI_EditingName &&
                <div onClick={() => startEditingIngName(ing)} style={{ fontSize: `${titleSize}px`}}>
                    {/* <JC_Tooltip content="Edit Name" position={TooltipPositionEnum.Bottom}> */}
                        {ing.Name}
                    {/* </JC_Tooltip> */}
                </div>}
                {/* Edit Name */}
                {ing.UI_EditingName &&
                <div className={styles.editContainer}>
                    <JC_Field
                        overrideClass={styles.nameFieldOverride}
                        inputOverrideClass={styles.nameFieldInputOverride}
                        inputId={`name-${ing.Id}`}
                        type={FieldTypeEnum.Text}
                        value={ing.Name}
                        onBlur={(newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => { ing.Name = newValue.length > 0 ? newValue : ing.Name; ing.UI_EditingName = false; })}
                        onEnter={(event:any) => event.target.blur()}
                        onEscape={(event:any) => { event.target.value = ingredients!.find(i => i.Id == ing.Id)!.Name; event.target.blur() }}
                    />
                </div>}
            </div>

            {/* Nut Vals */}
            <div className={styles.ingredientInputListContainer}>
                <div className={styles.nutValName}> Kj           </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-kj-${ing.Id}`}      type={FieldTypeEnum.Number} value={ing.Kilojoules}   onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.Kilojoules     = +newValue                         )}  />
                <div className={styles.nutValName}> Protein      </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-protein-${ing.Id}`} type={FieldTypeEnum.Number} value={ing.Protein}      onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.Protein        = +newValue                         )}  />
                <div className={styles.nutValName}> Fat, total   </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-fat-${ing.Id}`}     type={FieldTypeEnum.Number} value={ing.FatTotal}     onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.FatTotal       = +newValue                         )}  />
                <div className={styles.nutValName}> - saturated  </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-sat-${ing.Id}`}     type={FieldTypeEnum.Number} value={ing.FatSaturated} onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.FatSaturated   = +newValue                         )}  />
                <div className={styles.nutValName}> Carbohydrate </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-carb-${ing.Id}`}    type={FieldTypeEnum.Number} value={ing.Carbohydrate} onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => { ing.Carbohydrate = +newValue; updateNetCarbs(ing); } )}  />
                <div className={styles.nutValName}> - sugars     </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-sug-${ing.Id}`}     type={FieldTypeEnum.Number} value={ing.Sugars}       onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.Sugars         = +newValue                         )}  />
                <div className={styles.nutValName}> Fibre        </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-fibre-${ing.Id}`}   type={FieldTypeEnum.Number} value={ing.Fiber}        onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => { ing.Fiber        = +newValue; updateNetCarbs(ing); } )}  />
                <div className={styles.nutValName}> Net Carbs    </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-net-${ing.Id}`}     type={FieldTypeEnum.Number} value={ing._NetCarbs}    readOnly                                                                                             />
                <div className={styles.nutValName}> Sodium       </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-sodium-${ing.Id}`}  type={FieldTypeEnum.Number} value={ing.Sodium}       onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.Sodium         = +newValue                         )}  />
                <div className={styles.nutValName}> Cost         </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-cost-${ing.Id}`}    type={FieldTypeEnum.Number} value={ing.CostPer100g}  onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.CostPer100g    = +newValue                         )}  />
                <div className={styles.nutValName}> % Aus        </div> <JC_Field inputOverrideClass={styles.ingInputContainerOverride} inputId={`ing-% aus-${ing.Id}`}   type={FieldTypeEnum.Number} value={ing.PercAus}      onBlur={ (newValue:string) => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.PercAus        = +newValue                         )}  />
            </div>

            {/* Label Description */}
            <div className={styles.labelDescriptionContainer}>
                <div className={styles.labelDescriptionTitle}>Label Description</div>
                <div className={styles.labelDescriptionInput}> <textarea value={ing.LabelDescription ?? ""} onChange={event => { setIngredients(ingredients!.map(i => { if (i.Id == ing.Id) { i.LabelDescription = event.target.value; } return i; })); }} /> </div>
            </div>



            {/* Settings */}
            <div className={styles.settingsContainer}>
                <JC_Checkbox label="Is Allergen"   checked={ing.IsAllergen}  onChange={() => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.IsAllergen  = !ing.IsAllergen)} />
                <JC_Checkbox label="Is Organic"    checked={ing.IsOrganic}   onChange={() => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.IsOrganic   = !ing.IsOrganic)} />
                <JC_Checkbox label="Not Vegan"     checked={ing.IsNotVegan}  onChange={() => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.IsNotVegan  = !ing.IsNotVegan)} />
                <JC_Checkbox label="Hide On Label" checked={ing.HideOnLabel} onChange={() => handleIngInputBlur(ing.Id, (ing:IngredientModel) => ing.HideOnLabel = !ing.HideOnLabel)} />
            </div>

            {/* Submit Button - Only shown for new ingredients */}
            {isNewIngredient &&
            <div className={styles.newIngredientSubmitButton}>
                <JC_Button
                    text="Submit"
                    onClick={saveNewIngredient}
                    isDisabled={ing.Name == null || ing.Name == "" || ing.Name == "New Name"}
                />
            </div>}

        </div>;
    }


    // - ---- - //
    // - MAIN - //
    // - ---- - //

    return ingredients == null
        ? (<JC_Spinner isPageBody />)
        : (
            <>
                {/* Mobile Message */}
                <div className={styles.mobileMessageContainer}>
                    Only available on desktop!
                </div>

                <div className={styles.mainContainer}>

                {/* Basic List */}
                <div className={styles.basicListContainer}>
                    <input
                        className={styles.basicListSearchBox}
                        type="text"
                        placeholder="Search..."
                        value={searchBoxText}
                        onChange={(event) => setSearchBoxText(event.target.value)}
                    />
                    <div className={styles.basicListItems}>
                        {(!JC_Utils.stringNullOrEmpty(searchBoxText) ? ingredients.filter(i => JC_Utils.searchMatches(searchBoxText!, i.Name)) : ingredients).map((ing:IngredientModel) =>
                            <div key={ing.Id} className={`${styles.selectionTile} ${currentFlashOrangeIng == ing.Id ? styles.flashOrange : ''}`} onClick={() => selectIngredient(ing)}>{ing.Name}</div>
                        )}
                    </div>
                </div>

                {/* Main List */}
                <div className={styles.mainListContainer} id="main-list-container">

                    {/* Title */}
                    <JC_Title title="Ingredients" />

                    {/* New Ingredient Button */}
                    <div className={styles.newIngredientButton}><JC_Button text="New Ingredient" onClick={() => openNewIngredient()} /></div>

                    {/* Ingredients */}
                    <div className={styles.ingredientsListContainer}>
                        {ingredients.map((ing:IngredientModel) => buildIngredientForm(ing))}
                    </div>

                    {/* New Ingredient Modal */}
                    <JC_Modal isOpen={newIngredientModalOpen} onCancel={() => setNewIngredientModalOpen(false)} transparent>
                        {buildIngredientForm(tempNewIngredient, true)}
                    </JC_Modal>

                    {/* Save */}
                    {!newIngredientModalOpen && <div className={styles.saveButton}>
                        <JC_Button
                            text="Save"
                            iconName="Save"
                            iconOverrideClass={styles.saveIconOverride}
                            onClick={saveIngredients} />
                    </div>}

                </div>

            </div>
            </>
        );
}