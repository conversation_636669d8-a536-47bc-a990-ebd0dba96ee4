import { NextRequest, NextResponse } from "next/server";
import { ItemIngredientBusiness } from "../business";

export async function GET(request: NextRequest) {
    try {
        const productId = new URL(request.url).searchParams.get("productId");
        const productVariationId = new URL(request.url).searchParams.get("productVariationId");
        
        if (productId && !productVariationId) {
            const items = await ItemIngredientBusiness.GetForProduct(productId);
            return NextResponse.json(items);
        } else if (productVariationId) {
            const items = await ItemIngredientBusiness.GetForProductVariation(productVariationId);
            return NextResponse.json(items);
        } else {
            const items = await ItemIngredientBusiness.GetAll();
            return NextResponse.json(items);
        }
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
