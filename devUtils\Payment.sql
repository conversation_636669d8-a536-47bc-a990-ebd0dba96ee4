SELECT "payment"."Id"
      ,"payment"."OrderId"
      ,"order"."SubmittedDate" "__OrderSubmittedDate"
      ,"order"."CompletedDate" "__OrderCompletedDate"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"user"."CompanyName" "__UserCompany"
      ,"status"."Name" "__OrderStatus"
      ,"payment"."PaymentStatusCode"
      ,"paymentStatus"."Name" "__PaymentStatus"
FROM public."Payment" "payment"
INNER JOIN public."Order" "order" ON "payment"."OrderId" = "order"."Id"
INNER JOIN public."User" "user" ON "order"."UserId" = "user"."Id"
INNER JOIN public."OrderStatus" "status" ON "order"."OrderStatusCode" = "status"."Code"
INNER JOIN public."PaymentStatus" "paymentStatus" ON "payment"."PaymentStatusCode" = "paymentStatus"."Code"
ORDER BY "payment"."Id";