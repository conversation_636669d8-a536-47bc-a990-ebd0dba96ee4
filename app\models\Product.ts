import { DietaryAttributeModel } from "./DietaryAttribute";
import { ProductVariationModel } from "./ProductVariation";
import { ProductVariationsDataModel } from "./ProductVariationsData";
import { VariationCategoryModel } from "./VariationCategory";
import { ItemSettingsModel } from "./ItemSettings";
import { IngredientModel } from "./Ingredient";
import { ItemIngredientModel } from "./ItemIngredient";
import { _Base } from "./_Base";
import { SettingsTypeEnum } from "../enums/VariationSettings";
import { JC_Utils } from "../Utils";

export class ProductModel extends _Base {

    static apiRoute:string = "product";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;
    Description: string;
    UnitName: string;
    Price: number;
    WholesalePrice: number;
    VariationAddedPricesJson: string;
    ImageFileName: string;
    EnabledOnWebsite: boolean;
    SortOrder: number;

    // Extended
    Ex_DietaryAttributes: DietaryAttributeModel[];
    Ex_Variations: ProductVariationModel[];
    Ex_ProductVariationsData: ProductVariationsDataModel[];
    Ex_Ingredients: ItemIngredientModel[];
    Ex_Settings: ItemSettingsModel;

    // UI
    UI_HasChanges?: boolean;



    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ProductModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Name = "";
        this.Description = "";
        this.UnitName = "";
        this.Price = 0;
        this.WholesalePrice = 0;
        this.VariationAddedPricesJson = "";
        this.ImageFileName = "";
        this.EnabledOnWebsite = false;
        this.SortOrder = 999;
        this.Ex_DietaryAttributes = [];
        this.Ex_Variations = [];
        this.Ex_ProductVariationsData = [];
        this.Ex_Ingredients = [];
        this.Ex_Settings = new ItemSettingsModel({ ProductId: this.Id });
        Object.assign(this, init);
        // Extended
        this.Ex_DietaryAttributes = (init?.Ex_DietaryAttributes?.map(o => new DietaryAttributeModel(o))) ?? [];
        this.Ex_Variations = (init?.Ex_Variations?.map(o => new ProductVariationModel(o)).sort((a,b) => a.SortOrder > b.SortOrder ? 1 : -1)) ?? [];

        // Initialize settings with ProductId if not already set
        if (init?.Ex_Settings) {
            this.Ex_Settings = new ItemSettingsModel(init.Ex_Settings);
            // Ensure ProductId is set if not already
            if (!this.Ex_Settings.ProductId) {
                this.Ex_Settings.ProductId = this.Id;
            }
        } else {
            this.Ex_Settings = new ItemSettingsModel({ ProductId: this.Id });
        }

        this.Ex_ProductVariationsData = (init?.Ex_ProductVariationsData?.map(o => new ProductVariationsDataModel(o))) ?? [];
        this.Ex_Ingredients = (init?.Ex_Ingredients?.map(o => new ItemIngredientModel(o))) ?? [];
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }


    // - ----- - //
    // - UTILS - //
    // - ----- - //

    // Get unique Product Variation Categories for Product
    getVariationCategories(orderByNumSettings:boolean = false) {
        if (this.Ex_Variations == null) return [];

        // Filter out deleted variations
        const activeVariations = this.Ex_Variations.filter(v => !v.Deleted);
        if (activeVariations.length === 0) return [];

        let uniqueCategories:VariationCategoryModel[] = [];
        activeVariations.forEach(v => {
            if (!uniqueCategories.map(c => c.Code).includes(v.VariationCategoryCode)) {
                uniqueCategories.push(v.Ex_Category!);
            }
        });
        return uniqueCategories.sort(
            orderByNumSettings
            ? (a,b) => this.numSettingsForCategory(a.Code) > this.numSettingsForCategory(b.Code) ? 1 : -1
            : (a,b) => a.SortOrder > b.SortOrder ? 1 : -1
        );
    }

    // Map "Ex_Variations" to their Id's
    getVariationsIds(selectedVariations?: ProductVariationModel[]) {
        if (selectedVariations && selectedVariations.length > 0) {
            return selectedVariations.map(v => v.Id);
        } else {
            return this.Ex_Variations.map(v => v.Id);
        }
    }

    // Get Variations for given Variation Category
    getVariationsForCategory(categoryCode:string) {
        return this.Ex_Variations.filter(v => v.VariationCategoryCode == categoryCode && !v.Deleted).sort((a, b) => a.SortOrder - b.SortOrder);
    }

    // Check if a variation category has settings of a specific type
    variationHasSettings(variationCategoryCode:string, settings:SettingsTypeEnum) : boolean {
        // If no category code provided, return false
        if (!variationCategoryCode) return false;

        // Group variations by their category's sort order
        const categoriesBySortOrder = new Map<number, string[]>();

        // Get unique categories and their sort orders
        this.Ex_Variations.filter(v => !v.Deleted).forEach(v => {
            if (v.Ex_Category) {
                const sortOrder = v.Ex_Category.SortOrder;
                if (!categoriesBySortOrder.has(sortOrder)) {
                    categoriesBySortOrder.set(sortOrder, []);
                }
                if (!categoriesBySortOrder.get(sortOrder)!.includes(v.VariationCategoryCode)) {
                    categoriesBySortOrder.get(sortOrder)!.push(v.VariationCategoryCode);
                }
            }
        });

        // Sort the categories by their sort order
        const sortedSortOrders = Array.from(categoriesBySortOrder.keys()).sort((a, b) => a - b);

        // Check if any category has the required settings
        let anyCategoryHasSettings = false;

        // For each sort order (in order), check if any category has the required settings
        for (const sortOrder of sortedSortOrders) {
            const categoryCodes = categoriesBySortOrder.get(sortOrder)!;

            for (const categoryCode of categoryCodes) {
                // Find a variation with this category to check its settings
                const variation = this.Ex_Variations.find(v =>
                    v.VariationCategoryCode === categoryCode &&
                    !v.Deleted &&
                    v.Ex_Category?.[`Has${settings}Settings`]
                );

                if (variation) {
                    // If we found a category with the required settings, check if it matches the passed category
                    anyCategoryHasSettings = true;
                    return categoryCode === variationCategoryCode;
                }
            }
        }

        // If no category has the settings, use the first category by sort order
        if (!anyCategoryHasSettings && sortedSortOrders.length > 0) {
            const firstCategoryCodes = categoriesBySortOrder.get(sortedSortOrders[0])!;
            return firstCategoryCodes.includes(variationCategoryCode);
        }

        // If no category has the settings, return false
        return false;
    }

    // Variation given Settings
    getVariationForSettings(settings:SettingsTypeEnum, variationIds?:string[]) : ProductVariationModel | null {
        // Start with all non-deleted variations
        let variations = this.Ex_Variations?.filter(v => !v.Deleted) || [];

        // If variationIds are provided, filter variations by those IDs
        if (variationIds != null && variationIds.length > 0) {
            variations = variations.filter(v => JC_Utils.stringInListOfStrings(v.Id, variationIds));
        }

        // If no variations left after filtering, return null
        if (variations.length === 0) {
            // Log for debugging
            console.log(`getVariationForSettings(${settings}) - No variations found for product ${this.Name}`);
            return null;
        }

        // First, group variations by their category's sort order
        const variationsByCategory = new Map<number, ProductVariationModel[]>();

        variations.forEach(variation => {
            const categorySortOrder = variation.Ex_Category?.SortOrder ?? 999;
            if (!variationsByCategory.has(categorySortOrder)) {
                variationsByCategory.set(categorySortOrder, []);
            }
            variationsByCategory.get(categorySortOrder)!.push(variation);
        });

        // Sort the categories by their sort order
        const sortedCategorySortOrders = Array.from(variationsByCategory.keys()).sort((a, b) => a - b);

        // Check if any category has the required settings
        let anyCategoryHasSettings = false;

        // For each category (in order), check if any variation has the required settings
        for (const categorySortOrder of sortedCategorySortOrders) {
            const categoryVariations = variationsByCategory.get(categorySortOrder)!;

            // Sort variations within the category by their own sort order
            categoryVariations.sort((a, b) => a.SortOrder > b.SortOrder ? 1 : -1);

            // First, try to find a variation with the required settings
            const variationWithSettings = categoryVariations.find(v => v.Ex_Category?.[`Has${settings}Settings`]);
            if (variationWithSettings) {
                anyCategoryHasSettings = true;
                return variationWithSettings;
            }
        }

        // If no category has the required settings, just use the first category by sortorder
        if (!anyCategoryHasSettings && sortedCategorySortOrders.length > 0) {
            const firstCategoryVariations = variationsByCategory.get(sortedCategorySortOrders[0])!;
            return firstCategoryVariations[0];
        }

        // Fallback: return the first variation sorted by SortOrder (original behavior)
        return variations.sort((a, b) => a.SortOrder > b.SortOrder ? 1 : -1)[0];
    }

    // Get the number of Settings are related to a given Variation Category
    numSettingsForCategory(categoryCode:string) {
        let count = 0;
        const labelSizeVar = this.getVariationForSettings(SettingsTypeEnum.LabelSize);
        const ingredientsVar = this.getVariationForSettings(SettingsTypeEnum.Ingredients);
        const servingsVar = this.getVariationForSettings(SettingsTypeEnum.Servings);
        const labelsVar = this.getVariationForSettings(SettingsTypeEnum.Labels);
        const nameOverrideVar = this.getVariationForSettings(SettingsTypeEnum.NameOverride);

        count += labelSizeVar?.VariationCategoryCode === categoryCode ? 1 : 0;
        count += ingredientsVar?.VariationCategoryCode === categoryCode ? 1 : 0;
        count += servingsVar?.VariationCategoryCode === categoryCode ? 1 : 0;
        count += labelsVar?.VariationCategoryCode === categoryCode ? 1 : 0;
        count += nameOverrideVar?.VariationCategoryCode === categoryCode ? 1 : 0;
        return count;
    }

    /**
     * Marks a variation as deleted and updates all related variation data records
     * @param variationId The ID of the variation to delete
     */
    deleteVariation(variationId: string) {
        // Find the variation with the matching ID
        const variationToDelete = this.Ex_Variations.find(v => v.Id === variationId);
        if (!variationToDelete) {
            console.error(`Variation with ID ${variationId} not found`);
            return;
        }

        // Mark the variation as deleted and having changes
        variationToDelete.Deleted = true;
        variationToDelete.UI_HasChanges = true;

        // Mark the product as having changes
        this.UI_HasChanges = true;

        // Get all unique variation categories for this product
        const categories = this.getVariationCategories();

        // Determine if this is a single-category or two-category product
        const isSingleCategory = categories.length === 1;

        // Process each variation data record
        this.Ex_ProductVariationsData.forEach(varData => {
            // Skip the base product-only record (where ProductVariationIds is null)
            if (varData.ProductVariationIds === null) {
                return;
            }

            if (isSingleCategory) {
                // For single-category products, remove the deleted variationId from any lists containing it
                if (JC_Utils.stringInListOfStrings(variationId, varData.ProductVariationIds)) {
                    // Mark the record as deleted and having changes
                    varData.Deleted = true;
                    varData.UI_HasChanges = true;
                }
            } else {
                // For two-category products, check if this record includes the deleted variation
                if (JC_Utils.stringInListOfStrings(variationId, varData.ProductVariationIds)) {
                    // Mark the record as deleted and having changes
                    varData.Deleted = true;
                    varData.UI_HasChanges = true;
                }
            }
        });
    }

    // Get price of Product accounting for Variations selected
    getPriceForSelections(selectedVariation1?: ProductVariationModel, selectedVariation2?: ProductVariationModel) {
        let varSelections: ProductVariationModel[] = [];
        if (selectedVariation1) varSelections.push(selectedVariation1);
        if (selectedVariation2) varSelections.push(selectedVariation2);
        return this.Price + varSelections.reduce((prevResult, variation) => prevResult + variation.AddedPrice, 0);
    }

    // Get all barcodes used in this product's variations data
    getAllBarcodes(): string[] {
        if (!this.Ex_ProductVariationsData) return [];
        return this.Ex_ProductVariationsData
            .filter(vd => !vd.Deleted && vd.BarcodeNumber)
            .map(vd => vd.BarcodeNumber);
    }

    // Get Variations Data for selections
    getVarDataForSelections(selectedVariation1?: ProductVariationModel, selectedVariation2?: ProductVariationModel) {
        let selectedIds: string[] = [];
        if (selectedVariation1) selectedIds.push(selectedVariation1.Id);
        if (selectedVariation2) selectedIds.push(selectedVariation2.Id);

        // If no variations are selected or if the product has no variations, look for a product-only record (ProductVariationIds is null)
        if (selectedIds.length === 0 || this.Ex_Variations.length === 0 || this.getVariationCategories().length === 0) {
            let varData = this.Ex_ProductVariationsData.find(d => d.ProductVariationIds === null);

            if (varData) {
                // For product-only data, use the product's settings
                varData.Ex_NutValsEnabled = this.Ex_Settings?.NutValsEnabled;
                return varData;
            }

            // If no product-only record exists, create one
            // Create a new product-only record
            const newVarData = new ProductVariationsDataModel({
                Id: JC_Utils.generateGuid(),
                ProductId: this.Id,
                ProductVariationIds: null, // Set to null to indicate this is for a product without variations
                Code: "PRODUCT_ONLY",
                BarcodeNumber: "",
                UI_HasChanges: true,
                Ex_NutValsEnabled: this.Ex_Settings?.NutValsEnabled
            });

            // Add to the product's variations data
            this.Ex_ProductVariationsData.push(newVarData);

            // Calculate nutritional values for the new record
            newVarData.calcNutVals(this.Ex_Settings.WeightChange || 1, this.Ex_Ingredients || []);

            return newVarData;
        }

        // Otherwise, look for a record matching the selected variation IDs
        let varData = this.Ex_ProductVariationsData.find(d =>
            d.ProductVariationIds !== null &&
            JC_Utils.guidArraysEqual(d.ProductVariationIds, selectedIds)
        );

        if (varData) {
            let varForSettings_ingredients = this.getVariationForSettings(SettingsTypeEnum.Ingredients, selectedIds);
            varData.Ex_NutValsEnabled = varForSettings_ingredients?.Ex_Settings?.NutValsEnabled ?? false;
        }

        return varData;
    }

    // Get the appropriate settings object based on settings type
    getItemSettingsForSettingsType(settingsType: SettingsTypeEnum, variationIds?: string[]): ItemSettingsModel {
        // If the product has no variations, use the product's settings
        if (this.Ex_Variations.length === 0 || this.getVariationCategories().length === 0) {
            // Initialize settings if needed
            if (!this.Ex_Settings) {
                this.Ex_Settings = new ItemSettingsModel({ ProductId: this.Id });
            }
            return this.Ex_Settings;
        } else {
            // Get the variation for the specified settings
            const variation = this.getVariationForSettings(settingsType, variationIds);

            // If no variation found, fall back to product settings
            if (!variation) {
                if (!this.Ex_Settings) {
                    this.Ex_Settings = new ItemSettingsModel({ ProductId: this.Id });
                }
                return this.Ex_Settings;
            }

            // Initialize settings if needed
            if (!variation.Ex_Settings) {
                // Create settings with only ProductVariationId set (not both)
                variation.Ex_Settings = new ItemSettingsModel({
                    ProductVariationId: variation.Id
                });
            }
            return variation.Ex_Settings;
        }
    }

    // Add a new ingredient to either the product or the relevant variation
    addIngredient(variationIds?: string[]): void {
        // Mark the product as having changes
        this.UI_HasChanges = true;

        // Create a new ItemIngredientModel that wraps the ingredient
        // Explicitly set IngredientId to emptyGuid
        const newItemIngredient = new ItemIngredientModel({
            IngredientId: JC_Utils.emptyGuid(), // Explicitly set to empty GUID
            AmountGrams: 0,
            ShowPercent: false,
            UI_HasChanges: true
        });

        // Get the appropriate ingredients list
        const ingredientsList = this.getItemIngredients(variationIds);

        // Add the new ingredient to the list
        ingredientsList.push(newItemIngredient);

        // If this is a variation's ingredients, mark the variation as changed too
        if (this.Ex_Variations.length > 0 && this.getVariationCategories().length > 0) {
            const variation = this.getVariationForSettings(SettingsTypeEnum.Ingredients, variationIds);
            if (variation) {
                variation.UI_HasChanges = true;
                newItemIngredient.ProductVariationId = variation.Id;
            }
        } else {
            // This is a product ingredient
            newItemIngredient.ProductId = this.Id;
        }
    }

    // Get the appropriate ingredients array based on variation IDs
    getItemIngredients(variationIds?: string[]): ItemIngredientModel[] {
        // If the product has no variations, use the product's ingredients
        if (this.Ex_Variations.length === 0 || this.getVariationCategories().length === 0) {
            // Initialize ingredients array if needed
            if (!this.Ex_Ingredients) {
                this.Ex_Ingredients = [];
            }
            return this.Ex_Ingredients;
        } else {
            // Get the variation for ingredients settings
            const variation = this.getVariationForSettings(SettingsTypeEnum.Ingredients, variationIds);

            // If no variation found, fall back to product ingredients
            if (!variation) {
                if (!this.Ex_Ingredients) {
                    this.Ex_Ingredients = [];
                }
                return this.Ex_Ingredients;
            }

            // Initialize ingredients array if needed
            if (!variation.Ex_Ingredients) {
                variation.Ex_Ingredients = [];
            }
            return variation.Ex_Ingredients;
        }
    }

    // Replace an ingredient in either the product or the relevant variation
    replaceIngredient(allIngredients: IngredientModel[], oldIngId: string, newIngId: string, variationIds?: string[]): void {
        // Mark the product as having changes
        this.UI_HasChanges = true;

        // Get the new ingredient from allIngredients
        const newIngredient = allIngredients.find(i => i.Id === newIngId)!;
        if (!newIngredient) {
            console.error(`Ingredient with ID ${newIngId} not found in allIngredients`);
            return;
        }

        // Get the appropriate ingredients list
        const ingredientsList = this.getItemIngredients(variationIds);

        // Find the ingredient with the oldIngId
        const oldItemIngredient = ingredientsList.find(i => i.IngredientId === oldIngId);

        // If no old ingredient found, do nothing
        if (!oldItemIngredient) {
            console.error(`Ingredient with IngredientId ${oldIngId} not found in ingredients list`);
            return;
        }

        // Create a new ItemIngredientModel that wraps the ingredient
        // IMPORTANT: Preserve the original ItemIngredient's Id
        const newItemIngredient = new ItemIngredientModel({
            Id: oldItemIngredient.Id, // Keep the same Id to ensure proper updating
            IngredientId: newIngredient.Id,
            AmountGrams: oldItemIngredient.AmountGrams || 0,
            ShowPercent: oldItemIngredient.ShowPercent || false,
            UI_HasChanges: true,
            Ex_Ingredient: newIngredient // Ensure Ex_Ingredient is set
        });

        // If this is a variation's ingredients, mark the variation as changed too
        if (this.Ex_Variations.length > 0 && this.getVariationCategories().length > 0) {
            const variation = this.getVariationForSettings(SettingsTypeEnum.Ingredients, variationIds);
            if (variation) {
                variation.UI_HasChanges = true;
                newItemIngredient.ProductVariationId = variation.Id;
            }
        } else {
            // This is a product ingredient
            newItemIngredient.ProductId = this.Id;
        }

        // Find the index of the old ingredient
        const index = ingredientsList.indexOf(oldItemIngredient);

        // Replace the old ingredient with the new one at the same position
        if (index !== -1) {
            // Replace the old ingredient with the new one
            ingredientsList[index] = newItemIngredient;
        }

        // Update the appropriate ingredients list
        if (this.Ex_Variations.length === 0 || this.getVariationCategories().length === 0) {
            this.Ex_Ingredients = ingredientsList;
        } else {
            const variation = this.getVariationForSettings(SettingsTypeEnum.Ingredients, variationIds);
            if (variation) {
                variation.Ex_Ingredients = ingredientsList;
            } else {
                // If no variation found, update product ingredients
                this.Ex_Ingredients = ingredientsList;
            }
        }
    }

    // Set a field value on the appropriate settings object
    setSettingsValue(settings: SettingsTypeEnum, field: string, value: any, variationIds?: string[]): void {
        // Mark the product as having changes
        this.UI_HasChanges = true;

        // Get the appropriate settings object
        const settingsObject = this.getItemSettingsForSettingsType(settings, variationIds);

        // Mark settings as changed
        settingsObject.UI_HasChanges = true;

        // If this is a variation's settings, mark the variation as changed too
        if (settingsObject.ProductVariationId) {
            const variation = this.Ex_Variations.find(v => v.Id === settingsObject.ProductVariationId);
            if (variation) {
                variation.UI_HasChanges = true;
            }
        }

        // Set the field value on the settings object
        if (field && settingsObject) {
            // Log the current and new value for debugging
            console.log(`Setting ${field} from ${(settingsObject as any)[field]} to ${value} (type: ${typeof value})`);

            // Ensure numeric values are stored as numbers
            if (field === "IngredientsYOffset" || field === "IngredientsFontSize" ||
                field === "InstructionsFontSize" || field === "NoteFontSize" ||
                field === "IconX" || field === "IconY" || field === "IconSize" ||
                field === "FrontLabelNameXOffset") {
                // Convert to number if it's not already
                if (typeof value !== 'number') {
                    value = Number(value);
                }
            }

            // Set the value
            (settingsObject as any)[field] = value;

            // Log the updated value
            console.log(`${field} is now ${(settingsObject as any)[field]} (type: ${typeof (settingsObject as any)[field]})`);
        }
    }

    // Calc nutritional values for all Variation combinations
    calcAllNutVals() {
        this.Ex_ProductVariationsData.forEach(data => {
            // If ProductVariationIds is null, this is a product-only record
            if (data.ProductVariationIds === null) {
                // Use the product's settings and ingredients
                let ingredients = this.Ex_Ingredients ?? [];

                // Calculate nutritional values
                data.calcNutVals(this.Ex_Settings.WeightChange || 1, ingredients);

                // Set NutValsEnabled from product settings
                data.Ex_NutValsEnabled = this.Ex_Settings.NutValsEnabled;
            } else {
                // Get settings for servings and ingredients
                let servingsSettings = this.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, data.ProductVariationIds);
                let ingredientsSettings = this.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, data.ProductVariationIds);

                // If there are no variations, use the product's ingredients
                // Otherwise, get the ingredients from the appropriate variation
                let ingredients: ItemIngredientModel[] = [];
                if (this.Ex_Variations.length === 0 || this.getVariationCategories().length === 0) {
                    ingredients = this.Ex_Ingredients ?? [];
                } else {
                    let ingredientsVar = this.getVariationForSettings(SettingsTypeEnum.Ingredients, data.ProductVariationIds);
                    ingredients = ingredientsVar?.Ex_Ingredients ?? this.Ex_Ingredients ?? [];
                }

                // Calculate nutritional values
                data.calcNutVals(servingsSettings.WeightChange || 1, ingredients);

                // Set NutValsEnabled from ingredients settings
                data.Ex_NutValsEnabled = ingredientsSettings.NutValsEnabled;
            }
        });
    }

    // Calc nutritional values for only selected Variation combination
    calcNutValsForSelected(selectedVariation1?: ProductVariationModel, selectedVariation2?: ProductVariationModel) {
        let selectedIds: string[] = [];
        if (selectedVariation1) selectedIds.push(selectedVariation1.Id);
        if (selectedVariation2) selectedIds.push(selectedVariation2.Id);

        // Get variations data
        let selectedVarData = this.getVarDataForSelections(selectedVariation1, selectedVariation2);

        if (selectedVarData) {
            // If no variations are selected (product-only data)
            if (selectedIds.length === 0 || selectedVarData.ProductVariationIds === null) {
                // Use the product's settings and ingredients
                let ingredients = this.Ex_Ingredients ?? [];

                // Calculate nutritional values
                selectedVarData.calcNutVals(this.Ex_Settings.WeightChange || 1, ingredients);

                // Set NutValsEnabled from product settings
                selectedVarData.Ex_NutValsEnabled = this.Ex_Settings.NutValsEnabled === true;
            } else {
                // Get settings for servings and ingredients
                let servingsSettings = this.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, selectedIds);
                let ingredientsSettings = this.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);

                // If there are no variations, use the product's ingredients
                // Otherwise, get the ingredients from the appropriate variation
                let ingredients: ItemIngredientModel[] = [];
                if (this.Ex_Variations.length === 0 || this.getVariationCategories().length === 0) {
                    ingredients = this.Ex_Ingredients ?? [];
                } else {
                    let ingredientsVar = this.getVariationForSettings(SettingsTypeEnum.Ingredients, selectedIds);
                    ingredients = ingredientsVar?.Ex_Ingredients ?? this.Ex_Ingredients ?? [];
                }

                // Calculate nutritional values
                selectedVarData.calcNutVals(servingsSettings.WeightChange || 1, ingredients);

                // Set NutValsEnabled from ingredients settings
                selectedVarData.Ex_NutValsEnabled = ingredientsSettings.NutValsEnabled === true;
            }

            // Create a new instance of the variations data to ensure React detects the change
            const index = this.Ex_ProductVariationsData.findIndex(vd =>
                (vd.ProductVariationIds === null && selectedVarData.ProductVariationIds === null) ||
                (vd.ProductVariationIds !== null && selectedVarData.ProductVariationIds !== null &&
                 JC_Utils.guidArraysEqual(vd.ProductVariationIds, selectedVarData.ProductVariationIds))
            );

            if (index !== -1) {
                // Create a new instance with the updated values
                this.Ex_ProductVariationsData[index] = new ProductVariationsDataModel(selectedVarData);
            }
        }
    }


    // - ---------------- - //
    // - UTILS: SELECTING - //
    // - ---------------- - //

    // Get first variation for each category
    getFirstOfEachCategory() : ProductVariationModel[] {
        const result: ProductVariationModel[] = [];
        this.getVariationCategories()?.forEach(c => {
            const firstVariation = this.Ex_Variations.find(v => v.VariationCategoryCode == c.Code && !v.Deleted);
            if (firstVariation) result.push(firstVariation);
        });
        return result;
    }


    // --------------- //
    // - UTILS: EDIT - //
    // --------------- //

    // Returns a copy of the product with only child items that have UI_HasChanges = true
    getOnlyChanged(): ProductModel {
        console.log(`getOnlyChanged for product ${this.Name} (${this.Id})`);

        // Create a new instance of the product with all its properties
        const changedProduct = new ProductModel(this);

        // Filter variations with UI_HasChanges = true
        changedProduct.Ex_Variations = this.Ex_Variations
            .filter(variation => variation.UI_HasChanges || variation.Deleted)
            .map(variation => new ProductVariationModel(variation));
        console.log(`Including ${changedProduct.Ex_Variations.length} variations with changes`);

        // Filter ProductVariationsData with UI_HasChanges = true
        changedProduct.Ex_ProductVariationsData = this.Ex_ProductVariationsData
            .filter(varData => varData.UI_HasChanges || varData.Deleted)
            .map(varData => new ProductVariationsDataModel(varData));
        console.log(`Including ${changedProduct.Ex_ProductVariationsData.length} variation data records with changes`);

        // Log all ingredients before filtering
        console.log(`Product has ${this.Ex_Ingredients.length} total ingredients:`,
            this.Ex_Ingredients.map(ing => ({
                id: ing.Id,
                name: ing.Ex_Ingredient?.Name,
                deleted: ing.Deleted,
                hasChanges: ing.UI_HasChanges
            }))
        );

        // Filter ingredients with UI_HasChanges = true
        changedProduct.Ex_Ingredients = this.Ex_Ingredients
            .filter(ingredient => ingredient.UI_HasChanges || ingredient.Deleted)
            .map(ingredient => new ItemIngredientModel(ingredient));

        console.log(`Including ${changedProduct.Ex_Ingredients.length} ingredients with changes:`,
            changedProduct.Ex_Ingredients.map(ing => ({
                id: ing.Id,
                name: ing.Ex_Ingredient?.Name,
                deleted: ing.Deleted,
                hasChanges: ing.UI_HasChanges
            }))
        );

        // Log the settings state for debugging
        console.log(`Product ${this.Name} settings:`, {
            hasChanges: this.Ex_Settings?.UI_HasChanges,
            ingredientsYOffset: this.Ex_Settings?.IngredientsYOffset,
            productId: this.Ex_Settings?.ProductId,
            settingsId: this.Ex_Settings?.Id
        });

        // Filter settings with UI_HasChanges = true
        if (this.Ex_Settings?.UI_HasChanges) {
            changedProduct.Ex_Settings = new ItemSettingsModel(this.Ex_Settings);

            // Ensure numeric values are stored as numbers
            changedProduct.Ex_Settings.IngredientsYOffset = Number(changedProduct.Ex_Settings.IngredientsYOffset);
            changedProduct.Ex_Settings.IngredientsFontSize = Number(changedProduct.Ex_Settings.IngredientsFontSize);
            changedProduct.Ex_Settings.InstructionsFontSize = Number(changedProduct.Ex_Settings.InstructionsFontSize);
            changedProduct.Ex_Settings.NoteFontSize = Number(changedProduct.Ex_Settings.NoteFontSize);
            changedProduct.Ex_Settings.IconX = Number(changedProduct.Ex_Settings.IconX);
            changedProduct.Ex_Settings.IconY = Number(changedProduct.Ex_Settings.IconY);
            changedProduct.Ex_Settings.IconSize = Number(changedProduct.Ex_Settings.IconSize);
            changedProduct.Ex_Settings.FrontLabelNameXOffset = Number(changedProduct.Ex_Settings.FrontLabelNameXOffset);

            // Log the settings being included
            console.log(`Including settings in save:`, {
                ingredientsYOffset: changedProduct.Ex_Settings.IngredientsYOffset,
                type: typeof changedProduct.Ex_Settings.IngredientsYOffset
            });
        } else {
            changedProduct.Ex_Settings = new ItemSettingsModel();
            console.log(`No settings changes to save`);
        }

        // Return the filtered product
        return changedProduct;
    }

    addVariation(variation:ProductVariationModel) {
        // Set the sort order based on the highest existing sort order + 1
        // If there are no variations yet, start with sort order 1
        if (this.Ex_Variations.length > 0) {
            variation.SortOrder = Math.max(...this.Ex_Variations.map(v => v.SortOrder)) + 1;
        } else {
            variation.SortOrder = 1;
        }

        this.Ex_Variations.push(variation);
        this.Ex_Variations.sort((a:ProductVariationModel, b:ProductVariationModel) => a.SortOrder > b.SortOrder ? 1 : -1);
    }

    removeVariation(variation:ProductVariationModel) {
        this.Ex_Variations = this.Ex_Variations.filter(v => v.Id != variation.Id);
        this.Ex_ProductVariationsData = this.Ex_ProductVariationsData.filter(d =>
            // Keep product-only records (ProductVariationIds is null)
            d.ProductVariationIds === null ||
            // Filter out records containing the variation ID
            !JC_Utils.stringInListOfStrings(variation.Id, d.ProductVariationIds)
        );
    }

}
