export interface JC_FieldOption {
    OptionId: string;
    Label: string;
    IconName?: string;
    Selected?: boolean;
    Disabled?: boolean;
    Metadata?: any;
}

import { LabelSizeEnum } from "@/app/enums/LabelSize";

export function D_FieldOption_LabelSizes(): JC_FieldOption[] {
    return [{
        OptionId: LabelSizeEnum.Large,
        Label: "Large"
    },{
        OptionId: LabelSizeEnum.Small,
        Label: "Small"
    },{
        OptionId: LabelSizeEnum.TwoByFive,
        Label: "2X5"
    }];
}