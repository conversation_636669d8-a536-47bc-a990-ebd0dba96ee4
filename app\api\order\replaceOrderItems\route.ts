import { NextRequest, NextResponse } from "next/server";
import { OrderItemModel } from "@/app/models/OrderItem";
import { OrderBusiness } from "../business";

export async function POST(request: NextRequest) {
    
    try {
        const { orderId, newList } = await request.json() as { orderId:string, newList:OrderItemModel[] };
        await OrderBusiness.ReplaceOrderItems(orderId, newList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}