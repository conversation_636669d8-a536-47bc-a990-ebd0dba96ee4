SELECT "attribute"."Id"
      ,"attribute"."ProductId"
      ,"product"."Name" "__Product"
      ,"attribute"."DietaryAttributeCode"
      ,"dietary"."Description" "__DietaryAttribute"
FROM public."ProductDietaryAttribute" "attribute"
INNER JOIN public."Product" "product" ON "attribute"."ProductId" = "product"."Id"
INNER JOIN public."DietaryAttribute" "dietary" ON "attribute"."DietaryAttributeCode" = "dietary"."Code"
ORDER BY "product"."SortOrder", "product"."Name", "dietary"."SortOrder";