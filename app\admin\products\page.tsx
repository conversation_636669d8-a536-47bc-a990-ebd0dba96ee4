"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import J<PERSON>_Spinner from "../../components/JC_Spinner/JC_Spinner";
import JC_Dropdown from "@/app/components/JC_Dropdown/JC_Dropdown";
import J<PERSON>_<PERSON> from "@/app/components/JC_Field/JC_Field";
import <PERSON><PERSON>_<PERSON> from "@/app/components/JC_Button/JC_Button";
import JC_ModalConfirmation from "@/app/components/JC_ModalConfirmation/JC_ModalConfirmation";
import JC_Checkbox from "@/app/components/JC_Checkbox/JC_Checkbox";
import JC_LabelFront from "@/app/components/JC_LabelFront/JC_LabelFront";
import JC_LabelBack from "@/app/components/JC_LabelBack/JC_LabelBack";
import NewProductModal from "./components/NewProductModal/NewProductModal";
import NewVariationModal from "./components/NewVariationModal/NewVariationModal";
import EditVariationCategoryModal from "./components/EditVariationCategoryModal/EditVariationCategoryModal";
import { JC_GetList } from "@/app/services/JC_GetList";
import { JC_Post } from "@/app/services/JC_Post";
import { GetIngredients } from "@/app/services/Ingredient";
import { IngredientModel } from "@/app/models/Ingredient";
import { ItemIngredientModel } from "@/app/models/ItemIngredient";

import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { VariationCategoryModel } from "@/app/models/VariationCategory";
import { JC_ConfirmationModalUsageModel } from "@/app/models/ComponentModels/JC_ConfirmationModalUsage";
import { DropdownTypeEnum } from "@/app/enums/DropdownType";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";
import { SettingsTypeEnum } from "@/app/enums/VariationSettings";
import { LabelSizeEnum } from "@/app/enums/LabelSize";
import { JC_Utils, JC_Utils_CSS, JC_Utils_CK } from "@/app/Utils";
import { JC_FieldOption } from "@/app/models/ComponentModels/JC_FieldOption";

// CopyOtherMode component
const CopyOtherMode: React.FC<{
    selectedCopyOtherVariation: ProductVariationModel | null;
    selectedCopyOtherProduct: ProductModel | null;
    submitCopyOther: () => void;
    cancelCopyOther: () => void;
    originalSelectedProduct: ProductModel | null;
    originalSelectedVariation1: ProductVariationModel | null;
    originalSelectedVariation2: ProductVariationModel | null;
}> = ({
    selectedCopyOtherVariation,
    selectedCopyOtherProduct,
    submitCopyOther,
    cancelCopyOther,
    originalSelectedProduct,
    originalSelectedVariation1,
    originalSelectedVariation2
}) => {
    // Determine what name to display
    let displayName = "-";
    if (selectedCopyOtherVariation) {
        // If we have a selected variation, use its name
        displayName = `"${selectedCopyOtherVariation.Name}"`;
    } else if (selectedCopyOtherProduct) {
        // If we have a selected product but no variation, use the product name
        // This happens when the selected product has no variations
        displayName = `"${selectedCopyOtherProduct.Name}"`;
    }

    // Determine if the submit button should be disabled
    // It should be disabled if:
    // 1. We don't have a selected product, OR
    // 2. The selected product has variations AND we don't have a selected variation, OR
    // 3. The selected Copy Other product is the same as the original selected product AND we're trying to copy the same variation
    const hasVariations = selectedCopyOtherProduct ?
        (selectedCopyOtherProduct.Ex_Variations.filter((v: ProductVariationModel) => !v.Deleted).length > 0 &&
         selectedCopyOtherProduct.getVariationCategories().length > 0) : false;

    // Separate conditions for better readability
    const noProductSelected = !selectedCopyOtherProduct;

    const hasVariationsButNoVariationSelected = hasVariations && !selectedCopyOtherVariation;

    const isSameProduct = selectedCopyOtherProduct?.Id === originalSelectedProduct?.Id;

    const originalHasNoVariations = originalSelectedProduct &&
        (originalSelectedProduct.Ex_Variations.filter(v => !v.Deleted).length === 0 ||
         originalSelectedProduct.getVariationCategories().length === 0);

    const bothProductsHaveNoVariations = !hasVariations && originalHasNoVariations;

    const sameVariationSelected = selectedCopyOtherVariation &&
        (selectedCopyOtherVariation.Id === originalSelectedVariation1?.Id ||
         selectedCopyOtherVariation.Id === originalSelectedVariation2?.Id);

    const sameProductAndSameSelection = isSameProduct && (bothProductsHaveNoVariations || sameVariationSelected);

    const isSubmitDisabled = Boolean(
        noProductSelected ||
        hasVariationsButNoVariationSelected ||
        sameProductAndSameSelection
    );

    return (
        <div className={styles.copyOtherInfoContainer}>
            <div>Copy:</div>
            <div>{displayName}</div>
            <JC_Button
                text="Submit"
                isSecondary
                onClick={submitCopyOther}
                isDisabled={isSubmitDisabled}
            />
            <JC_Button
                text="Cancel"
                isSecondary
                onClick={cancelCopyOther}
            />
        </div>
    );
};



export default function Page_Products() {

    // - ----- - //
    // - STATE - //
    // - ----- - //

    // Main
    const [initialised, setInitialised] = useState<boolean>(false);
    const [products, setProducts] = useState<ProductModel[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<ProductModel|null>(null);
    const [selectedProductOriginal, setSelectedProductOriginal] = useState<ProductModel>();
    const [newProductModalOpen, setNewProductModalOpen] = useState<boolean>(false);
    const [newVariationModalOpen, setNewVariationModalOpen] = useState<boolean>(false);
    const [editVariationCategoryModalOpen, setEditVariationCategoryModalOpen] = useState<boolean>(false);
    const [currentVariationCategory, setCurrentVariationCategory] = useState<string>("");
    function resetChangeDetection(product?:ProductModel) { setSelectedProductOriginal(new ProductModel(product??selectedProduct??undefined)); }
    function forceSetSelectedProduct(product:ProductModel) {
        // Create a deep copy of the product to avoid reference issues
        const newProduct = new ProductModel(product);

        // Ensure we create new instances of all child objects to trigger React re-renders
        if (newProduct.Ex_ProductVariationsData) {
            newProduct.Ex_ProductVariationsData = newProduct.Ex_ProductVariationsData.map(
                vd => new ProductVariationsDataModel(vd)
            );
        }

        // Update the selected product state
        setSelectedProduct(newProduct);

        // Also update the product in the products array to ensure changes are reflected immediately
        setProducts(prevProducts => {
            return prevProducts.map(p => {
                if (p.Id === product.Id) {
                    // Return a new instance of the product with the updated values
                    const updatedProduct = new ProductModel(product);

                    // Also ensure we create new instances of all child objects in the products array
                    if (updatedProduct.Ex_ProductVariationsData) {
                        updatedProduct.Ex_ProductVariationsData = updatedProduct.Ex_ProductVariationsData.map(
                            vd => new ProductVariationsDataModel(vd)
                        );
                    }

                    return updatedProduct;
                }
                return p;
            });
        });
    }

    // No longer needed: const [forSave_DeletedProducts, setForSave_DeletedProducts] = useState<ProductModel[]>([]);

    // Function to scroll to the selected product only if it's near the edge of the view
    function scrollToSelectedProduct() {
        if (selectedProduct) {
            const productElement = document.getElementById(`product-tile-${selectedProduct.Id}`);
            const tilesContainer = document.querySelector(`.${styles.productTilesContainer}`);

            if (productElement && tilesContainer) {
                // Calculate the position to scroll to
                const containerRect = tilesContainer.getBoundingClientRect();
                const elementRect = productElement.getBoundingClientRect();

                // Define a threshold for what "near the edge" means (20% of container height)
                const threshold = containerRect.height * 0.2;

                // Calculate how far the element is from the top and bottom edges of the container
                const distanceFromTop = elementRect.top - containerRect.top;
                const distanceFromBottom = containerRect.bottom - elementRect.bottom;

                // Only scroll if the element is near the edge or outside the visible area
                if (distanceFromTop < threshold || distanceFromBottom < threshold ||
                    distanceFromTop < 0 || distanceFromBottom < 0) {

                    // Calculate the scroll position to center the element in the container
                    const scrollTop = elementRect.top - containerRect.top - (containerRect.height / 2) + (elementRect.height / 2) + tilesContainer.scrollTop;

                    // Scroll the container
                    tilesContainer.scrollTo({
                        top: scrollTop,
                        behavior: 'smooth'
                    });
                }
            }
        }
    }

    // Ingredients sorting
    const [ingredientsSortConfig, setIngredientsSortConfig] = useState<{key: string, direction: 'asc' | 'desc'}>({
        key: 'Name',
        direction: 'asc'
    });
    const [userSelectedIngSort, setUserSelectedIngSort] = useState<boolean>(false);


    // Removed unused state: const [allVariationCategories, setAllVariationCategories] = useState<VariationCategoryModel[]>([]);
    // Variations
    const [inVarIngEditMode, setInVarIngEditMode] = useState<boolean>(false);
    const [selectedVariation1, setSelectedVariation1] = useState<ProductVariationModel|null>(null);
    const [selectedVariation2, setSelectedVariation2] = useState<ProductVariationModel|null>(null);
    const [selectedVariationsData, setSelectedVariationsData] = useState<ProductVariationsDataModel|null>(null);
    // Ingredients
    const [allIngredients, setAllIngredients] = useState<IngredientModel[]>([]);
    // Confirmation
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel|null>();
    // Copy Other
    const [inCopyOtherMode, setInCopyOtherMode] = useState<boolean>(false);
    const [selectedCopyOtherProduct, setSelectedCopyOtherProduct] = useState<ProductModel|null>(null);
    const [selectedCopyOtherVariation, setSelectedCopyOtherVariation] = useState<ProductVariationModel|null>(null);


    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    // Ensure at least one variation for each category is selected before getting settings
    if (selectedProduct) {
        const variationCategories = selectedProduct.getVariationCategories();
        const firstVariations = selectedProduct.getFirstOfEachCategory();

        // Always ensure we have the first variation selected if available
        if (firstVariations.length > 0 && !selectedVariation1) {
            setSelectedVariation1(firstVariations[0]);
        }

        // Only set the second variation if there are multiple categories and we don't have it selected yet
        if (variationCategories.length > 1 && firstVariations.length > 1 && !selectedVariation2) {
            setSelectedVariation2(firstVariations[1]);
        }
    }

    // Get the selected variation IDs
    const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

    // Get item settings for different settings types
    let itemSettings = {
        labelSize: selectedProduct?.getItemSettingsForSettingsType(SettingsTypeEnum.LabelSize, selectedIds),
        label: selectedProduct?.getItemSettingsForSettingsType(SettingsTypeEnum.Labels, selectedIds),
        ingredients: selectedProduct?.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds),
        servings: selectedProduct?.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, selectedIds)
    };

    // Helper function to get the variation for a specific settings type is defined above

    useEffect(() => {
        JC_Utils_CSS.forceRootOverflowYHidden(styles);
        initialise();
    }, []);

    // Update selectedVariationsData whenever selectedProduct, selectedVariation1, or selectedVariation2 changes
    useEffect(() => {
        if (selectedProduct) {
            const varData = selectedProduct.getVarDataForSelections(
                selectedVariation1 || undefined,
                selectedVariation2 || undefined
            );
            setSelectedVariationsData(varData || null);
        } else {
            setSelectedVariationsData(null);
        }
    }, [selectedProduct, selectedVariation1, selectedVariation2]);

    // Scroll to selected product when component is initialized
    useEffect(() => {
        if (initialised && selectedProduct) {
            // Use setTimeout to ensure the DOM has updated
            setTimeout(() => {
                scrollToSelectedProduct();
            }, 100);
        }
    }, [initialised, selectedProduct?.Id]);

    // Save selections and printing product
    useEffect(() => {
        JC_Utils_CSS.forceHideHeaderFooter(styles);

        // Ensure at least one variation for each category is selected whenever selectedProduct changes
        if (selectedProduct) {
            const variationCategories = selectedProduct.getVariationCategories();
            const firstVariations = selectedProduct.getFirstOfEachCategory();

            // Always ensure we have the first variation selected if available
            if (firstVariations.length > 0 && !selectedVariation1) {
                setSelectedVariation1(firstVariations[0]);
            }

            // Only set the second variation if there are multiple categories and we don't have it selected yet
            if (variationCategories.length > 1 && firstVariations.length > 1 && !selectedVariation2) {
                setSelectedVariation2(firstVariations[1]);
            }
        }

        if (initialised) {
            // Save selections and printing product every time
            localStorage.setItem(
                LocalStorageKeyEnum.JC_ProductPageSelections,
                JSON.stringify({
                    productId: selectedProduct?.Id,
                    variationIds: [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean)
                })
            );
            // Save product and selected variations to localStorage to use on printing page
            // Include selectedVariation1, selectedVariation2, and selectedVariationsData in the JC_ProductToPrint object
            const productToPrint = {
                ...selectedProduct,
                selectedVariation1: selectedVariation1,
                selectedVariation2: selectedVariation2,
                selectedVariationsData: selectedVariationsData
            };
            localStorage.setItem(LocalStorageKeyEnum.JC_ProductToPrint, JSON.stringify(productToPrint));
        }
        // Show "Leave site?" when user attempting to close tab/window and there are changes
        window.onbeforeunload = function() {
            if (productHasChanges()) {
                return `There are unsaved changes!\nContinue to discard these changes.`; // Doesn't show this text, ah well
            }
            return null;
        }
    }, [selectedProduct, selectedVariation1, selectedVariation2]);


    // - ---------- - //
    // - INITIALISE - //
    // - ---------- - //

    // Helper function to select the first available non-deleted product
    function selectFirstAvailableProduct(productList: ProductModel[]) {
        // Get the first non-deleted product sorted by SortOrder
        const nonDeletedProducts = productList.filter(p => !p.Deleted);
        if (nonDeletedProducts.length > 0) {
            const firstProduct = nonDeletedProducts.sort((a, b) => a.SortOrder - b.SortOrder)[0];

            // Select the first variation of each category
            const variationCategories = firstProduct.getVariationCategories();
            const firstVariations = firstProduct.getFirstOfEachCategory();

            // Always set the first variation if available
            if (firstVariations.length > 0) {
                setSelectedVariation1(firstVariations[0]);
            }

            // Only set the second variation if there are multiple categories
            if (variationCategories.length > 1 && firstVariations.length > 1) {
                setSelectedVariation2(firstVariations[1]);
            } else {
                // Clear the second variation if there's only one category
                setSelectedVariation2(null);
            }

            // Calculate nutritional values
            firstProduct.calcNutValsForSelected(
                firstVariations.length > 0 ? firstVariations[0] : undefined,
                firstVariations.length > 1 ? firstVariations[1] : undefined
            );

            setSelectedProduct(firstProduct);
            resetChangeDetection(firstProduct);
        } else {
            // No non-deleted products available
            setSelectedProduct(null);
            setSelectedVariation1(null);
            setSelectedVariation2(null);
        }
    }

    function initialise() {
        // Reset initialised flag at the beginning of initialization
        setInitialised(false);

        // Get Ingredients
        GetIngredients().then(list => setAllIngredients(list));

        // Get Products
        JC_GetList<ProductModel>("product", { includeAll: true }, ProductModel).then(productList => {
            // Keep all products in state, including deleted ones
            setProducts(productList);

            // Set selections from localStorage
            let selectionsObj = localStorage.getItem(LocalStorageKeyEnum.JC_ProductPageSelections);
            if (selectionsObj != null) {
                let localStorageSelections = JSON.parse(selectionsObj);
                let product = productList.find(p => p.Id == localStorageSelections["productId"] && !p.Deleted);

                // Check if the product exists and is not deleted
                if (product != null) {
                    // Find the variations from the saved IDs
                    const savedVariationIds = localStorageSelections["variationIds"] as string[];
                    const savedVariations = product.Ex_Variations.filter(v => savedVariationIds.includes(v.Id));

                    // Set the selected variations
                    if (savedVariations.length > 0) setSelectedVariation1(savedVariations[0]);
                    if (savedVariations.length > 1) setSelectedVariation2(savedVariations[1]);

                    // If we don't have enough variations selected, select the first of each category
                    const variationCategories = product.getVariationCategories();
                    if (savedVariations.length < variationCategories.length) {
                        const firstVariations = product.getFirstOfEachCategory();

                        // Always ensure we have the first variation selected if available
                        if (!selectedVariation1 && firstVariations.length > 0) {
                            setSelectedVariation1(firstVariations[0]);
                        }

                        // Only set the second variation if there are multiple categories
                        if (variationCategories.length > 1 && !selectedVariation2 && firstVariations.length > 1) {
                            setSelectedVariation2(firstVariations[1]);
                        }
                    }

                    setSelectedProduct(product);
                    resetChangeDetection(product);
                } else {
                    // If the product doesn't exist or is deleted, select the first non-deleted product
                    selectFirstAvailableProduct(productList);
                }
            } else if (productList.length > 0) {
                // Select the first available product
                selectFirstAvailableProduct(productList);
            } else {
                // No products available
                setSelectedProduct(null);
                setSelectedVariation1(null);
                setSelectedVariation2(null);
            }

            setTimeout(() => setInitialised(true), 100);
        });

        // Variation Categories - no longer needed
        // JC_GetList<VariationCategoryModel>(VariationCategoryModel.apiRoute, {}, VariationCategoryModel).then(list => {
        //     setAllVariationCategories(list);
        // });
    }


    // - ------------ - //
    // - CONFIRMATION - //
    // - ------------ - //

    function productHasChanges() {
        // Check for changes in any product in the products list
        if (products.some(product => product.UI_HasChanges || product.Deleted)) {
            return true;
        }

        // Check for changes in the selected product
        if (!selectedProduct) {
            return false;
        }

        // Make sure variations in same order
        selectedProduct.Ex_Variations = selectedProduct.Ex_Variations.sort((a,b) => a.Id > b.Id ? 1 : -1);
        selectedProductOriginal!.Ex_Variations = selectedProductOriginal!.Ex_Variations.sort((a,b) => a.Id > b.Id ? 1 : -1);
        return JSON.stringify(selectedProduct) != JSON.stringify(selectedProductOriginal);
    }

    // Group or Product change
    function confirmProdChange(onOkCallback:()=>void) {
        // No longer checking for unsaved changes here, just run the callback
        onOkCallback();
    }


    // - --------- - //
    // - NEW ITEMS - //
    // - --------- - //

    // Open new product modal
    function openNewProductModal() {
        setNewProductModalOpen(true);
    }

    // Open new variation modal
    function openNewVariationModal(categoryCode: string) {
        setCurrentVariationCategory(categoryCode);
        setNewVariationModalOpen(true);
    }

    // Open edit variation category modal
    function openEditVariationCategoryModal(categoryCode: string) {
        setCurrentVariationCategory(categoryCode);
        setEditVariationCategoryModalOpen(true);
    }

    // Handle variation category change
    function handleVariationCategoryChange(oldCategoryCode: string, newCategoryCode: string, newCategory: VariationCategoryModel) {
        if (!selectedProduct || oldCategoryCode === newCategoryCode) return;

        // Get all variations in the old category
        const variationsToUpdate = selectedProduct.Ex_Variations.filter(
            v => !v.Deleted && v.VariationCategoryCode === oldCategoryCode
        );

        // Update each variation's category
        variationsToUpdate.forEach(variation => {
            variation.VariationCategoryCode = newCategoryCode;
            variation.Ex_Category = newCategory;
            variation.UI_HasChanges = true;
        });

        // Mark the product as having changes
        selectedProduct.UI_HasChanges = true;

        // Force re-render
        forceSetSelectedProduct(selectedProduct);

        // Close the modal
        setEditVariationCategoryModalOpen(false);
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    // Delete Product
    function deleteProduct() {
        if (!selectedProduct) return;

        // Show confirmation modal
        setConfirmationModalData({
            title: "Delete Product",
            text: `Are you sure you want to delete "${selectedProduct.Name}"?`,
            submitButtons: [{
                text: "Delete",
                onSubmit: () => {
                    // Set the product's Deleted property to true
                    selectedProduct.Deleted = true;
                    forceSetSelectedProduct(selectedProduct);

                    // Update the product in the products list but don't filter out deleted products
                    const updatedProducts = products.map(p => {
                        if (p.Id === selectedProduct.Id) {
                            p.Deleted = true;
                        }
                        return p;
                    });
                    setProducts(updatedProducts);

                    // Select the next non-deleted product in the list, or the previous one if it was the last
                    const nonDeletedProducts = updatedProducts.filter(p => !p.Deleted);
                    if (nonDeletedProducts.length > 0) {
                        const currentIndex = products.findIndex(p => p.Id === selectedProduct.Id);
                        // Find the next non-deleted product
                        let nextProduct = null;
                        // Try to find a product after the current one
                        for (let i = currentIndex + 1; i < products.length; i++) {
                            if (!products[i].Deleted) {
                                nextProduct = products[i];
                                break;
                            }
                        }
                        // If no product found after, try to find one before
                        if (!nextProduct) {
                            for (let i = currentIndex - 1; i >= 0; i--) {
                                if (!products[i].Deleted) {
                                    nextProduct = products[i];
                                    break;
                                }
                            }
                        }
                        if (nextProduct) {
                            selectProduct(nextProduct, true);
                        } else {
                            setSelectedProduct(null);
                            setSelectedVariation1(null);
                            setSelectedVariation2(null);
                        }
                    } else {
                        setSelectedProduct(null);
                        setSelectedVariation1(null);
                        setSelectedVariation2(null);
                    }

                    // Close the confirmation modal
                    setConfirmationModalData(null);
                }
            }]
        });
    }

    // Delete Option
    function deleteVariation(variation: ProductVariationModel) {
        if (!selectedProduct || !variation) return;

        // Show confirmation modal
        setConfirmationModalData({
            title: "Delete Option",
            text: `Are you sure you want to delete "${variation.Name}"?`,
            submitButtons: [{
                text: "Delete",
                onSubmit: () => {
                    // Use the new deleteVariation method from the Product model
                    selectedProduct.deleteVariation(variation.Id);

                    // If the deleted variation was selected, clear the selection
                    if (selectedVariation1?.Id === variation.Id) {
                        setSelectedVariation1(null);
                    }
                    if (selectedVariation2?.Id === variation.Id) {
                        setSelectedVariation2(null);
                    }

                    // Check if there are any non-deleted variations left
                    const nonDeletedVariations = selectedProduct.Ex_Variations.filter(v => !v.Deleted);
                    if (nonDeletedVariations.length === 0) {
                        // If no variations left, set selectedVariation1 and selectedVariation2 to null to view product settings
                        setSelectedVariation1(null);
                        setSelectedVariation2(null);
                    } else {
                        // Check if there are still multiple variation categories
                        const remainingCategories = selectedProduct.getVariationCategories();

                        // If there's no longer a second variation category, set selectedVariation2 to null
                        if (remainingCategories.length <= 1) {
                            setSelectedVariation2(null);
                        }

                        // If selectedVariation1 was cleared, select the first variation of the first category
                        if (!selectedVariation1) {
                            const firstVariations = selectedProduct.getFirstOfEachCategory();
                            if (firstVariations.length > 0) {
                                setSelectedVariation1(firstVariations[0]);
                            }
                        }

                        // Check if selectedVariation1 and selectedVariation2 are from the same category
                        // This can happen if we deleted a variation and the selection logic above selected two from the same category
                        if (selectedVariation1 && selectedVariation2 &&
                            selectedVariation1.VariationCategoryCode === selectedVariation2.VariationCategoryCode) {

                            // Keep selectedVariation1 and try to find a variation from a different category for selectedVariation2
                            const differentCategoryVariations = nonDeletedVariations.filter(
                                v => v.VariationCategoryCode !== selectedVariation1.VariationCategoryCode
                            );

                            if (differentCategoryVariations.length > 0) {
                                // If we have variations from a different category, select the first one
                                setSelectedVariation2(differentCategoryVariations[0]);
                            } else {
                                // If all remaining variations are from the same category, clear selectedVariation2
                                setSelectedVariation2(null);
                            }
                        }
                    }

                    // Update the product
                    forceSetSelectedProduct(selectedProduct);

                    // Explicitly update selectedVariationsData based on the new state
                    // This is redundant with the useEffect, but ensures immediate consistency
                    const remainingCategories = selectedProduct.getVariationCategories();
                    const updatedVarData = selectedProduct.getVarDataForSelections(
                        nonDeletedVariations.length === 0 ? undefined :
                            (selectedVariation1?.Id === variation.Id ? null : selectedVariation1) || undefined,
                        nonDeletedVariations.length === 0 || remainingCategories.length <= 1 ? undefined :
                            (selectedVariation2?.Id === variation.Id ? null : selectedVariation2) || undefined
                    );
                    setSelectedVariationsData(updatedVarData || null);

                    // Close the confirmation modal
                    setConfirmationModalData(null);
                }
            }]
        });
    }

    // - ------ - //
    // - SELECT - //
    // - ------ - //

    // Product
    function selectProduct(product:ProductModel, force?:boolean) {
        if (!inCopyOtherMode && product.Id == selectedProduct?.Id) {
            return;
        }
        if (inCopyOtherMode) {
            console.log(`Selecting product in Copy Other mode: ${product.Name} (ID: ${product.Id})`);

            // Create a deep copy of the product to avoid reference issues
            const productCopy = new ProductModel(product);

            // Always set the selected copy other product to the clicked product
            setSelectedCopyOtherProduct(productCopy);

            // If we're working with a product with variations
            if (productCopy.Ex_Variations.filter(v => !v.Deleted).length > 0 && productCopy.getVariationCategories().length > 0) {
                // Find the variation that has ingredients settings
                const ingredientsVar = productCopy.getVariationForSettings(SettingsTypeEnum.Ingredients);

                // If we found a variation with ingredients settings, use it
                if (ingredientsVar) {
                    console.log(`Selected product in Copy Other mode - Using variation with ingredients settings: ${ingredientsVar.Name}`);
                    setSelectedCopyOtherVariation(ingredientsVar);
                } else {
                    // Otherwise, use the first variation
                    const firstVariation = productCopy.Ex_Variations.filter(v => !v.Deleted)[0];
                    console.log(`Selected product in Copy Other mode - Using first variation: ${firstVariation.Name}`);
                    setSelectedCopyOtherVariation(firstVariation);
                }
            } else {
                // For products without variations, set selectedCopyOtherVariation to null
                console.log(`Selected product in Copy Other mode - Product has no variations, setting selectedCopyOtherVariation to null`);
                setSelectedCopyOtherVariation(null);
            }
        } else {
            let callback = () => {
                product.Ex_Variations = product.Ex_Variations.sort((a,b) => a.SortOrder > b.SortOrder ? 1 : -1);

                // Select the first variation of each category
                const variationCategories = product.getVariationCategories();
                const firstVariations = product.getFirstOfEachCategory();

                // Always set the first variation if available
                if (firstVariations.length > 0) {
                    setSelectedVariation1(firstVariations[0]);
                } else {
                    setSelectedVariation1(null);
                }

                // Only set the second variation if there are multiple categories
                if (variationCategories.length > 1 && firstVariations.length > 1) {
                    setSelectedVariation2(firstVariations[1]);
                } else {
                    // Clear the second variation if there's only one category
                    setSelectedVariation2(null);
                }

                // Calculate nutritional values
                product.calcNutValsForSelected(
                    firstVariations.length > 0 ? firstVariations[0] : undefined,
                    firstVariations.length > 1 ? firstVariations[1] : undefined
                );

                forceSetSelectedProduct(product);
                resetChangeDetection(product);
            };
            if (force) {
                callback();
            } else {
                confirmProdChange(callback);
            }
        }
    }

    // Variation
    function selectVariation(variation:ProductVariationModel) {
        // If already selected, do nothing
        if (variation.Id === selectedVariation1?.Id || variation.Id === selectedVariation2?.Id) {
            return;
        }

        if (inCopyOtherMode) {
            setSelectedCopyOtherVariation(variation);
        } else {
            confirmProdChange(() => {
                // Determine which variation slot to update based on category
                const categoryCode = variation.VariationCategoryCode;

                // If we have a first variation and it's from the same category, replace it
                if (selectedVariation1 && selectedVariation1.VariationCategoryCode === categoryCode) {
                    setSelectedVariation1(variation);
                }
                // If we have a second variation and it's from the same category, replace it
                else if (selectedVariation2 && selectedVariation2.VariationCategoryCode === categoryCode) {
                    setSelectedVariation2(variation);
                }
                // If we don't have a first variation yet, set it
                else if (!selectedVariation1) {
                    setSelectedVariation1(variation);
                }
                // If we don't have a second variation yet, set it
                else if (!selectedVariation2) {
                    // Make sure we're not selecting a variation from the same category as selectedVariation1
                    if (selectedVariation1 && selectedVariation1.VariationCategoryCode !== categoryCode) {
                        setSelectedVariation2(variation);
                    } else {
                        // If it's from the same category, replace selectedVariation1 instead
                        setSelectedVariation1(variation);
                    }
                }
                // If both slots are filled with different categories, replace the first one
                else {
                    setSelectedVariation1(variation);

                    // If the new variation is from the same category as selectedVariation2, clear selectedVariation2
                    if (selectedVariation2 && selectedVariation2.VariationCategoryCode === categoryCode) {
                        // Try to find a variation from a different category
                        const differentCategoryVariations = selectedProduct!.Ex_Variations.filter(
                            v => !v.Deleted &&
                            v.VariationCategoryCode !== categoryCode &&
                            v.Id !== variation.Id
                        );

                        if (differentCategoryVariations.length > 0) {
                            // If we have variations from a different category, select the first one
                            setSelectedVariation2(differentCategoryVariations[0]);
                        } else {
                            // If all remaining variations are from the same category, clear selectedVariation2
                            setSelectedVariation2(null);
                        }
                    }
                }

                // Determine which variations to use for calculation
                let var1: ProductVariationModel | undefined = undefined;
                let var2: ProductVariationModel | undefined = undefined;

                // Use the updated selectedVariation1 and selectedVariation2 values
                var1 = variation;

                // Only use selectedVariation2 if it's from a different category
                if (selectedVariation2 && selectedVariation2.VariationCategoryCode !== categoryCode) {
                    var2 = selectedVariation2;
                }

                // Calc nut vals with the new selections
                selectedProduct!.calcNutValsForSelected(var1, var2);

                forceSetSelectedProduct(selectedProduct!);
                resetChangeDetection();
            });
        }
    }


    // - ------ - //
    // - CHANGE - //
    // - ------ - //

    // Handle barcode selection
    function handleBarcodeSelection(newBarcodeNumber: string) {
        if (!selectedProduct || !selectedVariationsData) return;

        // If the barcode is the same as the current one, do nothing (case-insensitive comparison)
        if (newBarcodeNumber.toLowerCase() === selectedVariationsData.BarcodeNumber?.toLowerCase()) return;

        // Check if this barcode is already used by another product/variation
        let foundProduct: ProductModel | undefined;
        let foundVariation1: ProductVariationModel | undefined;
        let foundVariation2: ProductVariationModel | undefined;
        let foundVarData: ProductVariationsDataModel | undefined;

        // Search through all products to find if this barcode is already in use
        for (const product of products) {
            if (product.Ex_ProductVariationsData) {
                for (const varData of product.Ex_ProductVariationsData) {
                    // Case-insensitive comparison for barcode numbers
                    if (varData.BarcodeNumber?.toLowerCase() === newBarcodeNumber.toLowerCase() && !varData.Deleted) {
                        // Found a match - this barcode is already in use
                        foundProduct = product;
                        foundVarData = varData;

                        // Find the variations associated with this varData
                        if (varData.ProductVariationIds && varData.ProductVariationIds.length > 0) {
                            // Get the variations by their IDs (case-insensitive comparison)
                            const variations = product.Ex_Variations.filter(
                                v => varData.ProductVariationIds!.some(id =>
                                    id.toLowerCase() === v.Id.toLowerCase()
                                )
                            );

                            if (variations.length > 0) {
                                foundVariation1 = variations[0];
                            }
                            if (variations.length > 1) {
                                foundVariation2 = variations[1];
                            }
                        }
                        break;
                    }
                }
                if (foundProduct) break;
            }
        }

        // If the barcode is already in use, show confirmation modal
        if (foundProduct && foundVarData) {
            // Format the product and variation names for display
            const foundProductName = foundProduct.Name;
            const foundVariation1Name = foundVariation1 ? foundVariation1.Name : '';
            const foundVariation2Name = foundVariation2 ? foundVariation2.Name : '';

            const thisProductName = selectedProduct.Name;
            const thisVariation1Name = selectedVariation1 ? selectedVariation1.Name : '';
            const thisVariation2Name = selectedVariation2 ? selectedVariation2.Name : '';

            // Create formatted strings for the modal
            const foundItemText = [foundProductName, foundVariation1Name, foundVariation2Name]
                .filter(Boolean)
                .join(' ');

            const thisItemText = [thisProductName, thisVariation1Name, thisVariation2Name]
                .filter(Boolean)
                .join(' ');

            // Show confirmation modal
            setConfirmationModalData({
                title: "Swap Barcode",
                text: `The item "${foundItemText}" already has this barcode. Continuing will swap this "${thisItemText}" item's barcode with that item's barcode.`,
                submitButtons: [{
                    text: "Continue",
                    onSubmit: () => {
                        // Swap the barcodes
                        const currentBarcode = selectedVariationsData.BarcodeNumber;

                        // Update the current variation data
                        selectedVariationsData.BarcodeNumber = newBarcodeNumber;
                        selectedVariationsData.UI_HasChanges = true;

                        // Update the found variation data
                        foundVarData.BarcodeNumber = currentBarcode;
                        foundVarData.UI_HasChanges = true;

                        // Mark the products and variations as having changes
                        selectedProduct.UI_HasChanges = true;
                        foundProduct.UI_HasChanges = true;

                        // If we have variations, mark them as changed too
                        if (selectedVariation1) selectedVariation1.UI_HasChanges = true;
                        if (selectedVariation2) selectedVariation2.UI_HasChanges = true;
                        if (foundVariation1) foundVariation1.UI_HasChanges = true;
                        if (foundVariation2) foundVariation2.UI_HasChanges = true;

                        // Force re-render
                        forceSetSelectedProduct(selectedProduct);

                        // Close the modal
                        setConfirmationModalData(null);
                    }
                }]
            });
        } else {
            // Barcode is not in use, update directly
            selectedVariationsData.BarcodeNumber = newBarcodeNumber;
            selectedVariationsData.UI_HasChanges = true;

            // Mark the product and variations as having changes
            selectedProduct.UI_HasChanges = true;
            if (selectedVariation1) selectedVariation1.UI_HasChanges = true;
            if (selectedVariation2) selectedVariation2.UI_HasChanges = true;

            // Force re-render
            forceSetSelectedProduct(selectedProduct);
        }
    }

    // Handle ingredient sort
    function handleIngredientSort(key: string) {
        setIngredientsSortConfig((prevSortConfig) => {
            // Check if this is the default sort key and direction
            const isDefaultSort = key === 'Name' &&
                                (!userSelectedIngSort && prevSortConfig.direction === 'asc');

            // If clicking on the default sort column for the first time, keep same direction but mark as user-selected
            if (isDefaultSort) {
                setUserSelectedIngSort(true);
                return { key, direction: 'asc' as const };
            }

            // If no previous sort config or different key, sort ascending
            if (prevSortConfig.key !== key) {
                setUserSelectedIngSort(true);
                return { key, direction: 'asc' as const };
            }

            // If already sorting by this key in ascending order, switch to descending
            if (prevSortConfig.direction === 'asc') {
                setUserSelectedIngSort(true);
                return { key, direction: 'desc' as const };
            }

            // If already sorting by this key in descending order, remove sort and fall back to default
            setUserSelectedIngSort(false);
            return { key: 'Name', direction: 'asc' as const };
        });
    }

    // Sort ingredients
    function sortIngredients(ingredients: ItemIngredientModel[]) {
        if (!ingredientsSortConfig || !userSelectedIngSort) {
            // Default sort by Name
            return [...ingredients].sort((a, b) => {
                // If one is deleted and the other is not, put deleted at the end
                if (a.Deleted && !b.Deleted) return 1;
                if (!a.Deleted && b.Deleted) return -1;

                const aName = a.Ex_Ingredient?.Name || '';
                const bName = b.Ex_Ingredient?.Name || '';

                // Always put empty strings at the end
                if (aName === '' && bName !== '') return 1;
                if (aName !== '' && bName === '') return -1;

                return aName.localeCompare(bName);
            });
        }

        return [...ingredients].sort((a, b) => {
            // If one is deleted and the other is not, put deleted at the end
            if (a.Deleted && !b.Deleted) return 1;
            if (!a.Deleted && b.Deleted) return -1;

            let aValue, bValue;

            // Handle special cases for properties that are in Ex_Ingredient
            if (ingredientsSortConfig.key === 'Name' || ingredientsSortConfig.key === 'CostPer100g') {
                aValue = a.Ex_Ingredient ? (a.Ex_Ingredient as any)[ingredientsSortConfig.key] : null;
                bValue = b.Ex_Ingredient ? (b.Ex_Ingredient as any)[ingredientsSortConfig.key] : null;

                // Special handling for Name: always put empty strings at the end regardless of sort direction
                if (ingredientsSortConfig.key === 'Name') {
                    if (aValue === '' && bValue !== '') return 1;
                    if (aValue !== '' && bValue === '') return -1;
                }
            } else {
                // Access the property directly using the sort key for properties on ItemIngredientModel
                aValue = (a as any)[ingredientsSortConfig.key];
                bValue = (b as any)[ingredientsSortConfig.key];
            }

            // Handle different types of values
            if (aValue === bValue) return 0;

            // Handle null/undefined values
            if (aValue == null) return 1;
            if (bValue == null) return -1;

            // Handle different data types
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return ingredientsSortConfig.direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            }

            // Handle numbers and other comparable types
            return ingredientsSortConfig.direction === 'asc'
                ? (aValue > bValue ? 1 : -1)
                : (bValue > aValue ? 1 : -1);
        });
    }

    // Label Size
    function selectVarLabelSize(labelSize:LabelSizeEnum) {
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];
        selectedProduct!.setSettingsValue(SettingsTypeEnum.LabelSize, "LabelSize", labelSize, selectedIds);
        forceSetSelectedProduct(selectedProduct!);
    }

    // Ingredient (for Product or Variation)
    function selectIngredient(oldIngId:string, newIngId:string) {
        // Get the selected variation IDs
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

        // Use the new replaceIngredient function in the Product model
        selectedProduct!.replaceIngredient(allIngredients, oldIngId, newIngId, selectedIds);

        // Force re-render
        forceSetSelectedProduct(selectedProduct!);
    }



    // Ingredient Show % (for Product or Variation)
    function varIngShowPercToggle(ing:ItemIngredientModel) {
        ing.ShowPercent = !ing.ShowPercent;
        ing.UI_HasChanges = true;

        // Mark the product as having changes
        selectedProduct!.UI_HasChanges = true;

        // Get the selected variation IDs
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

        // If we have variations, mark the appropriate variation as changed
        if (selectedProduct!.Ex_Variations.length > 0 && selectedProduct!.getVariationCategories().length > 0) {
            // Get the settings object
            const ingredientsSettings = selectedProduct!.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);

            // If this is a variation's settings, mark the variation as changed too
            if (ingredientsSettings.ProductVariationId) {
                const variation = selectedProduct!.Ex_Variations.find(v => v.Id === ingredientsSettings.ProductVariationId);
                if (variation) {
                    variation.UI_HasChanges = true;
                }
            }
        }

        forceSetSelectedProduct(selectedProduct!);
    }

    // Ingredient To Delete (for Product or Variation)
    function varIngToDeleteToggle(ing:ItemIngredientModel) {
        // Use a temporary property for tracking deletion in the UI
        (ing as any).UI_ToDelete = !(ing as any).UI_ToDelete;

        console.log(`Toggled UI_ToDelete for ingredient ${ing.Ex_Ingredient?.Name} to ${(ing as any).UI_ToDelete}`);

        // Mark the ingredient as changed
        ing.UI_HasChanges = true;

        // Mark the product as having changes
        selectedProduct!.UI_HasChanges = true;

        // Get the selected variation IDs
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

        // If we have variations, mark the appropriate variation as changed
        if (selectedProduct!.Ex_Variations.length > 0 && selectedProduct!.getVariationCategories().length > 0) {
            // Get the settings object
            const ingredientsSettings = selectedProduct!.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);

            // If this is a variation's settings, mark the variation as changed too
            if (ingredientsSettings.ProductVariationId) {
                const variation = selectedProduct!.Ex_Variations.find(v => v.Id === ingredientsSettings.ProductVariationId);
                if (variation) {
                    variation.UI_HasChanges = true;
                }
            }
        }

        forceSetSelectedProduct(selectedProduct!);
    }


    function varEnabledOnWebsiteOnChange(variation: ProductVariationModel) {
        // Find the variation in the product's variations list and update it
        const variationIndex = selectedProduct!.Ex_Variations.findIndex(v => v.Id === variation.Id);
        if (variationIndex !== -1) {
            // Toggle the EnabledOnWebsite property on the variation in the selectedProduct
            selectedProduct!.Ex_Variations[variationIndex].EnabledOnWebsite = !selectedProduct!.Ex_Variations[variationIndex].EnabledOnWebsite;
            selectedProduct!.Ex_Variations[variationIndex].UI_HasChanges = true;
            selectedProduct!.UI_HasChanges = true;

            // Update the selected variation state if this is one of the selected variations
            if (selectedVariation1 && selectedVariation1.Id === variation.Id) {
                // Create a new reference to trigger re-render
                setSelectedVariation1(selectedProduct!.Ex_Variations[variationIndex]);
            } else if (selectedVariation2 && selectedVariation2.Id === variation.Id) {
                // Create a new reference to trigger re-render
                setSelectedVariation2(selectedProduct!.Ex_Variations[variationIndex]);
            }
        }

        // Force re-render
        forceSetSelectedProduct(selectedProduct!);
    }



    // Variation Name
    function varNameOnChange(variation: ProductVariationModel, newValue: string) {
        // Find the variation in the product's variations list and update it
        const variationIndex = selectedProduct!.Ex_Variations.findIndex(v => v.Id === variation.Id);
        if (variationIndex !== -1) {
            selectedProduct!.Ex_Variations[variationIndex].Name = newValue;
            selectedProduct!.Ex_Variations[variationIndex].UI_HasChanges = true;
            selectedProduct!.UI_HasChanges = true;
        }

        // Update the selected variation state if this is one of the selected variations
        if (selectedVariation1 && selectedVariation1.Id === variation.Id) {
            // Create a new reference to trigger re-render
            const updatedVariation = {...selectedVariation1, Name: newValue, UI_HasChanges: true};
            setSelectedVariation1(updatedVariation);
        } else if (selectedVariation2 && selectedVariation2.Id === variation.Id) {
            // Create a new reference to trigger re-render
            const updatedVariation = {...selectedVariation2, Name: newValue, UI_HasChanges: true};
            setSelectedVariation2(updatedVariation);
        }

        // Force re-render
        const productCopy = selectedProduct!;
        forceSetSelectedProduct(productCopy);
    }

    // Variation Sort Order
    function varSortOrderOnChange(variation: ProductVariationModel, newValue: string) {
        const oldSortOrder = variation.SortOrder;
        const newSortOrder = Number(newValue);

        // Get all variations in the same category
        const variationsInCategory = selectedProduct!.getVariationsForCategory(variation.VariationCategoryCode);

        // Check if another variation in the same category already has this sort order
        const matchingVariation = variationsInCategory.find(v =>
            v.Id !== variation.Id &&
            v.SortOrder === newSortOrder
        );

        // Find the actual variation in the product's variations list
        const variationInProduct = selectedProduct!.Ex_Variations.find(v => v.Id === variation.Id);
        if (variationInProduct) {
            // Update the variation in the product
            variationInProduct.SortOrder = newSortOrder;
            // Mark the variation as having changes
            variationInProduct.UI_HasChanges = true;
            // Mark the product as having changes
            selectedProduct!.UI_HasChanges = true;
        }

        // If we found a matching variation, swap the sort orders
        if (matchingVariation) {
            // Find the matching variation in the product's variations list
            const matchingVariationInProduct = selectedProduct!.Ex_Variations.find(v => v.Id === matchingVariation.Id);
            if (matchingVariationInProduct) {
                matchingVariationInProduct.SortOrder = oldSortOrder;
                // Mark the matching variation as having changes
                matchingVariationInProduct.UI_HasChanges = true;
                // Mark the product as having changes
                selectedProduct!.UI_HasChanges = true;
            }
        }

        // Get all variations in this category from the product
        const allVariationsInCategory = selectedProduct!.Ex_Variations.filter(
            v => v.VariationCategoryCode === variation.VariationCategoryCode
        );

        // Organize the sort orders to ensure they are sequential
        const organizedVariations = JC_Utils.organiseSortOrders(allVariationsInCategory);

        // Create a map of original sort orders for comparison
        const originalSortOrders = new Map<string, number>();
        allVariationsInCategory.forEach(v => {
            originalSortOrders.set(v.Id, v.SortOrder);
        });

        // Update the product's variations with the organized ones
        selectedProduct!.Ex_Variations = selectedProduct!.Ex_Variations.map(v => {
            if (v.VariationCategoryCode === variation.VariationCategoryCode) {
                // Find the organized version of this variation
                const organizedVariation = organizedVariations.find(ov => ov.Id === v.Id);

                // If we found an organized variation and its sort order changed, mark it as having changes
                if (organizedVariation && originalSortOrders.has(organizedVariation.Id) &&
                    organizedVariation.SortOrder !== originalSortOrders.get(organizedVariation.Id)) {
                    organizedVariation.UI_HasChanges = true;
                    selectedProduct!.UI_HasChanges = true;
                }

                return organizedVariation || v;
            }
            return v;
        });

        // Update the selected variation states
        if (selectedVariation1) {
            const updatedVariation1 = selectedProduct!.Ex_Variations.find(v => v.Id === selectedVariation1.Id);
            if (updatedVariation1) {
                setSelectedVariation1(updatedVariation1);
            }
        }

        if (selectedVariation2) {
            const updatedVariation2 = selectedProduct!.Ex_Variations.find(v => v.Id === selectedVariation2.Id);
            if (updatedVariation2) {
                setSelectedVariation2(updatedVariation2);
            }
        }

        // Force re-render
        const productCopy = selectedProduct!;
        forceSetSelectedProduct(productCopy);
    }


    // - ---- - //
    // - EDIT - //
    // - ---- - //

    // Variation Ingredients
    function cancelVarIngEdit() {
        // No need to reset UI_ToDelete as we'll track to-delete ingredients in state instead
        forceSetSelectedProduct(selectedProduct!);
        setInVarIngEditMode(false);
    }
    async function submitVarIngEdit() {
        console.log("Starting submitVarIngEdit");

        // Check if we're working with a product without variations or with a variation
        if (selectedProduct!.Ex_Variations.length === 0 || selectedProduct!.getVariationCategories().length === 0) {
            console.log("Working with product's ingredients");

            // Find ingredients marked for deletion
            const ingredientsToDelete = selectedProduct!.Ex_Ingredients!.filter((i: ItemIngredientModel) => (i as any).UI_ToDelete);
            console.log(`Found ${ingredientsToDelete.length} ingredients to delete:`,
                ingredientsToDelete.map(ing => ({
                    id: ing.Id,
                    name: ing.Ex_Ingredient?.Name,
                    UI_ToDelete: (ing as any).UI_ToDelete
                }))
            );

            // Mark ingredients for deletion and set UI_HasChanges
            // IMPORTANT: Keep them in the array but mark as Deleted=true
            ingredientsToDelete.forEach((ing: ItemIngredientModel) => {
                ing.Deleted = true;
                ing.UI_HasChanges = true;
                console.log(`Marked ingredient ${ing.Ex_Ingredient?.Name} (${ing.Id}) as Deleted=true and UI_HasChanges=true`);
            });

            // Mark the product as changed if any ingredients were deleted
            if (ingredientsToDelete.length > 0) {
                selectedProduct!.UI_HasChanges = true;
                console.log(`Marked product ${selectedProduct!.Name} as having changes`);
            }
        } else {
            console.log("Working with variation's ingredients");

            // Get the selected variation IDs
            const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];
            console.log(`Selected variation IDs: ${selectedIds.join(', ')}`);

            // Get the ingredients using getItemIngredients
            const ingredients = selectedProduct!.getItemIngredients(selectedIds);
            console.log(`Found ${ingredients.length} ingredients for selected variations`);

            // Find ingredients marked for deletion
            const ingredientsToDelete = ingredients.filter((i: ItemIngredientModel) => (i as any).UI_ToDelete);
            console.log(`Found ${ingredientsToDelete.length} ingredients to delete:`,
                ingredientsToDelete.map(ing => ({
                    id: ing.Id,
                    name: ing.Ex_Ingredient?.Name,
                    UI_ToDelete: (ing as any).UI_ToDelete
                }))
            );

            // Mark ingredients for deletion and set UI_HasChanges
            // IMPORTANT: Keep them in the array but mark as Deleted=true
            ingredientsToDelete.forEach((ing: ItemIngredientModel) => {
                ing.Deleted = true;
                ing.UI_HasChanges = true;
                console.log(`Marked ingredient ${ing.Ex_Ingredient?.Name} (${ing.Id}) as Deleted=true and UI_HasChanges=true`);
            });

            // Get the settings object
            const ingredientsSettings = selectedProduct!.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);
            console.log(`Got ingredients settings with ProductVariationId: ${ingredientsSettings.ProductVariationId}`);

            // If this is a variation's settings, mark the variation as changed too
            if (ingredientsSettings.ProductVariationId) {
                const variation = selectedProduct!.Ex_Variations.find(v => v.Id === ingredientsSettings.ProductVariationId);
                if (variation) {
                    // Don't filter out deleted ingredients, keep them in the array
                    // but they will be hidden in the UI
                    variation.UI_HasChanges = true;
                    selectedProduct!.UI_HasChanges = true;
                    console.log(`Updated variation ${variation.Name} and marked as changed`);
                }
            }
        }

        forceSetSelectedProduct(selectedProduct!);
        setInVarIngEditMode(false);
    }


    // - --- - //
    // - NEW - //
    // - --- - //

    // Ingredient (for Product or Variation)
    function addNewIngredient() {
        // First, unset any user-selected sort to ensure the new ingredient appears at the end
        setUserSelectedIngSort(false);
        setIngredientsSortConfig({
            key: 'Name',
            direction: 'asc'
        });

        // Get the selected variation IDs
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

        // Get the current ingredients list
        const ingredients = selectedProduct!.getItemIngredients(selectedIds);

        // Check if an ingredient with empty GUID already exists
        const emptyIngredient = ingredients.find(ing => ing.IngredientId === JC_Utils.emptyGuid());
        const hasEmptyIngredient = emptyIngredient !== undefined;

        // If an empty ingredient already exists, just open its dropdown
        if (hasEmptyIngredient) {
            // Force re-render to ensure the DOM is updated
            forceSetSelectedProduct(selectedProduct!);

            // Open the dropdown for the empty ingredient
            setTimeout(() => {
                // Get the sorted ingredients list as it appears in the UI
                const sortedIngredients = sortIngredients(ingredients);

                // Find the index of the empty ingredient in the sorted list
                const emptyIngredientIndex = sortedIngredients.findIndex(ing => ing.IngredientId === JC_Utils.emptyGuid());

                if (emptyIngredientIndex !== -1) {
                    // Get only ingredient dropdowns by data-type attribute
                    let ingredientDropdowns = document.querySelectorAll('.ck-dropdown[data-type="ingredient"]');
                    if (ingredientDropdowns.length > 0) {
                        // Click the last ingredient dropdown
                        (ingredientDropdowns[ingredientDropdowns.length - 1] as HTMLElement).click();
                    }
                }
            }, 20);
            return;
        }

        // Use the new addIngredient function in the Product model
        selectedProduct!.addIngredient(selectedIds);

        // Force re-render
        forceSetSelectedProduct(selectedProduct!);

        // Open the dropdown for the new ingredient
        setTimeout(() => {
            // Get only ingredient dropdowns by data-type attribute
            let ingredientDropdowns = document.querySelectorAll('.ck-dropdown[data-type="ingredient"]');
            if (ingredientDropdowns.length > 0) {
                // Click the last ingredient dropdown
                (ingredientDropdowns[ingredientDropdowns.length - 1] as HTMLElement).click();
            }
        }, 20);
    }


    // - --------------- - //
    // - COPY OTHER MODE - //
    // - --------------- - //

    function startCopyOther() {
        if (!selectedProduct) return;

        // First, set the selected copy other product to the current product
        // Create a deep copy to ensure we don't have reference issues
        const currentSelectedProduct = new ProductModel(selectedProduct);
        setSelectedCopyOtherProduct(currentSelectedProduct);
        console.log(`Starting Copy Other mode with product: ${currentSelectedProduct.Name} (ID: ${currentSelectedProduct.Id})`);

        // Then, enter Copy Other mode
        setInCopyOtherMode(true);

        // Finally, explicitly select the current product to trigger the selectProduct function
        // This ensures the same behavior as if we clicked the product after entering Copy Other mode
        setTimeout(() => {
            // Find the product in the products list to ensure we get a fresh reference
            const productToSelect = products.find(p => p.Id === selectedProduct.Id);
            if (productToSelect) {
                console.log(`Re-selecting current product in Copy Other mode: ${productToSelect.Name} (ID: ${productToSelect.Id})`);
                selectProduct(productToSelect);
            }
        }, 0);
    }

    function submitCopyOther() {
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

        // Check if we're working with a product without variations
        if (selectedProduct!.Ex_Variations.filter(v => !v.Deleted).length === 0 || selectedProduct!.getVariationCategories().length === 0) {
            // Working with product's ingredients
            // For products without variations, we can copy from either a variation or another product without variations
            const sourceName = selectedCopyOtherVariation
                ? selectedCopyOtherVariation.Name
                : selectedCopyOtherProduct!.Name;

            setConfirmationModalData({
                title: "Confirm Copy Ingredients",
                text: `The ingredients on "${selectedProduct!.Name}" will be replaced by the ingredients from "${sourceName}".`,
                submitButtons: [{
                    text: "Confirm",
                    onSubmit: async () => {
                        // First, mark all existing ingredients as Deleted=true and UI_HasChanges=true
                        if (selectedProduct!.Ex_Ingredients && selectedProduct!.Ex_Ingredients.length > 0) {
                            selectedProduct!.Ex_Ingredients.forEach(ing => {
                                ing.Deleted = true;
                                ing.UI_HasChanges = true;
                            });
                            console.log(`Marked ${selectedProduct!.Ex_Ingredients.length} existing ingredients as Deleted=true and UI_HasChanges=true`);
                        }

                        // Copy ingredients from the source
                        // If we have a selected variation, use its ingredients
                        // Otherwise, use the product's ingredients (for products without variations)
                        const copiedIngredients = selectedCopyOtherVariation
                            ? (selectedCopyOtherVariation.Ex_Ingredients || [])
                            : (selectedCopyOtherProduct!.Ex_Ingredients || []);

                        // Create deep copies of the ingredients with new IDs
                        const newIngredientsArray = copiedIngredients.map(ing => {
                            // Create a new ingredient with a new ID but same properties
                            const newIng = new ItemIngredientModel({
                                ProductId: selectedProduct!.Id,
                                ProductVariationId: null,
                                IngredientId: ing.IngredientId,
                                AmountGrams: ing.AmountGrams,
                                ShowPercent: ing.ShowPercent,
                                Ex_Ingredient: ing.Ex_Ingredient,
                                UI_HasChanges: true // Mark as changed
                            });
                            return newIng;
                        });

                        // Add the new ingredients to the product (keeping the old ones marked as deleted)
                        selectedProduct!.Ex_Ingredients = [
                            ...selectedProduct!.Ex_Ingredients, // Keep old ingredients (marked as deleted)
                            ...newIngredientsArray // Add new ingredients
                        ];

                        // Mark the product as having changes
                        selectedProduct!.UI_HasChanges = true;

                        console.log(`Added ${newIngredientsArray.length} new ingredients with UI_HasChanges=true`);
                        console.log(`Product now has ${selectedProduct!.Ex_Ingredients.length} total ingredients (including deleted ones)`);

                        JC_Utils.showToastSuccess("Ingredients copied!");
                        cancelCopyOther();
                        setConfirmationModalData(null);
                    }
                }]
            });
        } else {
            // Working with variation's ingredients
            // Get the variation for display purposes
            const currentIngredientsVariation = selectedProduct!.getVariationForSettings(SettingsTypeEnum.Ingredients, selectedIds);

            // Check if we found a variation with ingredients settings
            if (!currentIngredientsVariation) {
                JC_Utils.showToastError("No variation with ingredients settings found.");
                cancelCopyOther();
                return;
            }

            // Check if we're copying from a product with variations or without variations
            if (selectedCopyOtherProduct!.Ex_Variations.filter(v => !v.Deleted).length === 0 ||
                selectedCopyOtherProduct!.getVariationCategories().length === 0) {
                // Copying from a product without variations to a product with variations
                setConfirmationModalData({
                    title: "Confirm Copy Ingredients",
                    text: `The ingredients on "${currentIngredientsVariation.Name}" will be replaced by the ingredients from "${selectedCopyOtherProduct!.Name}".`,
                    submitButtons: [{
                        text: "Confirm",
                        onSubmit: async () => {
                            // First, mark all existing ingredients as Deleted=true and UI_HasChanges=true
                            if (currentIngredientsVariation.Ex_Ingredients && currentIngredientsVariation.Ex_Ingredients.length > 0) {
                                currentIngredientsVariation.Ex_Ingredients.forEach(ing => {
                                    ing.Deleted = true;
                                    ing.UI_HasChanges = true;
                                });
                                console.log(`Marked ${currentIngredientsVariation.Ex_Ingredients.length} existing ingredients on variation as Deleted=true and UI_HasChanges=true`);
                            }

                            // Copy ingredients from the product without variations
                            const sourceIngredients = selectedCopyOtherProduct!.Ex_Ingredients == null ? [] : selectedCopyOtherProduct!.Ex_Ingredients!;

                            // Create deep copies of the ingredients with new IDs
                            const newIngredientsArray = sourceIngredients.map(ing => {
                                // Create a new ingredient with a new ID but same properties
                                const newIng = new ItemIngredientModel({
                                    ProductId: null,
                                    ProductVariationId: currentIngredientsVariation.Id,
                                    IngredientId: ing.IngredientId,
                                    AmountGrams: ing.AmountGrams,
                                    ShowPercent: ing.ShowPercent,
                                    Ex_Ingredient: ing.Ex_Ingredient,
                                    UI_HasChanges: true // Mark as changed
                                });
                                return newIng;
                            });

                            // Add the new ingredients to the variation (keeping the old ones marked as deleted)
                            currentIngredientsVariation.Ex_Ingredients = [
                                ...(currentIngredientsVariation.Ex_Ingredients || []), // Keep old ingredients (marked as deleted)
                                ...newIngredientsArray // Add new ingredients
                            ];

                            // Mark the variation and product as having changes
                            currentIngredientsVariation.UI_HasChanges = true;
                            selectedProduct!.UI_HasChanges = true;

                            console.log(`Added ${newIngredientsArray.length} new ingredients to variation with UI_HasChanges=true`);
                            console.log(`Variation now has ${currentIngredientsVariation.Ex_Ingredients.length} total ingredients (including deleted ones)`);

                            JC_Utils.showToastSuccess("Ingredients copied!");
                            cancelCopyOther();
                            setConfirmationModalData(null);
                        }
                    }]
                });
            } else {
                // For products with variations, we must copy from another variation
                if (!selectedCopyOtherVariation) {
                    JC_Utils.showToastError("No variation selected to copy from!");
                    return;
                }

                setConfirmationModalData({
                    title: "Confirm Copy Ingredients",
                    text: `The ingredients on "${currentIngredientsVariation.Name}" will be replaced by the ingredients from "${selectedCopyOtherVariation!.Name}".`,
                    submitButtons: [{
                        text: "Confirm",
                        onSubmit: async () => {
                            // First, mark all existing ingredients as Deleted=true and UI_HasChanges=true
                            if (currentIngredientsVariation.Ex_Ingredients && currentIngredientsVariation.Ex_Ingredients.length > 0) {
                                currentIngredientsVariation.Ex_Ingredients.forEach(ing => {
                                    ing.Deleted = true;
                                    ing.UI_HasChanges = true;
                                });
                                console.log(`Marked ${currentIngredientsVariation.Ex_Ingredients.length} existing ingredients on variation as Deleted=true and UI_HasChanges=true`);
                            }

                            // Copy ingredients from the selected variation
                            const sourceIngredients = selectedCopyOtherVariation!.Ex_Ingredients == null ? [] : selectedCopyOtherVariation!.Ex_Ingredients!;

                            // Create deep copies of the ingredients with new IDs
                            const newIngredientsArray = sourceIngredients.map(ing => {
                                // Create a new ingredient with a new ID but same properties
                                const newIng = new ItemIngredientModel({
                                    ProductId: null,
                                    ProductVariationId: currentIngredientsVariation.Id,
                                    IngredientId: ing.IngredientId,
                                    AmountGrams: ing.AmountGrams,
                                    ShowPercent: ing.ShowPercent,
                                    Ex_Ingredient: ing.Ex_Ingredient,
                                    UI_HasChanges: true // Mark as changed
                                });
                                return newIng;
                            });

                            // Add the new ingredients to the variation (keeping the old ones marked as deleted)
                            currentIngredientsVariation.Ex_Ingredients = [
                                ...(currentIngredientsVariation.Ex_Ingredients || []), // Keep old ingredients (marked as deleted)
                                ...newIngredientsArray // Add new ingredients
                            ];

                            // Mark the variation and product as having changes
                            currentIngredientsVariation.UI_HasChanges = true;
                            selectedProduct!.UI_HasChanges = true;

                            console.log(`Added ${newIngredientsArray.length} new ingredients to variation with UI_HasChanges=true`);
                            console.log(`Variation now has ${currentIngredientsVariation.Ex_Ingredients.length} total ingredients (including deleted ones)`);

                            JC_Utils.showToastSuccess("Ingredients copied!");
                            cancelCopyOther();
                            setConfirmationModalData(null);
                        }
                    }]
                });
            }
        }
    }

    function cancelCopyOther() {
        setSelectedCopyOtherProduct(null);
        setSelectedCopyOtherVariation(null);
        setInCopyOtherMode(false);
    }




    // - ---- - //
    // - SAVE - //
    // - ---- - //

    async function save() {
        console.log("Starting save function");

        // First, remove empty ingredients from all products and variations
        // This ensures we don't save any ingredients with empty IngredientId (emptyGuid)
        for (const product of products) {
            // Remove empty ingredients from the product
            if (product.Ex_Ingredients) {
                const beforeCount = product.Ex_Ingredients.length;
                product.Ex_Ingredients = product.Ex_Ingredients.filter(ing =>
                    ing.IngredientId !== JC_Utils.emptyGuid()
                );
                console.log(`Removed ${beforeCount - product.Ex_Ingredients.length} empty ingredients from product ${product.Name}`);

                // Log deleted ingredients
                const deletedIngredients = product.Ex_Ingredients.filter(ing => ing.Deleted);
                console.log(`Product ${product.Name} has ${deletedIngredients.length} deleted ingredients:`,
                    deletedIngredients.map(ing => ({
                        id: ing.Id,
                        name: ing.Ex_Ingredient?.Name,
                        deleted: ing.Deleted,
                        hasChanges: ing.UI_HasChanges
                    }))
                );
            }

            // Remove empty ingredients from all variations
            for (const variation of product.Ex_Variations) {
                if (variation.Ex_Ingredients) {
                    const beforeCount = variation.Ex_Ingredients.length;
                    variation.Ex_Ingredients = variation.Ex_Ingredients.filter(ing =>
                        ing.IngredientId !== JC_Utils.emptyGuid()
                    );
                    console.log(`Removed ${beforeCount - variation.Ex_Ingredients.length} empty ingredients from variation ${variation.Name}`);

                    // Log deleted ingredients
                    const deletedIngredients = variation.Ex_Ingredients.filter(ing => ing.Deleted);
                    console.log(`Variation ${variation.Name} has ${deletedIngredients.length} deleted ingredients:`,
                        deletedIngredients.map(ing => ({
                            id: ing.Id,
                            name: ing.Ex_Ingredient?.Name,
                            deleted: ing.Deleted,
                            hasChanges: ing.UI_HasChanges
                        }))
                    );
                }
            }
        }

        // Also remove empty ingredients from the selected product
        if (selectedProduct) {
            // Remove empty ingredients from the product
            if (selectedProduct.Ex_Ingredients) {
                selectedProduct.Ex_Ingredients = selectedProduct.Ex_Ingredients.filter(ing =>
                    ing.IngredientId !== JC_Utils.emptyGuid()
                );
            }

            // Remove empty ingredients from all variations
            for (const variation of selectedProduct.Ex_Variations) {
                if (variation.Ex_Ingredients) {
                    variation.Ex_Ingredients = variation.Ex_Ingredients.filter(ing =>
                        ing.IngredientId !== JC_Utils.emptyGuid()
                    );
                }
            }
        }

        // Get the ingredients using the product model's getItemIngredients method
        const ingredients = selectedProduct!.getItemIngredients();

        if (ingredients) {
            // Mark ingredients with UI_ToDelete as deleted (for database tracking)
            const ingredientsToDelete = ingredients.filter(i => (i as any).UI_ToDelete);
            ingredientsToDelete.forEach(ing => {
                ing.Deleted = true;
                ing.UI_HasChanges = true;
            });

            // Filter out UI_ToDelete items and ingredients with empty names
            const filteredIngredients = ingredients.filter(i =>
                !(i as any).UI_ToDelete &&
                !JC_Utils.stringNullOrEmpty(i.Ex_Ingredient?.Name)
            );

            // If this is a product with no variations, update the product's ingredients
            if (selectedProduct!.Ex_Variations.filter(v => !v.Deleted).length === 0 || selectedProduct!.getVariationCategories().length === 0) {
                selectedProduct!.Ex_Ingredients = filteredIngredients;
            } else {
                // Otherwise, update the variation's ingredients
                // Get the selected variation IDs
                const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

                // Get the ingredients settings
                const ingredientsSettings = selectedProduct!.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);

                // If this is a variation's settings, update the variation's ingredients
                if (ingredientsSettings.ProductVariationId) {
                    const variation = selectedProduct!.Ex_Variations.find(v => v.Id === ingredientsSettings.ProductVariationId);
                    if (variation) {
                        variation.Ex_Ingredients = filteredIngredients;
                        variation.UI_HasChanges = true;
                    }
                }
            }

            // Mark the product as having changes
            selectedProduct!.UI_HasChanges = true;
        }

        // Get all products with changes
        const productsToUpdate = products
            .filter(product => product.UI_HasChanges || product.Deleted)
            .map(product => product.getOnlyChanged());

        // If the selected product has changes, make sure it's included
        if (selectedProduct?.UI_HasChanges && !productsToUpdate.some(p => p.Id === selectedProduct.Id)) {
            // Log the selected product's settings before getting only changed
            console.log("Selected product settings before getOnlyChanged:", {
                ingredientsYOffset: selectedProduct.Ex_Settings?.IngredientsYOffset,
                hasChanges: selectedProduct.Ex_Settings?.UI_HasChanges,
                productId: selectedProduct.Ex_Settings?.ProductId,
                settingsId: selectedProduct.Ex_Settings?.Id
            });

            // Get only the changed data
            const changedProduct = selectedProduct!.getOnlyChanged();

            // Log the changed product's settings
            console.log("Changed product settings after getOnlyChanged:", {
                ingredientsYOffset: changedProduct.Ex_Settings?.IngredientsYOffset,
                hasChanges: changedProduct.Ex_Settings?.UI_HasChanges,
                productId: changedProduct.Ex_Settings?.ProductId,
                settingsId: changedProduct.Ex_Settings?.Id
            });

            // Add to products to update
            productsToUpdate.push(changedProduct);
        }

        // All products will be updated using the same endpoint
        // The backend will handle creating new products and updating existing ones

        // Update all products
        if (productsToUpdate.length > 0) {
            // Log the products being saved
            console.log("Products to update:", productsToUpdate.map(p => ({
                id: p.Id,
                name: p.Name,
                settingsId: p.Ex_Settings?.Id,
                ingredientsYOffset: p.Ex_Settings?.IngredientsYOffset,
                settingsHasChanges: p.Ex_Settings?.UI_HasChanges,
                ingredientsCount: p.Ex_Ingredients?.length,
                deletedIngredientsCount: p.Ex_Ingredients?.filter(i => i.Deleted).length
            })));

            // Log detailed information about ingredients being saved
            for (const product of productsToUpdate) {
                if (product.Ex_Ingredients && product.Ex_Ingredients.length > 0) {
                    console.log(`Product ${product.Name} ingredients being saved:`,
                        product.Ex_Ingredients.map(ing => ({
                            id: ing.Id,
                            name: ing.Ex_Ingredient?.Name,
                            deleted: ing.Deleted,
                            hasChanges: ing.UI_HasChanges
                        }))
                    );
                }
            }

            // Save all products with their variations using updateList
            // The backend will handle all child lists (variations, ingredients, etc.)
            await JC_Post<any>(`${ProductModel.apiRoute}/updateList`, {
                products: productsToUpdate
            });

            console.log("Products saved successfully");
        }

        setTimeout(initialise, 200);
        JC_Utils.showToastSuccess(`Successfully saved products!`)
    }


    // - ------ - //
    // - BUILDS - //
    // - ------ - //

    function _buildSettings(variationCategoryCode?:string, forceInCopyOtherMode?:boolean) {

        if (!selectedProduct) return;

        // Use the passed inCopyOtherMode parameter if provided, otherwise use the global state
        const useInCopyOtherMode = forceInCopyOtherMode !== undefined ? forceInCopyOtherMode : inCopyOtherMode;

        // Get the selected variation IDs
        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

        // Use getItemSettingsForSettingsType() since settings can relate to either the product or a variation
        const labelSizeSettings = selectedProduct.getItemSettingsForSettingsType(SettingsTypeEnum.LabelSize, selectedIds);
        const ingredientsSettings = selectedProduct.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);
        const labelSettings = selectedProduct.getItemSettingsForSettingsType(SettingsTypeEnum.Labels, selectedIds);
        const servingsSettings = selectedProduct.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, selectedIds);

        // Check if we should show settings based on the variation category
        // If no category is provided or if the product has no variations, show all settings
        const shouldShowLabelSizeSettings = !variationCategoryCode ||
            selectedProduct.Ex_Variations.filter(v => !v.Deleted).length === 0 ||
            selectedProduct.getVariationCategories().length === 0 ||
            selectedProduct.variationHasSettings(variationCategoryCode, SettingsTypeEnum.LabelSize);

        const shouldShowServingsSettings = !variationCategoryCode ||
            selectedProduct.Ex_Variations.filter(v => !v.Deleted).length === 0 ||
            selectedProduct.getVariationCategories().length === 0 ||
            selectedProduct.variationHasSettings(variationCategoryCode, SettingsTypeEnum.Servings);

        // In Copy Other mode, always show ingredients settings
        // Otherwise, check if the variation category has ingredients settings
        const shouldShowIngredientsSettings = useInCopyOtherMode ||
            !variationCategoryCode ||
            selectedProduct.Ex_Variations.filter(v => !v.Deleted).length === 0 ||
            selectedProduct.getVariationCategories().length === 0 ||
            selectedProduct.variationHasSettings(variationCategoryCode, SettingsTypeEnum.Ingredients);

        const shouldShowLabelsSettings = !variationCategoryCode ||
            selectedProduct.Ex_Variations.filter(v => !v.Deleted).length === 0 ||
            selectedProduct.getVariationCategories().length === 0 ||
            selectedProduct.variationHasSettings(variationCategoryCode, SettingsTypeEnum.Labels);

        return <div className={styles.settingsContainer}>

            {/* Label Size and Servings Settings Container */}
            {!inCopyOtherMode && labelSizeSettings && shouldShowLabelSizeSettings &&
            <div className={styles.labelSizeServingsContainer}>
                {/* Label Size Settings */}
                <div className={styles.itemSettingsContainer}>
                    <div key={labelSizeSettings.Id} className={`${styles.settingsForm} ${styles.labelSizeSettings}`}>
                        <div className={styles.labelSizeLabel}>Label Size:</div>
                        <JC_Dropdown
                            key={`${labelSizeSettings.Id}-LabelSize`}
                            type={DropdownTypeEnum.Default}
                            options={[{
                                OptionId: LabelSizeEnum.Large,
                                Label: "Large"
                            },{
                                OptionId: LabelSizeEnum.Small,
                                Label: "Small"
                            },{
                                OptionId: LabelSizeEnum.TwoByFive,
                                Label: "2X5"
                            }]}
                            selectedOptionId={labelSizeSettings?.LabelSize}
                            onSelection={(newLabelSize:string) => selectVarLabelSize(LabelSizeEnum[newLabelSize as keyof typeof LabelSizeEnum])}
                        />
                    </div>
                </div>

                {/* Servings Settings */}
                {!inCopyOtherMode && shouldShowServingsSettings && (() => {
                    return servingsSettings && (
                        <div className={styles.itemSettingsContainer}>
                            <div key={servingsSettings.Id} className={`${styles.settingsForm} ${styles.servingsSettings}`}>
                                <div className={styles.servingsLabel}>Pack Weight Text:</div>
                                <JC_Field
                                    key={`${servingsSettings.Id}-PackWeightText`}
                                    overrideClass={styles.packWeightTextField}
                                    inputId="pack-weight-text-input"
                                    type={FieldTypeEnum.Text}
                                    value={servingsSettings?.PackWeightText}
                                    onChange={(newValue) => {
                                        selectedProduct.setSettingsValue(SettingsTypeEnum.Servings, "PackWeightText", newValue, selectedIds);
                                        forceSetSelectedProduct(selectedProduct);
                                    }}
                                />
                                <div className={styles.servingsLabel}>Servings Per Pack:</div>
                                <div style={{width: "100%"}}>
                                    <JC_Field
                                        key={`${servingsSettings.Id}-ServingsPerPack`}
                                        inputId="servings-per-pack-input"
                                        type={FieldTypeEnum.Number}
                                        value={servingsSettings?.ServingsPerPack}
                                        onChange={(newValue) => {
                                            selectedProduct.setSettingsValue(SettingsTypeEnum.Servings, "ServingsPerPack", +newValue, selectedIds);
                                            forceSetSelectedProduct(selectedProduct);
                                        }}
                                    />
                                </div>
                                <div className={styles.servingsLabel}>Serving Size:</div>
                                <div style={{width: "100%"}}>
                                    <JC_Field
                                        key={`${servingsSettings.Id}-ServingSizeGrams`}
                                        inputId="serving-size-input"
                                        type={FieldTypeEnum.Number}
                                        value={servingsSettings?.ServingSizeGrams}
                                        onChange={(newValue) => {
                                            selectedProduct.setSettingsValue(SettingsTypeEnum.Servings, "ServingSizeGrams", +newValue, selectedIds);
                                            forceSetSelectedProduct(selectedProduct);
                                        }}
                                    />
                                </div>
                                <div className={styles.servingsLabel}>Weight Change:</div>
                                <div style={{width: "100%"}}>
                                    <JC_Field
                                        key={`${servingsSettings.Id}-WeightChange`}
                                        inputId={`weight-change-input-${selectedProduct.Id}`}
                                        type={FieldTypeEnum.Number}
                                        value={servingsSettings?.WeightChange}
                                        onChange={(newValue) => {
                                            selectedProduct.setSettingsValue(SettingsTypeEnum.Servings, "WeightChange", +newValue, selectedIds);

                                            // Recalculate nutritional values with the current selections
                                            selectedProduct.calcNutValsForSelected(
                                                selectedVariation1 || undefined,
                                                selectedVariation2 || undefined
                                            );

                                            forceSetSelectedProduct(selectedProduct);
                                        }}
                                        defaultValue={1}
                                    />
                                </div>
                            </div>
                        </div>
                    );
                })()}
            </div>}

            {/* Ingredients Settings */}
            {ingredientsSettings && shouldShowIngredientsSettings &&
            <div className={styles.itemSettingsContainer}>
                {(() => {
                    // Get ingredients from the selected product or variation
                    let itemIngredients = selectedCopyOtherVariation != null
                        ? (selectedCopyOtherVariation.Ex_Ingredients??[])
                        : selectedProduct.getItemIngredients(selectedIds);

                    // Ensure itemIngredients is always an array, even if empty
                    itemIngredients = itemIngredients || [];

                    return (
                        <div key={ingredientsSettings.Id} className={styles.ingListContainer}>
                            {/* Headings */}
                            <div
                                className={`${styles.ingListHeadingText} ${styles.sortable}`}
                                onClick={() => handleIngredientSort('Name')}
                            >
                                <div className={styles.headerLabelContainer}>
                                    <div className={styles.headerLabel}>
                                        Ingredient
                                    </div>
                                    {ingredientsSortConfig.key === 'Name' && userSelectedIngSort && (
                                        <Image
                                            src="/icons/Chevron.webp"
                                            width={0}
                                            height={0}
                                            alt="Sort indicator"
                                            className={styles.sortIndicator}
                                            style={{
                                                transform: ingredientsSortConfig.direction === 'asc' ? 'rotate(180deg)' : 'rotate(0deg)'
                                            }}
                                            unoptimized
                                        />
                                    )}
                                </div>
                            </div>
                            <div
                                className={`${styles.ingListHeadingText} ${styles.sortable}`}
                                onClick={() => handleIngredientSort('AmountGrams')}
                            >
                                <div className={styles.headerLabelContainer}>
                                    <div className={styles.headerLabel}>
                                        Amount (g)
                                    </div>
                                    {ingredientsSortConfig.key === 'AmountGrams' && userSelectedIngSort && (
                                        <Image
                                            src="/icons/Chevron.webp"
                                            width={0}
                                            height={0}
                                            alt="Sort indicator"
                                            className={styles.sortIndicator}
                                            style={{
                                                transform: ingredientsSortConfig.direction === 'asc' ? 'rotate(180deg)' : 'rotate(0deg)'
                                            }}
                                            unoptimized
                                        />
                                    )}
                                </div>
                            </div>
                            <div
                                className={`${styles.ingListHeadingText} ${styles.sortable}`}
                                onClick={() => handleIngredientSort('CostPer100g')}
                            >
                                <div className={styles.headerLabelContainer}>
                                    <div className={styles.headerLabel}>
                                        Cost
                                    </div>
                                    {ingredientsSortConfig.key === 'CostPer100g' && userSelectedIngSort && (
                                        <Image
                                            src="/icons/Chevron.webp"
                                            width={0}
                                            height={0}
                                            alt="Sort indicator"
                                            className={styles.sortIndicator}
                                            style={{
                                                transform: ingredientsSortConfig.direction === 'asc' ? 'rotate(180deg)' : 'rotate(0deg)'
                                            }}
                                            unoptimized
                                        />
                                    )}
                                </div>
                            </div>
                            <div/>
                            {/* Ingredient */}
                            {sortIngredients(itemIngredients)?.filter(ing => !ing.Deleted).map(itemIngredient =>
                                <React.Fragment key={`ingredient-${itemIngredient.Id}`}>
                                    {/* Dropdown */}
                                    <div className={styles.ingListDropdown}>
                                        <JC_Dropdown
                                            type={DropdownTypeEnum.Default}
                                            options={allIngredients.filter(mainIng => !itemIngredients.filter((i: ItemIngredientModel) => i.IngredientId != itemIngredient.IngredientId && !i.Deleted).some((i: ItemIngredientModel) => i.IngredientId == mainIng.Id)).map((i: IngredientModel) => ({
                                                OptionId: i.Id,
                                                Label: i.Name
                                            }))}
                                            selectedOptionId={itemIngredient.IngredientId}
                                            enableSearch
                                            dataType="ingredient"
                                            onSelection={newIngId => selectIngredient(itemIngredient.IngredientId, newIngId)}
                                        />
                                    </div>
                                    {/* Amount */}
                                    <JC_Field
                                        inputOverrideClass={styles.ingInputOverride}
                                        inputId={`amount-grams-${itemIngredient.Id}`}
                                        type={FieldTypeEnum.Number}
                                        value={itemIngredient.AmountGrams}
                                        onChange={(newValue:string) => {
                                            // Update the ingredient amount
                                            itemIngredient.AmountGrams = +newValue;
                                            itemIngredient.UI_HasChanges = true;
                                            selectedProduct!.UI_HasChanges = true;

                                            // Get the selected variation IDs
                                            const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

                                            // If have variations, mark the appropriate variation as changed
                                            if (selectedProduct!.Ex_Variations.length > 0 && selectedProduct!.getVariationCategories().length > 0) {
                                                // Get the settings object
                                                const ingredientsSettings = selectedProduct!.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds);

                                                // If this is a variation's settings, mark the variation as changed too
                                                if (ingredientsSettings.ProductVariationId) {
                                                    const variation = selectedProduct!.Ex_Variations.find(v => v.Id === ingredientsSettings.ProductVariationId);
                                                    if (variation) {
                                                        variation.UI_HasChanges = true;
                                                    }
                                                }
                                            }

                                            // Recalculate nutritional values with the current selections
                                            selectedProduct!.calcNutValsForSelected(
                                                selectedVariation1 || undefined,
                                                selectedVariation2 || undefined
                                            );

                                            // Force re-render with the updated product
                                            forceSetSelectedProduct(selectedProduct!);
                                        }}
                                    />
                                    {/* Cost */}
                                    <div className={styles.costText}>
                                        ${JC_Utils.roundAndCutZeroes(((itemIngredient.Ex_Ingredient?.CostPer100g || 0) / 100) * (itemIngredient.AmountGrams ?? 0), 2)}
                                    </div>
                                    {/* Show % */}
                                    {!inVarIngEditMode &&
                                    <div style={{width: "100px", marginTop: "10px"}}>
                                        <JC_Checkbox
                                            label="Show %"
                                            checked={itemIngredient.ShowPercent}
                                            onChange={() => varIngShowPercToggle(itemIngredient)}
                                        />
                                    </div>}
                                    {/* Delete */}
                                    {inVarIngEditMode &&
                                    <div style={{width: "100px", marginTop: "10px"}}>
                                        <JC_Checkbox
                                            label="Delete "
                                            checked={(itemIngredient as any).UI_ToDelete}
                                            onChange={() => varIngToDeleteToggle(itemIngredient)}
                                        />
                                    </div>}
                                </React.Fragment>)}
                            {/* Add New Button - Hide in Copy Other mode */}
                            {!inCopyOtherMode &&
                            <div className={`${styles.addNewButton} ${styles.ingredientAddButton}`} onClick={addNewIngredient}>
                                <div>+</div>
                            </div>}
                            {/* Total - Hide in Copy Other mode */}
                            {!inVarIngEditMode && !inCopyOtherMode && <div className={styles.totalText}>{JC_Utils.roundAndCutZeroes(itemIngredients.filter(ing => !ing.Deleted).reduce((prev: number, cur: ItemIngredientModel) => prev+(cur.AmountGrams ?? 0), 0), 2)}</div>}

                            {/* Total Cost - Hide in Copy Other mode */}
                            {!inVarIngEditMode && !inCopyOtherMode && <div className={styles.totalCost}>${JC_Utils.roundAndCutZeroes(itemIngredients.filter(ing => !ing.Deleted).reduce((prev: number, cur: ItemIngredientModel) => prev+((((cur.Ex_Ingredient?.CostPer100g || 0)/100)*(cur.AmountGrams??0))), 0)??0, 2)}</div>}
                            {/* Edit Buttons - Hide in Copy Other mode */}
                            {!inCopyOtherMode &&
                            <div className={styles.editButtonsContainer}>
                                {!inVarIngEditMode && itemIngredients?.filter(ing => !ing.Deleted).length > 0 && <JC_Button isSmall text="Edit"   onClick={() => setInVarIngEditMode(true)}/>}
                                {inVarIngEditMode && <JC_Button isSmall text="Cancel" onClick={cancelVarIngEdit}/>}
                                {inVarIngEditMode && <JC_Button isSmall text="Submit" onClick={submitVarIngEdit}/>}
                            </div>}
                            {/* Copy Other Button - Hide in Copy Other mode */}
                            {!inCopyOtherMode && !inVarIngEditMode &&
                            <div className={styles.copyOtherButton}>
                                <JC_Button isSmall text="Copy Other" onClick={startCopyOther}/>
                            </div>}

                            {/* Show Values on Website - Hide in Copy Other mode and Edit mode */}
                            {!inCopyOtherMode && !inVarIngEditMode &&
                            <div className={styles.showValuesCheckbox}>
                                <JC_Checkbox
                                    label="Show Values on Website"
                                    checked={ingredientsSettings?.NutValsEnabled}
                                    onChange={() => {
                                        const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];

                                        // If we have variations, use the selected variation IDs
                                        if (selectedProduct!.Ex_Variations.filter(v => !v.Deleted).length > 0 && selectedProduct!.getVariationCategories().length > 0) {
                                            selectedProduct!.setSettingsValue(SettingsTypeEnum.Ingredients, "NutValsEnabled", !ingredientsSettings?.NutValsEnabled, selectedIds);
                                        } else {
                                            // Otherwise, use product settings
                                            selectedProduct!.setSettingsValue(SettingsTypeEnum.Ingredients, "NutValsEnabled", !selectedProduct!.Ex_Settings?.NutValsEnabled);
                                        }

                                        // Update the UI
                                        forceSetSelectedProduct(selectedProduct!);
                                    }}
                                />
                            </div>}
                        </div>
                    );
                })()}
            </div>}

            {/* Labels Settings */}
            {!inCopyOtherMode && labelSettings && shouldShowLabelsSettings &&
            <div className={styles.labelContainersRow}>
                {/* Front Label Container */}
                <div className={styles.frontLabelContainer}>
                    <div className={styles.itemSettingsContainer}>
                        <div key={labelSettings.Id} className={`${styles.settingsForm} ${styles.labelSettings}`}>
                            {/* Front Label Heading */}
                            <div className={styles.listTitle}>Front Label</div>

                            {/* Front Label Name */}
                            <JC_Field
                                key={`${labelSettings.Id}-FrontLabelNameOverride`}
                                inputId="front-label-name-input"
                                type={FieldTypeEnum.RichText}
                                label="Front Label Name"
                                value={labelSettings?.FrontLabelNameOverride}
                                onChange={(newValue) => {
                                    const selectedIds = [selectedVariation1?.Id, selectedVariation2?.Id].filter(Boolean) as string[];
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "FrontLabelNameOverride", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                                richTextEnableColor
                                richTextEnableItalic
                                richTextEnableDegree
                            />

                            {/* Front Label Name X Offset - moved outside the row */}
                            <div className={`${styles.iconSettingField} ${styles.frontLabelXOffset}`}>
                                <Image
                                    src="/icons/Width.webp"
                                    alt="Width"
                                    width={0}
                                    height={0}
                                    className={styles.iconSettingImage}
                                    fill={false}
                                    unoptimized
                                />
                                <JC_Field
                                    key={`${labelSettings.Id}-FrontLabelNameXOffset`}
                                    inputId="front-label-name-x-offset-input"
                                    type={FieldTypeEnum.NumberStepper}
                                    value={labelSettings?.FrontLabelNameXOffset}
                                    numberStepperOptions={{ increment: 1 }}
                                    onChange={(newValue) => {
                                        selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "FrontLabelNameXOffset", +newValue, selectedIds);
                                        forceSetSelectedProduct(selectedProduct);
                                    }}
                                />
                            </div>

                            {/* Icon Settings - Only show if IconName is not empty */}
                            {!JC_Utils.stringNullOrEmpty(labelSettings?.IconName) &&
                            <div className={styles.iconSettingsContainer}>
                                <div className={styles.iconSettingsTitle}>
                                    Icon
                                    <div className={styles.iconVisibleCheckbox}>
                                        <JC_Checkbox
                                            label="Show"
                                            checked={labelSettings?.IconVisible !== false}
                                            onChange={() => {
                                                selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "IconVisible", !(labelSettings?.IconVisible !== false), selectedIds);
                                                forceSetSelectedProduct(selectedProduct);
                                            }}
                                        />
                                    </div>
                                </div>
                                <div className={styles.iconSettingsFields}>
                                    <div className={styles.iconSettingField}>
                                        <Image
                                            src="/icons/Width.webp"
                                            alt="Width"
                                            width={0}
                                            height={0}
                                            className={styles.iconSettingImage}
                                            fill={false}
                                            unoptimized
                                        />
                                        <JC_Field
                                            key={`${labelSettings.Id}-IconX`}
                                            inputId="icon-x-input"
                                            type={FieldTypeEnum.NumberStepper}
                                            value={labelSettings?.IconX}
                                            numberStepperOptions={{ increment: 1 }}
                                            onChange={(newValue) => {
                                                selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "IconX", +newValue, selectedIds);
                                                forceSetSelectedProduct(selectedProduct);
                                            }}
                                        />
                                    </div>
                                    <div className={styles.iconSettingField}>
                                        <Image
                                            src="/icons/Height.webp"
                                            alt="Height"
                                            width={0}
                                            height={0}
                                            className={styles.iconSettingImage}
                                            fill={false}
                                            unoptimized
                                        />
                                        <JC_Field
                                            key={`${labelSettings.Id}-IconY`}
                                            inputId="icon-y-input"
                                            type={FieldTypeEnum.NumberStepper}
                                            value={labelSettings?.IconY}
                                            numberStepperOptions={{ increment: 1 }}
                                            onChange={(newValue) => {
                                                selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "IconY", +newValue, selectedIds);
                                                forceSetSelectedProduct(selectedProduct);
                                            }}
                                        />
                                    </div>
                                    <div className={styles.iconSettingField}>
                                        <Image
                                            src="/icons/Size.webp"
                                            alt="Size"
                                            width={0}
                                            height={0}
                                            className={styles.iconSettingImage}
                                            fill={false}
                                            unoptimized
                                        />
                                        <JC_Field
                                            key={`${labelSettings.Id}-IconSize`}
                                            inputId="icon-size-input"
                                            type={FieldTypeEnum.NumberStepper}
                                            value={labelSettings?.IconSize}
                                            numberStepperOptions={{ increment: 1 }}
                                            onChange={(newValue) => {
                                                selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "IconSize", +newValue, selectedIds);
                                                forceSetSelectedProduct(selectedProduct);
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>}

                            {/* Ingredients Gap */}
                            <JC_Field
                                key={`${labelSettings.Id}-IngredientsYOffset`}
                                inputId="ingredients-gap-input"
                                type={FieldTypeEnum.NumberStepper}
                                label="Ingredients Gap"
                                value={labelSettings?.IngredientsYOffset || 0}
                                numberStepperOptions={{ increment: 1 }}
                                onChange={(newValue) => {
                                    // Ensure the value is converted to a number
                                    const numericValue = parseInt(newValue, 10);
                                    // Set the value on the settings object
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "IngredientsYOffset", numericValue, selectedIds);
                                    // Force a re-render to update the UI
                                    forceSetSelectedProduct(selectedProduct);
                                    // Log for debugging
                                    console.log(`IngredientsYOffset set to: ${numericValue}, UI_HasChanges: ${selectedProduct.getItemSettingsForSettingsType(SettingsTypeEnum.Labels, selectedIds).UI_HasChanges}`);
                                }}
                            />

                            {/* Instructions */}
                            <JC_Field
                                key={`${labelSettings.Id}-InstructionsText`}
                                inputId="instructions-input"
                                type={FieldTypeEnum.RichText}
                                label="Instructions"
                                value={itemSettings.label?.InstructionsText}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "InstructionsText", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                                richTextEnableColor
                                richTextEnableBold
                                richTextEnableItalic
                                richTextEnableDegree
                            />
                            {/* Instructions Font Size - Only show if InstructionsText is not null or empty */}
                            {!JC_Utils.stringNullOrEmpty(itemSettings.label?.InstructionsText?.replace('<p></p>', '')) &&
                            <JC_Field
                                key={`${labelSettings.Id}-InstructionsFontSize`}
                                inputId="instructions-font-size-input"
                                type={FieldTypeEnum.NumberStepper}
                                label="Instructions Font Size"
                                value={itemSettings.label?.InstructionsFontSize || 10}
                                numberStepperOptions={{ increment: 1 }}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "InstructionsFontSize", +newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                            />}

                            {/* Note */}
                            <JC_Field
                                key={`${labelSettings.Id}-NoteText`}
                                inputId="note-input"
                                type={FieldTypeEnum.RichText}
                                label="Note"
                                value={itemSettings.label?.NoteText}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "NoteText", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                                richTextEnableColor
                                richTextEnableBold
                                richTextEnableItalic
                                richTextEnableDegree
                            />
                            {/* Note Font Size - Only show if NoteText is not null or empty */}
                            {!JC_Utils.stringNullOrEmpty(itemSettings.label?.NoteText?.replace('<p></p>', '')) &&
                            <JC_Field
                                key={`${labelSettings.Id}-NoteFontSize`}
                                inputId="note-font-size-input"
                                type={FieldTypeEnum.NumberStepper}
                                label="Note Font Size"
                                value={itemSettings.label?.NoteFontSize || 10}
                                numberStepperOptions={{ increment: 1 }}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "NoteFontSize", +newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                            />}

                            {/* Allergens */}
                            <JC_Field
                                key={`${labelSettings.Id}-AllergensText`}
                                inputId="allergens-input"
                                type={FieldTypeEnum.Text}
                                label="Allergens"
                                value={itemSettings.label?.AllergensText}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "AllergensText", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                            />
                            {/* Free From */}
                            <JC_Field
                                key={`${labelSettings.Id}-FreeFromText`}
                                inputId="free-from-input"
                                type={FieldTypeEnum.Text}
                                label="Free From"
                                value={itemSettings.label?.FreeFromText}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "FreeFromText", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                            />
                            {/* Store Frozen */}
                            <JC_Field
                                key={`${labelSettings.Id}-StoreFrozenOverride`}
                                inputId="store-frozen-input"
                                type={FieldTypeEnum.RichText}
                                label="Store Frozen"
                                value={itemSettings.label?.StoreFrozenOverride}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "StoreFrozenOverride", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                                richTextEnableItalic
                                richTextEnableDegree
                            />
                        </div>
                    </div>
                </div>

                {/* Back Label Container */}
                <div className={styles.backLabelContainer}>
                    <div className={styles.itemSettingsContainer}>
                        <div key={labelSettings.Id} className={`${styles.settingsForm} ${styles.labelSettings}`}>
                            {/* Back Label Heading */}
                            <div className={styles.listTitle}>Back Label</div>

                            {/* Back Label Name */}
                            <JC_Field
                                key={`${labelSettings.Id}-BackLabelNameOverride`}
                                inputId="back-label-name-input"
                                type={FieldTypeEnum.RichText}
                                label="Back Label Name"
                                value={itemSettings.label?.BackLabelNameOverride}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "BackLabelNameOverride", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                                richTextEnableColor
                                richTextEnableItalic
                                richTextEnableDegree
                            />
                            {/* Biodegradable Text */}
                            <JC_Field
                                key={`${labelSettings.Id}-BiodegOverrideText`}
                                inputId="biodegradable-input"
                                type={FieldTypeEnum.Text}
                                label="Biodegradable Text"
                                value={itemSettings.label?.BiodegOverrideText}
                                onChange={(newValue) => {
                                    selectedProduct.setSettingsValue(SettingsTypeEnum.Labels, "BiodegOverrideText", newValue, selectedIds);
                                    forceSetSelectedProduct(selectedProduct);
                                }}
                            />
                        </div>
                    </div>
                </div>
            </div>}
        </div>
    }


    // - ---- - //
    // - MAIN - //
    // - ---- - //

    // Show spinner anytime initialised is false
    if (!initialised) {
        return <JC_Spinner isPageBody />;
    }

    return (
        <React.Fragment>
            <div className={styles.mobileMessageContainer}>
                Only available on desktop!
            </div>

            <div className={styles.mainContainer}>
                {/* New Product Modal */}
                <NewProductModal
                    isOpen={newProductModalOpen}
                    onCancel={() => setNewProductModalOpen(false)}
                    onSave={(newProduct, productVariationsData) => {
                        // Add to products list
                        setProducts([...products, newProduct]);

                        // Add the product-only ProductVariationsData record to the product
                        if (!newProduct.Ex_ProductVariationsData) {
                            newProduct.Ex_ProductVariationsData = [];
                        }
                        newProduct.Ex_ProductVariationsData.push(productVariationsData);

                        // Select the new product
                        setSelectedProduct(newProduct);
                        resetChangeDetection(newProduct);

                        // Close the modal
                        setNewProductModalOpen(false);
                    }}
                    products={products}
                />

                {/* New Variation Modal */}
                <NewVariationModal
                    isOpen={newVariationModalOpen}
                    onCancel={() => setNewVariationModalOpen(false)}
                    onSave={(newVariation: ProductVariationModel) => {
                        if (!selectedProduct) return;

                        // Add to product's variations
                        selectedProduct.Ex_Variations.push(newVariation);
                        selectedProduct.UI_HasChanges = true;

                        // Add the new ProductVariationsData records to the product
                        if (!selectedProduct.Ex_ProductVariationsData) {
                            selectedProduct.Ex_ProductVariationsData = [];
                        }

                        // Get the variationsData from the temporary property we added in NewVariationModal
                        const variationsData = (newVariation as any).Ex_ProductVariationsData || [];
                        selectedProduct.Ex_ProductVariationsData.push(...variationsData);

                        // Check if this is the only variation in its category for this product
                        const categoryCode = newVariation.VariationCategoryCode;
                        const variationsInCategory = selectedProduct.Ex_Variations
                            .filter(v => !v.Deleted && v.VariationCategoryCode === categoryCode);

                        // If this is the only variation in this category, automatically select it
                        if (variationsInCategory.length === 1) {
                            selectVariation(newVariation);
                        }

                        // Update the product
                        forceSetSelectedProduct(selectedProduct);

                        // Close the modal
                        setNewVariationModalOpen(false);
                    }}
                    categoryCode={currentVariationCategory}
                    product={selectedProduct}
                    allProducts={products}
                />

                {/* Printing button */}
                {!inCopyOtherMode &&
                <div className={styles.editModeButtons}>
                    <JC_Button
                        text="Printing"
                        iconName="Cog"
                        isSecondary
                        linkToPage="admin/printSettings"
                        linkInNewTab
                    />
                </div>}

                {selectedProduct != null &&
                <div className={styles.mainBody}>

                    {/* - ----- - */}
                    {/* - LISTS - */}
                    {/* - ----- - */}

                    <div className={styles.selectionsContainer}>

                        {/* Products List */}
                        <div className={styles.productsListWrapper}>
                            <div className={styles.productsListContainer}>
                                <div className={styles.listTitle}>Products</div>
                                <div className={styles.productTilesContainer}>
                                    {products.length > 0 ?
                                        [...products]
                                        .filter(product => !product.Deleted) // Filter out deleted products only for rendering
                                        .sort((a, b) => a.SortOrder - b.SortOrder)
                                        .map(product =>
                                        <React.Fragment key={product.Id}>
                                            <div className={styles.tileContainer}>
                                                <div
                                                    id={`product-tile-${product.Id}`}
                                                    className={`
                                                        ${styles.selectionTile}
                                                        ${product.Id == selectedProduct?.Id ? styles.selected : ""}
                                                        ${product.Id == selectedCopyOtherProduct?.Id ? styles.copyOtherSelected : ""}
                                                        ${inCopyOtherMode ? styles.inCopyOtherMode : ""}
                                                        ${product.Id == selectedCopyOtherProduct?.Id ? styles.copyOtherSelected : ""
                                                    }`}
                                                    onClick={() => selectProduct(product)}
                                                >
                                                    {product.Name}
                                                </div>
                                            </div>
                                        </React.Fragment>
                                    )
                                    : (
                                        <div className={styles.noProductsMessage}>
                                            No products yet
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className={styles.addNewButton} onClick={openNewProductModal}><div>+</div></div>
                        </div>

                        {/* Variations - Show one container per variation category */}
                        {(() => {
                            const currentProduct = inCopyOtherMode ? selectedCopyOtherProduct : selectedProduct;

                            // Safety check - if currentProduct is null, return empty div
                            if (!currentProduct) {
                                return <div className={styles.variationDetailsContainer}></div>;
                            }

                            // Determine which categories to show based on the mode
                            // In copy other mode, always show at least one category for the ingredients container
                            // In normal mode, show only the product's variation categories
                            const categoriesToShow = inCopyOtherMode
                                ? (() => {
                                    // Add debug logging
                                    console.log(`Copy Other Mode - Product: ${currentProduct?.Name}`);
                                    console.log(`- Has variations: ${currentProduct?.Ex_Variations?.length > 0}`);
                                    console.log(`- Non-deleted variations: ${currentProduct?.Ex_Variations?.filter(v => !v.Deleted).length}`);
                                    console.log(`- Variation categories: ${currentProduct?.getVariationCategories()?.length}`);

                                    // Get the variation for ingredients settings
                                    const ingredientsVar = currentProduct?.getVariationForSettings(SettingsTypeEnum.Ingredients);
                                    console.log(`- Ingredients variation found: ${ingredientsVar !== null}`);

                                    // If we found a variation with ingredients settings, use its category
                                    if (ingredientsVar && ingredientsVar.Ex_Category) {
                                        console.log(`- Using category from ingredients variation: ${ingredientsVar.Ex_Category.Name}`);
                                        return [ingredientsVar.Ex_Category];
                                    }

                                    // If no variation with ingredients settings was found, but the product has variation categories,
                                    // use the first category by sort order
                                    const categories = currentProduct?.getVariationCategories() || [];
                                    if (categories.length > 0) {
                                        console.log(`- Using first category by sort order: ${categories[0].Name}`);
                                        return [categories[0]];
                                    }

                                    // If the product has no variation categories, create a dummy category for ingredients
                                    console.log(`- Creating dummy category for ingredients`);
                                    return [{
                                        Code: "DummyCategory",
                                        Name: "Ingredients",
                                        HasIngredientsSettings: true,
                                        HasLabelSizeSettings: false,
                                        HasLabelsSettings: false,
                                        HasNameOverrideSettings: false,
                                        HasServingsSettings: false,
                                        SortOrder: 1
                                    }];
                                })()
                                : currentProduct?.getVariationCategories() || [];

                            return (
                                <div className={styles.variationDetailsContainer}>
                                    {/* Product Details Container - Hide in Copy Other mode */}
                                    {!inCopyOtherMode && (
                                    <div className={styles.productDetailsContainer} key={`product-details-${selectedProduct.Id}`}>
                                        <div className={styles.productSettings}>
                                            <JC_Field
                                                inputId="product-name-input"
                                                label="Name"
                                                type={FieldTypeEnum.Text}
                                                value={selectedProduct.Name}
                                                onChange={(newValue) => {
                                                    // Update the selected product
                                                    selectedProduct.Name = newValue;
                                                    selectedProduct.UI_HasChanges = true;
                                                    forceSetSelectedProduct(selectedProduct);

                                                    // Update the product in the products array
                                                    const updatedProducts = [...products];
                                                    const productIndex = updatedProducts.findIndex(p => p.Id === selectedProduct.Id);
                                                    if (productIndex !== -1) {
                                                        updatedProducts[productIndex].Name = newValue;
                                                        updatedProducts[productIndex].UI_HasChanges = true;
                                                        setProducts(updatedProducts);
                                                    }
                                                }}
                                            />
                                            <div className={styles.productSettingsRow}>
                                                <JC_Field
                                                    inputId="product-sort-order-input"
                                                    label="Order"
                                                    type={FieldTypeEnum.NumberStepperArrowsOnly}
                                                    value={selectedProduct.SortOrder}
                                                    numberStepperOptions={{
                                                        increment: 1,
                                                        inverted: true,
                                                        minValue: Math.min(...products.map(p => p.SortOrder)),
                                                        maxValue: Math.max(...products.map(p => p.SortOrder))
                                                    }}
                                                    onChange={(newValue) => {
                                                        const oldSortOrder = selectedProduct.SortOrder;
                                                        const newSortOrder = Number(newValue);

                                                        // Check if another product already has this sort order
                                                        const updatedProducts = [...products];
                                                        const matchingProduct = updatedProducts.find(p =>
                                                            p.Id !== selectedProduct.Id &&
                                                            p.SortOrder === newSortOrder
                                                        );

                                                        // Update the selected product
                                                        selectedProduct.SortOrder = newSortOrder;
                                                        selectedProduct.UI_HasChanges = true;
                                                        forceSetSelectedProduct(selectedProduct);

                                                        // Update the products array
                                                        const productIndex = updatedProducts.findIndex(p => p.Id === selectedProduct.Id);
                                                        if (productIndex !== -1) {
                                                            updatedProducts[productIndex].SortOrder = newSortOrder;
                                                            updatedProducts[productIndex].UI_HasChanges = true;

                                                            // If we found a matching product, swap the sort orders
                                                            if (matchingProduct) {
                                                                const matchingIndex = updatedProducts.findIndex(p => p.Id === matchingProduct.Id);
                                                                if (matchingIndex !== -1) {
                                                                    // Set the matching product's sort order to the selected product's old sort order
                                                                    updatedProducts[matchingIndex].SortOrder = oldSortOrder;
                                                                    updatedProducts[matchingIndex].UI_HasChanges = true;
                                                                }
                                                            }

                                                            // Run organiseSortOrders to ensure all products have unique sort orders
                                                            const organizedProducts = JC_Utils.organiseSortOrders(updatedProducts);

                                                            // Mark all products that had their sort order changed as having changes
                                                            organizedProducts.forEach((product, index) => {
                                                                // If the sort order changed from the original products array
                                                                if (product.SortOrder !== updatedProducts[index].SortOrder) {
                                                                    product.UI_HasChanges = true;
                                                                }
                                                            });

                                                            setProducts(organizedProducts);

                                                            // Use setTimeout to ensure the DOM has updated before scrolling
                                                            setTimeout(() => {
                                                                scrollToSelectedProduct();
                                                            }, 50);
                                                        }
                                                    }}
                                                />
                                                <div className={styles.websiteCheckbox}>
                                                    <JC_Checkbox
                                                        label="Website"
                                                        checked={selectedProduct.EnabledOnWebsite}
                                                        onChange={() => {
                                                            // Update the selected product
                                                            selectedProduct.EnabledOnWebsite = !selectedProduct.EnabledOnWebsite;
                                                            selectedProduct.UI_HasChanges = true;
                                                            forceSetSelectedProduct(selectedProduct);

                                                            // Update the product in the products array
                                                            const updatedProducts = [...products];
                                                            const productIndex = updatedProducts.findIndex(p => p.Id === selectedProduct.Id);
                                                            if (productIndex !== -1) {
                                                                updatedProducts[productIndex].EnabledOnWebsite = selectedProduct.EnabledOnWebsite;
                                                                updatedProducts[productIndex].UI_HasChanges = true;
                                                                setProducts(updatedProducts);
                                                            }
                                                        }}
                                                    />
                                                </div>
                                                <JC_Button
                                                    text="Delete"
                                                    isSecondary
                                                    isSmall
                                                    onClick={deleteProduct}
                                                    overrideClass={styles.deleteButton}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    )}
                                    {/* In Copy Other mode, only show categories if the product has variations. In normal mode, only show if there are variations */}
                                    {((inCopyOtherMode && currentProduct.Ex_Variations.filter(v => !v.Deleted).length > 0) ||
                                      (!inCopyOtherMode && selectedProduct.Ex_Variations.filter(v => !v.Deleted).length > 0)) ? (
                                        /* Show variation containers */
                                        categoriesToShow.map(category => (
                                            <div key={`${selectedProduct.Id}-${category.Code}`} className={`${styles.itemContainer} ${inCopyOtherMode ? styles.inCopyOtherMode : ""}`}>
                                                <div className={styles.itemContentWrapper}>
                                                    <div className={`${styles.itemListWrapper} ${inCopyOtherMode ? styles.inCopyOtherMode : ""}`}>
                                                        <div className={`${styles.itemsList} ${inCopyOtherMode ? styles.inCopyOtherMode : ""}`}>
                                                            {!inCopyOtherMode && (
                                                                <Image
                                                                    src="/icons/Pencil.webp"
                                                                    width={16}
                                                                    height={16}
                                                                    alt="Edit category"
                                                                    className={styles.editCategoryIcon}
                                                                    onClick={() => openEditVariationCategoryModal(category.Code)}
                                                                    unoptimized
                                                                />
                                                            )}
                                                            <div className={styles.listTitle}>
                                                                {category.Name}
                                                            </div>
                                                            <div className={`${styles.itemTilesContainer} ${inCopyOtherMode ? styles.inCopyOtherMode : ""}`}>
                                                                {currentProduct.getVariationsForCategory(category.Code)
                                                                    .filter(variation => !variation.Deleted) // Filter out deleted variations
                                                                    .map(variation => (
                                                                        <React.Fragment key={variation.Id}>
                                                                            <div className={styles.tileContainer}>
                                                                                <div
                                                                                    className={`
                                                                                        ${styles.selectionTile}
                                                                                        ${(variation.Id === selectedVariation1?.Id || variation.Id === selectedVariation2?.Id) ? styles.selected : ""}
                                                                                        ${inCopyOtherMode ? styles.inCopyOtherMode : ""}
                                                                                        ${variation.Id === selectedCopyOtherVariation?.Id ? styles.copyOtherSelected : ""}
                                                                                    `}
                                                                                    onClick={() => selectVariation(variation)}
                                                                                >
                                                                                    {variation.Name}
                                                                                </div>
                                                                            </div>
                                                                        </React.Fragment>
                                                                    ))}
                                                            </div>
                                                        </div>
                                                        <div className={styles.addNewButton} onClick={() => openNewVariationModal(category.Code)}><div>+</div></div>
                                                    </div>
                                                    {/* Variation Details and Settings container for this category */}
                                                    <div className={styles.variationDetailsAndSettingsContainer}>
                                                        {/* Variation Details Container - Show for selected variations */}
                                                        {!inCopyOtherMode && selectedProduct.Ex_Variations.filter(v => !v.Deleted).length > 0 && selectedVariation1 && selectedVariation1.VariationCategoryCode === category.Code && (
                                                        <div className={styles.variationDetailsFormContainer} key={`variation-details-${selectedVariation1.Id}`}>
                                                            <JC_Field
                                                                inputId={`variation-name-input-${selectedVariation1.Id}`}
                                                                label="Name"
                                                                type={FieldTypeEnum.Text}
                                                                value={selectedVariation1.Name}
                                                                onChange={(newValue) => varNameOnChange(selectedVariation1, newValue)}
                                                            />
                                                            <div className={styles.productSettingsRow}>
                                                                <JC_Field
                                                                    inputId={`variation-sort-order-input-${selectedVariation1.Id}`}
                                                                    label="Order"
                                                                    type={FieldTypeEnum.NumberStepperArrowsOnly}
                                                                    value={selectedVariation1.SortOrder}
                                                                    numberStepperOptions={{
                                                                        increment: 1,
                                                                        inverted: true,
                                                                        minValue: Math.min(...selectedProduct!.getVariationsForCategory(selectedVariation1.VariationCategoryCode).map(v => v.SortOrder)),
                                                                        maxValue: Math.max(...selectedProduct!.getVariationsForCategory(selectedVariation1.VariationCategoryCode).map(v => v.SortOrder))
                                                                    }}
                                                                    onChange={(newValue) => varSortOrderOnChange(selectedVariation1, newValue)}
                                                                />
                                                                <div className={styles.websiteCheckbox}>
                                                                    <JC_Checkbox
                                                                        label="Website"
                                                                        checked={selectedVariation1.EnabledOnWebsite}
                                                                        onChange={() => varEnabledOnWebsiteOnChange(selectedVariation1)}
                                                                    />
                                                                </div>
                                                                <JC_Button
                                                                    text="Delete"
                                                                    isSecondary
                                                                    isSmall
                                                                    onClick={() => deleteVariation(selectedVariation1)}
                                                                    overrideClass={styles.deleteButton}
                                                                />
                                                            </div>
                                                        </div>)}

                                                        {!inCopyOtherMode && selectedProduct.Ex_Variations.filter(v => !v.Deleted).length > 0 && selectedVariation2 && selectedVariation2.VariationCategoryCode === category.Code && (
                                                        <div className={styles.variationDetailsFormContainer} key={`variation-details-${selectedVariation2.Id}`}>
                                                            <JC_Field
                                                                inputId={`variation-name-input-${selectedVariation2.Id}`}
                                                                label="Name"
                                                                type={FieldTypeEnum.Text}
                                                                value={selectedVariation2.Name}
                                                                onChange={(newValue) => varNameOnChange(selectedVariation2, newValue)}
                                                            />
                                                            <div className={styles.productSettingsRow}>
                                                                <JC_Field
                                                                    inputId={`variation-sort-order-input-${selectedVariation2.Id}`}
                                                                    label="Order"
                                                                    type={FieldTypeEnum.NumberStepperArrowsOnly}
                                                                    value={selectedVariation2.SortOrder}
                                                                    numberStepperOptions={{
                                                                        increment: 1,
                                                                        inverted: true,
                                                                        minValue: Math.min(...selectedProduct!.getVariationsForCategory(selectedVariation2.VariationCategoryCode).map(v => v.SortOrder)),
                                                                        maxValue: Math.max(...selectedProduct!.getVariationsForCategory(selectedVariation2.VariationCategoryCode).map(v => v.SortOrder))
                                                                    }}
                                                                    onChange={(newValue) => varSortOrderOnChange(selectedVariation2, newValue)}
                                                                />
                                                                <div className={styles.websiteCheckbox}>
                                                                    <JC_Checkbox
                                                                        label="Website"
                                                                        checked={selectedVariation2.EnabledOnWebsite}
                                                                        onChange={() => varEnabledOnWebsiteOnChange(selectedVariation2)}
                                                                    />
                                                                </div>
                                                                <JC_Button
                                                                    text="Delete"
                                                                    isSecondary
                                                                    isSmall
                                                                    onClick={() => deleteVariation(selectedVariation2)}
                                                                    overrideClass={styles.deleteButton}
                                                                />
                                                            </div>
                                                        </div>)}

                                                        {_buildSettings(category.Code)}
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        /* If there are no variations, show "Add Option" button and settings */
                                        <div className={styles.itemContainer}>
                                            <div className={styles.itemContentWrapper}>
                                                <div className={styles.variationDetailsAndSettingsContainer}>
                                                    {/* Add Option button - Hide in Copy Other mode */}
                                                    {!inCopyOtherMode &&
                                                    <div className={styles.addOptionContainer}>
                                                        <div className={styles.addOptionText}>Add Option</div>
                                                        <div className={styles.addNewButton} onClick={() => openNewVariationModal("")}><div>+</div></div>
                                                    </div>}
                                                    {/* Settings - Always show in both modes */}
                                                    {_buildSettings(undefined, inCopyOtherMode)}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            );
                        })()}
                    </div>

                    {/* - ------ - */}
                    {/* - LABELS - */}
                    {/* - ------ - */}

                    {!inCopyOtherMode && selectedProduct && (
                        <div className={styles.labelsContainer}>
                            {selectedVariationsData && (
                                <div className={styles.labelContainer}>
                                    <JC_Button
                                        overrideClass={styles.printButton}
                                        iconOverrideClass={styles.printIcon}
                                        iconName="Print"
                                        isSecondary
                                        linkToPage="admin/printFrontLabel"
                                        linkInNewTab
                                    />
                                    <JC_LabelFront
                                        product={selectedProduct}
                                        variations={[selectedVariation1, selectedVariation2].filter(Boolean) as ProductVariationModel[]}
                                        varData={selectedVariationsData}
                                    />
                                </div>
                            )}
                            {selectedVariationsData && selectedVariationsData.Ex_NutVals &&
                            <div className={styles.labelContainer}>
                                <JC_Button
                                    overrideClass={styles.printButton}
                                    iconOverrideClass={styles.printIcon}
                                    iconName="Print"
                                    isSecondary
                                    linkToPage="admin/printBackLabel"
                                    linkInNewTab
                                />
                                <JC_LabelBack
                                    product={selectedProduct}
                                    varData={selectedVariationsData}
                                />
                            </div>}

                            {/* Barcode Dropdown */}
                            {selectedVariationsData &&
                            <div className={styles.barcodeDropdownContainer}>
                                <JC_Dropdown
                                    type={DropdownTypeEnum.Default}
                                    label="Barcode"
                                    overrideClass={styles.barcodeDropdown}
                                    options={JC_Utils_CK.allBarcodes.map(barcode => ({
                                        OptionId: barcode,
                                        Label: barcode
                                    }))}
                                    selectedOptionId={selectedVariationsData.BarcodeNumber}
                                    onSelection={(newBarcodeNumber) => handleBarcodeSelection(newBarcodeNumber)}
                                    dataType="barcode"
                                    enableSearch
                                    scrollContainerToBottom={`.${styles.labelsContainer}`}
                                />
                            </div>}
                        </div>
                    )}

                    {/* - ---------- - */}
                    {/* - COPY OTHER - */}
                    {/* - ---------- - */}

                    {inCopyOtherMode && (
                        <CopyOtherMode
                            selectedCopyOtherVariation={selectedCopyOtherVariation}
                            selectedCopyOtherProduct={selectedCopyOtherProduct}
                            submitCopyOther={submitCopyOther}
                            cancelCopyOther={cancelCopyOther}
                            originalSelectedProduct={selectedProduct}
                            originalSelectedVariation1={selectedVariation1}
                            originalSelectedVariation2={selectedVariation2}
                        />
                    )}

                </div>}

                {/* Save */}
                {!inCopyOtherMode && selectedProduct != null &&
                <div className={styles.saveButton}><JC_Button text="Save" iconName="Save" onClick={save} /></div>}



                {/* - ------ - */}
                {/* - MODALS - */}
                {/* -------- - */}

                {/* Confirmation */}
                {confirmationModalData && (
                    <JC_ModalConfirmation
                        title={confirmationModalData.title}
                        text={confirmationModalData.text}
                        isOpen={confirmationModalData != null}
                        onCancel={() => setConfirmationModalData(null)}
                        submitButtons={confirmationModalData.submitButtons}
                    />
                )}

                {/* Edit Variation Category Modal */}
                <EditVariationCategoryModal
                    isOpen={editVariationCategoryModalOpen}
                    onCancel={() => setEditVariationCategoryModalOpen(false)}
                    onSave={handleVariationCategoryChange}
                    currentCategoryCode={currentVariationCategory}
                    product={selectedProduct}
                />

            </div>
        </React.Fragment>
    );
}
