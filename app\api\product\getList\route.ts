import { NextRequest, NextResponse } from "next/server";
import { ProductBusiness } from "../business";

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
    try {
        const url = new URL(request.url);
        const includeAll = url.searchParams.get("includeAll") === "true";
        
        const products = await ProductBusiness.GetList(includeAll);
        return NextResponse.json({ result: products });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
