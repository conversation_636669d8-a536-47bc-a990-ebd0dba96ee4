"use client"

import styles from "./page.module.scss";
import { Suspense, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_ProductTile from "../components/JC_ProductTile/JC_ProductTile";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import { JC_GetList } from "../services/JC_GetList";
import { ProductModel } from "../models/Product";
import { JC_FieldOption, D_FieldOption_LabelSizes } from "../models/ComponentModels/JC_FieldOption";
import { JC_PriceFilterModel } from "../models/ComponentModels/JC_PriceFilter";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils } from "../Utils";



export default function Page_Products_Suspense() {
    return <Suspense><Page_Products /></Suspense>
}

function Page_Products() {

    // - STATE - //

    const [products, setProducts] = useState<ProductModel[]>([]);
    const [filterOptions, setFilterOptions] = useState<JC_FieldOption[]>([{
            OptionId: "Savoury",
            Label: "Savoury",
            IconName: "Pizza"
        },{
            OptionId: "Sweet",
            Label: "Sweet",
            IconName: "Cupcake"
        },{
            OptionId: "Name",
            Label: "Name",
            IconName: "ChefHat"
        },{
            OptionId: "Price",
            Label: "Price",
            IconName: "DollarSign"
    }]);
    const [priceFilter, setPriceFilter] = useState<JC_PriceFilterModel>({})
    const [sort, setSort] = useState<JC_FieldOption[]>([{
            OptionId: "Default",
            Label: "Default",
            IconName: "CK"
        },{
            OptionId: "BestSelling",
            Label: "Best Selling",
            IconName: "Fire"
        },{
            OptionId: "Name",
            Label: "Name",
            IconName: "ChefHat"
        },{
            OptionId: "Price",
            Label: "Price",
            IconName: "DollarSign"
    }]);

    useEffect(() => {
        JC_GetList<ProductModel>("product", {}, ProductModel).then(productList => {
            setProducts(productList);
        });
    }, []);



    // - FILTERS - //

    function selectFilter(optionId:string) {
        let option = filterOptions.find(f => f.OptionId == optionId)!;
        option.Selected = !option.Selected;
        setFilterOptions([...filterOptions]);
    }


    // - MAIN - //

    let filteredProducts = products.sort((a,b) => a.SortOrder > b.SortOrder ? 1 : -1);
    if (priceFilter.FromPrice != null && priceFilter.FromPrice > 0) {
        filteredProducts = filteredProducts.filter(p => p.Price >= priceFilter.FromPrice!);
    }
    if (priceFilter.ToPrice != null && priceFilter.ToPrice > 0) {
        filteredProducts = filteredProducts.filter(p => p.Price <= priceFilter.ToPrice!);
    }

    return products.length === 0
        ? (<JC_Spinner isPageBody />)
        : (

            <div className={styles.mainContainer}>

                <JC_Title overrideClass={styles.titleOverride} title="Products" />

                {/* Back Button */}
                <div className={styles.backButton}>
                    <JC_Button
                        text="Products"
                        iconName="Arrow"
                        linkToPage="productGroups"
                    />
                </div>

                {/* List */}
                <div className={styles.productListContainer}>
                    {filteredProducts.map((product:ProductModel) => {
                        return <JC_ProductTile
                            key={product.Id}
                            name={product.Name}
                            imageName={`products/${product.ImageFileName}`}
                            href={`/product?id=${product.Id}`}
                        />
                    })}
                </div>

            </div>

        );
}



// {/* Filters */}
// <div className={styles.filtersContainer}>

//     {/* Filters */}
//     <div className={styles.filters}>
//         <JC_Dropdown
//             overrideClass={styles.filtersDropdown}
//             type={DropdownTypeEnum.Multi}
//             options={filterOptions}
//             onSelection={selectFilter}
//         />

//         <JC_PriceFilter initialValues={priceFilter} onChange={(newFilter) => setPriceFilter(newFilter)} />

//     </div>

//     {/* Sort */}
//     {/* <JC_Dropdown
//         type={DropdownTypeEnum.Default}
//         options={selectedFilters}
//         onSelection={selectFilter}
//     /> */}

// </div>