import { NextRequest, NextResponse } from "next/server";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { ProductVariationsDataBusiness } from "./business";

export const dynamic = 'force-dynamic';

// - --- - //
// - GET - //
// - --- - //

export async function GET(request: NextRequest) {
    try {
        const url = new URL(request.url);
        const productId = url.searchParams.get("productId");

        // If productId is provided, get variations data for that product
        if (productId) {
            const data = await ProductVariationsDataBusiness.GetForProduct(productId);
            return NextResponse.json(data);
        }

        // Otherwise, get all variations data
        const data = await ProductVariationsDataBusiness.GetAll();
        return NextResponse.json(data);
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - UPDATE - //
// - ------ - //

export async function POST(request: NextRequest) {
    try {
        const item: ProductVariationsDataModel = new ProductVariationsDataModel(await request.json());
        await ProductVariationsDataBusiness.Update(item);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
