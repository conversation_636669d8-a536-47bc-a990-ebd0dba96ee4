@import '../global';

.mainContainer {
    @include mainPageStyles;

    // Tab Body
    .tabBody {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 20px;

        // Charlottes
        .loginCharlotte, .registerCharlotte {
            position: absolute;
            height: auto;
            opacity: $charlotteOpacity;
        }
        .loginCharlotte {
            top: -70px;
            right: -240px;
            width: 160px;
        }
        .registerCharlotte {
            top: -10px;
            left: -240px;
            width: 185px;
        }

        // Small Text
        .smallTextButton {
            font-weight: bold;
            user-select: none;
            cursor: pointer;

            &:hover {
                color: $secondaryColor;
            }
        }

        // Password Requirements Field
        .passwordRequirementsField {
            margin-top: -15px;
            margin-bottom: -15px;
        }
    }

}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .loginCharlotte, .registerCharlotte {
        display: none;
    }
}