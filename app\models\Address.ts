import { _Base } from "./_Base";

export class AddressModel extends _Base {
    
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId: string;
    Line1: string;
    Line2: string;
    City: string;
    Postcode: string;
    Country: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<AddressModel>) {
        super(init);
        this.Id = "";
        this.UserId = "";
        this.Line1 = "";
        this.Line2 = "";
        this.City = "";
        this.Postcode = "";
        this.Country = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Country} | ${this.City} | ${this.Line1}`;
    }
}
