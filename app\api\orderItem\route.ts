import { NextRequest, NextResponse } from "next/server";
import { sql } from "@vercel/postgres";
import { OrderItemModel } from "@/app/models/OrderItem";
import { OrderBusiness } from "../order/business";
import { OrderItemBusiness } from "./business";


// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {

    try {

        const params = new URL(request.url).searchParams;
        const userId = params.get("userId")!;

        const orderItemData:OrderItemModel = await request.json();

        // IF there is no Order for this OrderItem yet, create an Order
        if (!(await OrderItemBusiness.CheckOrderExists(orderItemData.OrderId))) {
            orderItemData.OrderId = (await OrderBusiness.CreateOrderFromUserId(userId)).Id;
        }

        // Create the OrderItem
        await OrderItemBusiness.Create(orderItemData);

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}


// ---------- //
// - UPDATE - //
// ---------- //

// Update "Quantity" or "Deleted"
export async function POST(request: NextRequest) {
    try {
        const orderItemData:OrderItemModel = await request.json();
        await OrderItemBusiness.Update(orderItemData);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}