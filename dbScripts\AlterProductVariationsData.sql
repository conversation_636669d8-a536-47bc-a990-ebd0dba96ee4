-- SQL script to modify ProductVariationsData table to allow NULL for ProductVariationIds
-- When ProductVariationIds is NULL, this means the data is related just to a Product

-- First, check if the column is already nullable
DO $$
DECLARE
    column_is_nullable boolean;
BEGIN
    SELECT (is_nullable = 'YES') INTO column_is_nullable
    FROM information_schema.columns
    WHERE table_name = 'ProductVariationsData'
    AND column_name = 'ProductVariationIds';

    IF column_is_nullable THEN
        RAISE NOTICE 'ProductVariationIds column is already nullable. No changes needed.';
    ELSE
        -- Alter the table to make ProductVariationIds nullable
        ALTER TABLE public."ProductVariationsData"
        ALTER COLUMN "ProductVariationIds" DROP NOT NULL;

        RAISE NOTICE 'ProductVariationIds column has been modified to allow NULL values.';
    END IF;
END $$;

-- Add a comment to the column to explain the meaning of NULL
COMMENT ON COLUMN public."ProductVariationsData"."ProductVariationIds" IS 'Combination of ProductVariation''s that gives this BarcodeNumber. When NULL, this means the data is related just to a Product.';

-- Output success message
SELECT 'ProductVariationsData table has been updated successfully.' as result;
