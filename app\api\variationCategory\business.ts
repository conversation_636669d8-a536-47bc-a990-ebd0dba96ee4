import { sql } from "@vercel/postgres";
import { VariationCategoryModel } from "@/app/models/VariationCategory";

export class VariationCategoryBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll() {
        return (await sql<VariationCategoryModel>`
            SELECT * FROM public."VariationCategory"
            WHERE "Deleted" = 'False'
            ORDER BY "SortOrder"
        `).rows;
    }

}