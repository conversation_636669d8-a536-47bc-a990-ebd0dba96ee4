import { LabelSizeEnum } from "@/app/enums/LabelSize";
import { ProductModel } from "../Product";
import { ProductVariationModel } from "../ProductVariation";
import { ProductVariationsDataModel } from "../ProductVariationsData";

export interface JC_LabelPageModel {
    overrideClass?: string;
    product: ProductModel;
    variations?: ProductVariationModel[];
    varData: ProductVariationsDataModel;
    labelSize: LabelSizeEnum;
    paddingStyle?: React.CSSProperties;
    disablePrint?: boolean;
}
