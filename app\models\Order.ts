import { OrderItemModel } from "./OrderItem";
import { ProductModel } from "./Product";
import { _Base } from "./_Base";
import { OrderStatusEnum } from "../enums/OrderStatus";
import { JC_Utils } from "../Utils";
import { _OrderValuesAtSubmissionModel } from "./_OrderValuesAtSubmission";

export class OrderModel extends _Base {

    static apiRoute:string = "order";
    static apiRoute_getByUserAndStatus:string = "order/getByUserAndStatus";
    static apiRoute_getListUserPreviousOrders:string = "order/getListUserPreviousOrders";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId?: string;
    OrderStatusCode: string;
    SubmittedDate?: Date;
    CompletedDate?: Date;
    ValuesAtSubmissionJson?: string;

    // Extended
    Ex_OrderItems: OrderItemModel[];
    Ex_ValuesAtSubmission: _OrderValuesAtSubmissionModel[];

    // UI
    UI_Expanded?: boolean;


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<OrderModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.UserId = undefined;
        this.OrderStatusCode = OrderStatusEnum.Bag;
        this.SubmittedDate = undefined;
        this.CompletedDate = undefined;
        this.ValuesAtSubmissionJson = undefined;
        this.Ex_OrderItems = [];
        Object.assign(this, init);
        // Extended
        this.Ex_OrderItems = (init?.Ex_OrderItems?.map(o => new OrderItemModel(o))) ?? [];
        this.Ex_ValuesAtSubmission = (init?.Ex_ValuesAtSubmission?.map(o => new _OrderValuesAtSubmissionModel(o))) ?? [];
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.UserId} | ${this.OrderStatusCode}`;
    }


    // - ----- - //
    // - UTILS - //
    // - ----- - //

    getTotalItems() : number {
        return this.Ex_OrderItems.reduce((prev, cur) => prev + cur.Quantity, 0);
    }

    getTotalPrice(products: ProductModel[]) : number {
        return Number((
            this.Ex_OrderItems.reduce((prev, cur) => prev + (cur.getPrice(products) * cur.Quantity), 0)
        ).toFixed(2));
    }
}
