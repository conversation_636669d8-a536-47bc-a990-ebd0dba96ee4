import { JC_Utils, JC_Utils_Validation } from "@/app/Utils";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import { JC_FieldOption } from "./JC_FieldOption";

export interface JC_FieldNumberStepperModel {
    increment: number;
    inverted?: boolean;
    minValue?: number;
    maxValue?: number;
}

export interface JC_FieldRichTextModel {
    enableColor?: boolean;
    enableBold?: boolean;
    enableItalic?: boolean;
    enableDegree?: boolean;
    height?: number;
    width?: number;
}

export interface JC_FieldModel {
    overrideClass?: string;
    inputOverrideClass?: string;
    inputId: string;
    type: FieldTypeEnum;
    label?: string;
    iconName?: string;
    placeholder?: string;
    readOnly?: boolean;
    autoFocus?: boolean;
    value?: string | number; // This only works if an "inputId" is supplied
    defaultValue?: string | number;
    numberStepperOptions?: JC_FieldNumberStepperModel; // Options for NumberStepper and NumberStepperArrowsOnly
    richTextOptions?: JC_FieldRichTextModel; // Options for RichText fields
    // Direct rich text options (these will be converted to richTextOptions)
    richTextEnableColor?: boolean;
    richTextEnableBold?: boolean;
    richTextEnableItalic?: boolean;
    richTextEnableDegree?: boolean;
    richTextWidth?: number;
    richTextHeight?: number;
    decimalPlaces?: number; // For Number and NumberStepper - the number of decimal places to display
    options?: JC_FieldOption[]; // For Dropdown - the options to display
    enableSearch?: boolean; // For Dropdown - whether to enable search functionality
    onClick?: () => void;
    onChange?: (newValue:string) => void;
    onBlur?: (newValue:string) => void;
    onEnter?: (event:any) => void;
    onEscape?: (event:any) => void;
    validate?: (value:string|number|undefined) => string;
    required?: boolean;
}



// - Defaults For Specific Fields - //

// Name
export function D_FieldModel_Name() : JC_FieldModel {
    return {
        inputId: "name-input",
        type: FieldTypeEnum.Text,
        label: "Name",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a name." : ""
    };
}

// First Name
export function D_FieldModel_FirstName() : JC_FieldModel {
    return {
        inputId: "first-name-input",
        type: FieldTypeEnum.Text,
        label: "First Name",
        iconName: "User",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a first name." : ""
    };
}

// Last Name
export function D_FieldModel_LastName() : JC_FieldModel {
    return {
        inputId: "last-name-input",
        type: FieldTypeEnum.Text,
        label: "Last Name",
        iconName: "User",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a last name." : ""
    };
}

// Email
export function D_FieldModel_Email() : JC_FieldModel {
    return {
        inputId: "email-input",
        type: FieldTypeEnum.Email,
        label: "Email",
        iconName: "Email",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                ? "Enter an email."
                                : !JC_Utils_Validation.validEmail(v)
                                    ? "Enter a valid email"
                                    : ""
    };
}

// Phone
export function D_FieldModel_Phone(hideOptionalText: boolean = false) : JC_FieldModel {
    return {
        inputId: "contact-email-input",
        type: FieldTypeEnum.Text,
        label: hideOptionalText ? "Phone" : "Phone (optional)",
        iconName: "Phone",
        validate: (v:any) => !JC_Utils.stringNullOrEmpty(v) && !JC_Utils_Validation.validPhone(v) ? "Enter a valid phone number" : ""
    };
}

// Rich Text
export function D_FieldModel_RichText(options?: {
    label?: string;
    inputId?: string;
    width?: number;
    height?: number;
    enableColor?: boolean;
    enableBold?: boolean;
    enableItalic?: boolean;
    enableDegree?: boolean;
}) : JC_FieldModel {
    return {
        inputId: options?.inputId || "rich-text-input",
        type: FieldTypeEnum.RichText,
        label: options?.label || "Text",
        richTextOptions: {
            width: options?.width || 280,
            height: options?.height || 160,
            enableColor: options?.enableColor !== undefined ? options.enableColor : true,
            enableBold: options?.enableBold !== undefined ? options.enableBold : true,
            enableItalic: options?.enableItalic !== undefined ? options.enableItalic : true,
            enableDegree: options?.enableDegree !== undefined ? options.enableDegree : false
        }
    };
}