import { cookies } from "next/headers";
import <PERSON><PERSON> from "stripe";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/enums/CookieKey";
import { NextRequest, NextResponse } from "next/server";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
    try {
        cookies().delete(CookieKeyEnum.JC_PaymentIntentId);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.error(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}