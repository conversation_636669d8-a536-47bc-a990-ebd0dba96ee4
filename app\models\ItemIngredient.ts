import { _Base } from "./_Base";
import { JC_Utils } from "../Utils";
import { IngredientModel } from "./Ingredient";

export class ItemIngredientModel extends _Base {

    static apiRoute:string = "itemIngredient";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ProductId: string | null;
    ProductVariationId: string | null;
    IngredientId: string;
    AmountGrams: number;
    ShowPercent: boolean;

    // Extended
    Ex_ProductName?: string;
    Ex_ProductVariationName?: string;
    Ex_Ingredient?: IngredientModel;

    // UI
    UI_HasChanges?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ItemIngredientModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.ProductId = null;
        this.ProductVariationId = null;
        this.IngredientId = JC_Utils.emptyGuid();
        this.AmountGrams = 0;
        this.ShowPercent = false;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        if (this.ProductVariationId) {
            return `${this.Ex_ProductVariationName} | ${this.Ex_Ingredient?.Name} | ${this.AmountGrams}g`;
        } else {
            return `${this.Ex_ProductName} | ${this.Ex_Ingredient?.Name} | ${this.AmountGrams}g`;
        }
    }
}
