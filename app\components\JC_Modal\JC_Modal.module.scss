@import '../../global';

$topBottomPadding: 26px;

// Black background overlay
.blackOverlay {
    z-index: 998;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0; left: 0;
    background-color: $offBlack;
    opacity: 0.35;
}

// Modal
.modalContainer {
    z-index: 999;
    padding: $topBottomPadding 30px $topBottomPadding 30px;
    width: max-content;
    max-width: calc(100% - 12px);
    min-width: 350px;
    height: max-content;
    min-height: 200px;
    position: fixed;
    left: 50%; top: 50%; transform: translate(-50%, -50%);
    background-color: $offWhite;
    outline: solid $largeBorderWidth $offBlack;
    border-radius: $smallBorderRadius;

    // Loading spinner
    .loadingSpinner {
        position: absolute;
        left: 0;
        top: 60%;
        transform: translateY(-50%);
    }

    // Hide content when loading
    .modalContentHidden {
        visibility: hidden;
    }
}

.modalContainer.forceTransparent {
    background-color: transparent;
    outline: none;
}

// Title
.title {
    margin: calc(16px - $topBottomPadding) auto 16px auto;
    width: max-content;
    text-align: center;
    font-size: 28px;
    font-weight: bold;
}

// Cancel button
.cancelButton {
    position: absolute;
    right: -44px;
    top: 2px;
    width: 18px;
    height: auto;
    padding: 5px;
    outline: solid $tinyBorderWidth $primaryColor;
    background-color: $offWhite;
    border-radius: 50%;
    color: $primaryColor;
    font-size: 27px;
    font-weight: bold;
    cursor: pointer;
}
.modalContainer.forceTransparent .cancelButton {
    right: -18px;
    top: 38px;
}