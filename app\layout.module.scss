@import './global';

// Root
.rootMainContainer {
    margin: 0;
    width: 100vw;
    height: 100vh;
    display: flex; // So "JC_Footer" can stretch to bottom of page if set "flex-grow: 1" on it
    flex-direction: column;
    font-family: var(--font-inter); // Font family used everywhere, can only set this if have "inter.variable" in ts
    background-color: $offWhite;

    // Every Page
    .pageContainer {
        flex-grow: 1;
        position: relative;
        box-sizing: border-box;
    }
    
    // textarea
    textarea {
        font-family: var(--font-inter);
    }

    // a
    a {
        color: black;
        text-decoration: none;
    }
}