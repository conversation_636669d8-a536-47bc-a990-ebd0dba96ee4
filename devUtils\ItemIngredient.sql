SELECT "itemIng"."Id"
      ,"itemIng"."ProductId"
      ,"product"."Name" "__ProductName"
      ,"itemIng"."ProductVariationId"
      ,"variation"."Name" "__ProductVariationName"
      ,"itemIng"."IngredientId"
      ,"ingredient"."Name" "__IngredientName"
      ,"itemIng"."AmountGrams"
      ,"itemIng"."ShowPercent"
      ,"itemIng"."CreatedAt"
      ,"itemIng"."ModifiedAt"
      ,"itemIng"."Deleted"
FROM public."ItemIngredient" "itemIng"
LEFT JOIN public."Product" "product" ON "itemIng"."ProductId" = "product"."Id"
LEFT JOIN public."ProductVariation" "variation" ON "itemIng"."ProductVariationId" = "variation"."Id"
INNER JOIN public."Ingredient" "ingredient" ON "itemIng"."IngredientId" = "ingredient"."Id"
WHERE 1=1
      AND "itemIng"."Deleted" = 'False'
      -- AND "itemIng"."ProductId" = '8f835d15-dc2a-48c3-974e-7d6c1b98fa11'
      -- AND "itemIng"."ProductVariationId" = '11c4f7de-7638-481b-b02b-70bbf8e6bce1'
ORDER BY 
    CASE 
        WHEN "itemIng"."ProductId" IS NOT NULL THEN "product"."Name"
        ELSE "variation"."Name"
    END,
    "ingredient"."Name";

-- Delete
-- UPDATE public."ItemIngredient"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = '578e5928-c575-4d22-be67-dbc89d32aa58'

-- Un-delete
-- UPDATE public."ItemIngredient"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = '578e5928-c575-4d22-be67-dbc89d32aa58'
