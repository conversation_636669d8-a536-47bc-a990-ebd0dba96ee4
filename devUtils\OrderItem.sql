SELECT "item"."Id"
      ,"item"."OrderId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"user"."CompanyName" "__UserCompany"
      ,"status"."Name" "__OrderStatus"
      ,"payment"."PaymentStatusCode" "__PaymentStatusCode"
      ,"paymentStatus"."Name" "__PaymentStatus"
      ,"item"."ProductId"
      ,"product"."Name" "__Product"
      ,"item"."ProductVariationIds"
      ,COALESCE(
          (SELECT array_agg("variation"."Name" ORDER BY "variation_ord")
          FROM unnest("item"."ProductVariationIds") WITH ORDINALITY AS "variationId"(id, variation_ord)
          JOIN public."ProductVariation" "variation" ON "variation"."Id" = "variationId".id::uuid
          ), '{}'
      ) AS "__ProductVariations"
      ,"item"."Quantity"
      ,"item"."CreatedAt"
      ,"item"."ModifiedAt"
      ,"item"."Deleted"
FROM public."OrderItem" "item"
INNER JOIN public."Order" "order" ON "item"."OrderId" = "order"."Id"
INNER JOIN public."User" "user" ON "order"."UserId" = "user"."Id"
INNER JOIN public."OrderStatus" "status" ON "order"."OrderStatusCode" = "status"."Code"
INNER JOIN public."Product" "product" ON "item"."ProductId" = "product"."Id"
LEFT JOIN public."Payment" "payment" ON "order"."Id" = "payment"."OrderId"
LEFT JOIN public."PaymentStatus" "paymentStatus" ON "payment"."PaymentStatusCode" = "paymentStatus"."Code"
WHERE 1=1
      AND "item"."Deleted" = 'False'
ORDER BY "item"."CreatedAt" DESC;