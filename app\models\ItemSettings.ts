import { _Base } from "./_Base";
import { JC_Utils } from "../Utils";
import { LabelSizeEnum } from "../enums/LabelSize";

export class ItemSettingsModel extends _Base {

    static apiRoute:string = "itemSettings";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ProductId: string | null;
    ProductVariationId: string | null;
    ServingSizeGrams: number;
    ServingsPerPack: number;
    WeightChange: number;
    LabelSize: LabelSizeEnum;
    PackWeightText: string;
    FrontLabelNameOverride: string;
    FrontLabelNameXOffset: number;
    BackLabelNameOverride: string;
    IngredientsFontSize: number;
    IngredientsYOffset: number;
    InstructionsText: string;
    InstructionsFontSize: number;
    NoteText: string;
    NoteFontSize: number;
    AllergensText: string;
    FreeFromText: string;
    BiodegOverrideText: string;
    StoreFrozenOverride: string;
    IconName: string;
    IconVisible: boolean;
    IconX: number;
    IconY: number;
    IconSize: number;
    NutValsEnabled: boolean;

    // Extended
    Ex_ProductName?: string;
    Ex_ProductVariationName?: string;

    // UI
    UI_HasChanges?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ItemSettingsModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.ProductId = "";
        this.ProductVariationId = null;
        this.ServingSizeGrams = 0;
        this.ServingsPerPack = 0;
        this.WeightChange = 1;
        this.LabelSize = LabelSizeEnum.Large;
        this.PackWeightText = "";
        this.FrontLabelNameOverride = "";
        this.FrontLabelNameXOffset = 0;
        this.BackLabelNameOverride = "";
        this.IngredientsFontSize = 10;
        this.IngredientsYOffset = 0;
        this.InstructionsText = "";
        this.InstructionsFontSize = 10;
        this.NoteText = "";
        this.NoteFontSize = 10;
        this.AllergensText = "";
        this.FreeFromText = "";
        this.BiodegOverrideText = "";
        this.StoreFrozenOverride = "";
        this.IconName = "";
        this.IconVisible = true;
        this.IconX = 9;
        this.IconY = -12;
        this.IconSize = 32;
        this.NutValsEnabled = false;

        // Apply the init values
        Object.assign(this, init);

        // Ensure numeric values are properly converted to numbers
        if (init) {
            this.IngredientsYOffset = Number(this.IngredientsYOffset || 0);
            this.IngredientsFontSize = Number(this.IngredientsFontSize || 10);
            this.InstructionsFontSize = Number(this.InstructionsFontSize || 10);
            this.NoteFontSize = Number(this.NoteFontSize || 10);
            this.IconX = Number(this.IconX || 9);
            this.IconY = Number(this.IconY || -12);
            this.IconSize = Number(this.IconSize || 32);
            this.FrontLabelNameXOffset = Number(this.FrontLabelNameXOffset || 0);
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        if (this.ProductVariationId) {
            return `Settings for ${this.Ex_ProductVariationName}`;
        } else {
            return `Settings for ${this.Ex_ProductName}`;
        }
    }
}
