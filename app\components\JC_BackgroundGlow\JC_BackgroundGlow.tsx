"use client"

import styles from "./JC_BackgroundGlow.module.scss";
import React, { useEffect, useState, useRef } from 'react';
import { JC_Utils } from "@/app/Utils";

// Configuration for glow orbs
const GLOW_CONFIG = {
    // Size as a percentage of the larger screen dimension (width or height)
    SIZE_FACTOR: 0.25,
    // Speed range for orb movement
    MIN_SPEED: 1.2,
    MAX_SPEED: 2,
    // Minimum speed to prevent orbs from getting stuck
    MIN_VELOCITY: 0.5,
    // Maximum speed to prevent orbs from moving too fast
    MAX_VELOCITY: 2.5,
    // Bounce randomness factors (values closer to 1.0 make bounces more predictable)
    MIN_BOUNCE_FACTOR: 0.8,
    MAX_BOUNCE_FACTOR: 1.2,
    // Number of orbs to display
    ORB_COUNT: 2,
    // How far past the window edge orbs can travel before bouncing (in pixels)
    BOUNDARY_EXTENSION: 50
};

interface GlowOrb {
    id: number;
    x: number;
    y: number;
    size: number;
    velocityX: number;
    velocityY: number;
    opacity: number;
}

export default function JC_BackgroundGlow(_: Readonly<{
    overrideClass?: string;
}>) {
    const containerRef = useRef<HTMLDivElement>(null);
    const [orbs, setOrbs] = useState<GlowOrb[]>([]);
    const animationRef = useRef<number | null>(null);
    const orbCount = GLOW_CONFIG.ORB_COUNT;

    // Initialize orbs
    useEffect(() => {
        if (typeof window === 'undefined') return;

        const largerDimension = Math.max(window.innerWidth, window.innerHeight);
        const orbSize = largerDimension * GLOW_CONFIG.SIZE_FACTOR;

        const initialOrbs: GlowOrb[] = [];
        for (let i = 0; i < orbCount; i++) {
            initialOrbs.push({
                id: i,
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                size: orbSize,
                velocityX: (Math.random() * (GLOW_CONFIG.MAX_SPEED - GLOW_CONFIG.MIN_SPEED) + GLOW_CONFIG.MIN_SPEED) * (Math.random() > 0.5 ? 1 : -1),
                velocityY: (Math.random() * (GLOW_CONFIG.MAX_SPEED - GLOW_CONFIG.MIN_SPEED) + GLOW_CONFIG.MIN_SPEED) * (Math.random() > 0.5 ? 1 : -1),
                opacity: 0.5
            });
        }

        setOrbs(initialOrbs);

        // Handle window resize
        const handleResize = () => {
            const newLargerDimension = Math.max(window.innerWidth, window.innerHeight);
            const newOrbSize = newLargerDimension * GLOW_CONFIG.SIZE_FACTOR;

            setOrbs(prevOrbs => prevOrbs.map(orb => ({
                ...orb,
                size: newOrbSize
            })));
        };

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [orbCount]);

    // Animate orbs
    useEffect(() => {
        if (orbs.length === 0 || typeof window === 'undefined') return;

        const animate = () => {
            setOrbs(prevOrbs => {
                return prevOrbs.map(orb => {
                    let { x, y, velocityX, velocityY } = orb;
                    const halfSize = orb.size / 2;

                    // Update position
                    x += velocityX;
                    y += velocityY;

                    // Boundary checks with extended boundaries
                    const rightBoundary = window.innerWidth + GLOW_CONFIG.BOUNDARY_EXTENSION;
                    const bottomBoundary = window.innerHeight + GLOW_CONFIG.BOUNDARY_EXTENSION;
                    const leftBoundary = -GLOW_CONFIG.BOUNDARY_EXTENSION;
                    const topBoundary = -GLOW_CONFIG.BOUNDARY_EXTENSION;

                    // Apply bounce with randomness
                    if (x + halfSize > rightBoundary || x - halfSize < leftBoundary) {
                        velocityX = -velocityX * (GLOW_CONFIG.MIN_BOUNCE_FACTOR + Math.random() * (GLOW_CONFIG.MAX_BOUNCE_FACTOR - GLOW_CONFIG.MIN_BOUNCE_FACTOR));
                        // Ensure minimum velocity
                        velocityX = Math.sign(velocityX) * Math.max(Math.abs(velocityX), GLOW_CONFIG.MIN_VELOCITY);
                        // Cap maximum velocity
                        velocityX = Math.sign(velocityX) * Math.min(Math.abs(velocityX), GLOW_CONFIG.MAX_VELOCITY);
                    }

                    if (y + halfSize > bottomBoundary || y - halfSize < topBoundary) {
                        velocityY = -velocityY * (GLOW_CONFIG.MIN_BOUNCE_FACTOR + Math.random() * (GLOW_CONFIG.MAX_BOUNCE_FACTOR - GLOW_CONFIG.MIN_BOUNCE_FACTOR));
                        // Ensure minimum velocity
                        velocityY = Math.sign(velocityY) * Math.max(Math.abs(velocityY), GLOW_CONFIG.MIN_VELOCITY);
                        // Cap maximum velocity
                        velocityY = Math.sign(velocityY) * Math.min(Math.abs(velocityY), GLOW_CONFIG.MAX_VELOCITY);
                    }

                    return {
                        ...orb,
                        x,
                        y,
                        velocityX,
                        velocityY
                    };
                });
            });

            animationRef.current = requestAnimationFrame(animate);
        };

        animationRef.current = requestAnimationFrame(animate);

        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [orbs.length]);

    return (
        <div
            ref={containerRef}
            className={`${styles.glowContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}
        >
            {orbs.map(orb => (
                <div
                    key={orb.id}
                    className={styles.glow}
                    style={{
                        width: `${orb.size}px`,
                        height: `${orb.size}px`,
                        transform: `translate(${orb.x - orb.size / 2}px, ${orb.y - orb.size / 2}px)`,
                        opacity: orb.opacity
                    }}
                />
            ))}
        </div>
    );
}
