import { sql } from "@vercel/postgres";
import { ContactModel } from "@/app/models/Contact";

export class ContactBusiness {

    // - ------ - //
    // - CREATE - //
    // - ------ - //
    
    static async Create(contactData:ContactModel) {
        await sql`
            INSERT INTO public."Contact"
            (
                "Id",
                "UserId",
                "Name",
                "Email",
                "Phone",
                "Message",
                "CreatedAt"
            )
            VALUES
            (
                ${contactData.Id},
                ${contactData.UserId},
                ${contactData.Name},
                ${contactData.Email},
                ${contactData.Phone},
                ${contactData.Message},
                ${new Date().toUTCString()}
            )
        `;
    }

}