"use client"

import React, { useState, useEffect, useLayoutEffect } from 'react';
import JC_Modal from "@/app/components/JC_Modal/JC_Modal";
import JC_Form from "@/app/components/JC_Form/JC_Form";
import { D_FieldModel_Name } from "@/app/models/ComponentModels/JC_Field";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { JC_GetList } from "@/app/services/JC_GetList";
import { VariationCategoryModel } from "@/app/models/VariationCategory";
import { JC_FieldOption } from "@/app/models/ComponentModels/JC_FieldOption";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import { JC_Utils_CK } from "@/app/Utils";
import styles from "./NewVariationModal.module.scss";

interface NewVariationModalProps {
    isOpen: boolean;
    onCancel: () => void;
    onSave: (variation: ProductVariationModel) => void;
    categoryCode: string;
    product: ProductModel | null;
    allProducts: ProductModel[];
}

export default function NewVariationModal({
    isOpen,
    onCancel,
    onSave,
    categoryCode,
    product,
    allProducts
}: NewVariationModalProps) {
    const [initialised, setInitialised] = useState<boolean>(false);
    const [newVariationName, setNewVariationName] = useState<string>("");
    const [currentCategory, setCurrentCategory] = useState<string>(categoryCode);
    // Removed unused state variable: variationCategories
    const [categoryOptions, setCategoryOptions] = useState<JC_FieldOption[]>([]);
    const [variationCategories, setVariationCategories] = useState<VariationCategoryModel[]>([]);

    // Update current category when prop changes
    useEffect(() => {
        setCurrentCategory(categoryCode);
    }, [categoryCode]);

    // Focus the name input field when the modal opens
    useLayoutEffect(() => {
        if (isOpen && initialised) {
            // Focus the name input field after the modal is fully rendered
            setTimeout(() => {
                const nameInput = document.getElementById("new-variation-name-input") as HTMLInputElement;
                if (nameInput) {
                    nameInput.focus();
                }
            }, 100);
        }
    }, [isOpen, initialised]);

    // Initialize data when modal is opened
    useEffect(() => {
        if (isOpen) {
            // Load all required data
            // Only load variation categories, barcodes will be handled in the backend
            JC_GetList<VariationCategoryModel>("variationCategory", {}, VariationCategoryModel)
                .then((categories) => {
                    // Process variation categories
                    const filteredCategories = categories
                        .filter(c => c.Code !== "None")
                        .sort((a, b) => a.SortOrder - b.SortOrder);

                    // Get existing variation categories from the product
                    const existingCategories = product?.Ex_Variations
                        ?.filter(v => !v.Deleted)
                        ?.map(v => v.VariationCategoryCode) || [];

                    // Get unique categories (a product can have multiple variations in the same category)
                    const uniqueExistingCategories = existingCategories.filter((value, index, self) =>
                        self.indexOf(value) === index
                    );

                    // If product has 2 or more variation categories, only those should be enabled
                    const hasMultipleCategories = uniqueExistingCategories.length >= 2;

                    // Store the variation categories for later use
                    setVariationCategories(filteredCategories);

                    // Create options for JC_Radio
                    const options = filteredCategories.map(category => ({
                        OptionId: category.Code,
                        Label: category.Name,
                        // Disable options that are not in the existing categories if the product has 2+ categories
                        Disabled: hasMultipleCategories && !uniqueExistingCategories.includes(category.Code)
                    }));

                    setCategoryOptions(options);

                    // If we have existing categories and the current category is disabled, select the first enabled one
                    if (hasMultipleCategories && options.find(o => o.OptionId === currentCategory)?.Disabled) {
                        const firstEnabledOption = options.find(o => !o.Disabled);
                        if (firstEnabledOption) {
                            setCurrentCategory(firstEnabledOption.OptionId);
                        }
                    }

                    // Set initialised to true after all data is loaded
                    setTimeout(() => {
                        setInitialised(true);
                        // Focus the name input field after initialization
                        setTimeout(() => {
                            const nameInput = document.getElementById("new-variation-name-input") as HTMLInputElement;
                            if (nameInput) {
                                nameInput.focus();
                            }
                        }, 100);
                    }, 100);
                });
        }
    }, [isOpen, product]);

    // Create new variation
    function createNewVariation() {
        if (!product || newVariationName.trim() === "") return;

        // Ensure a category is selected
        if (currentCategory === "") {
            // If no category is selected and we have options, select the first enabled one
            const enabledOptions = categoryOptions.filter(option => !option.Disabled);
            if (enabledOptions.length > 0) {
                setCurrentCategory(enabledOptions[0].OptionId);
                return; // Don't proceed until category is set
            }
            return; // Don't proceed if no categories available
        }

        // Check if the selected category is disabled
        const selectedOption = categoryOptions.find(option => option.OptionId === currentCategory);
        if (selectedOption?.Disabled) {
            // Find the first enabled option
            const firstEnabledOption = categoryOptions.find(option => !option.Disabled);
            if (firstEnabledOption) {
                setCurrentCategory(firstEnabledOption.OptionId);
                return; // Don't proceed until an enabled category is selected
            }
            return; // Don't proceed if all categories are disabled
        }

        // Find the selected category object from the variationCategories array
        const selectedCategory = variationCategories.find(category => category.Code === currentCategory);

        // Create a new variation with the category object
        const newVariation = new ProductVariationModel({
            ProductId: product.Id,
            VariationCategoryCode: currentCategory,
            Name: newVariationName,
            UI_HasChanges: true,
            Ex_Category: selectedCategory // This is now strongly typed as VariationCategoryModel
        });

        // Get the next sort order for this category
        const variationsInCategory = product.Ex_Variations
            .filter(v => !v.Deleted && v.VariationCategoryCode === currentCategory);

        const sortOrders = variationsInCategory.map(v => v.SortOrder);
        newVariation.SortOrder = sortOrders.length > 0 ? Math.max(...sortOrders) + 1 : 1;

        try {
            // Get all used barcodes from all products passed from the parent component
            const usedBarcodes = allProducts.flatMap(p => p.getAllBarcodes());

            // Get all available barcodes from JC_Utils_CK
            const allBarcodes = JC_Utils_CK.allBarcodes;

            // Find unused barcodes
            const unusedBarcodes = allBarcodes.filter(barcode => !usedBarcodes.includes(barcode));

            // Create ProductVariationsData records for this new variation with unused barcodes
            const variationsData = ProductVariationsDataModel.createForNewVariation(
                newVariation.Id,
                product.Id,
                currentCategory,
                product.Ex_Variations,
                unusedBarcodes
            );

            // Add variationsData to the newVariation object as a temporary property
            (newVariation as any).Ex_ProductVariationsData = variationsData;

            // Call the save callback with the new variation (which now contains the variationsData)
            onSave(newVariation);

            // Reset the form data to initial values
            setNewVariationName("");

            // Focus the name input field after saving
            setTimeout(() => {
                const nameInput = document.getElementById("new-variation-name-input") as HTMLInputElement;
                if (nameInput) {
                    nameInput.focus();
                }
            }, 50);
        } catch (error) {
            console.error("Error creating variation data:", error);
            // Still proceed with saving the variation, but without barcodes
            // The backend will assign barcodes when the variation is saved
            const emptyVariationsData: ProductVariationsDataModel[] = [];
            (newVariation as any).Ex_ProductVariationsData = emptyVariationsData;
            onSave(newVariation);
            setNewVariationName("");
        }
    }

    return (
        <JC_Modal
            isOpen={isOpen}
            onCancel={onCancel}
            title="New Option"
            isLoading={!initialised}
        >
            <div className={styles.modalContent}>
                {/* Combined Form with Category Selection and Name */}
                <JC_Form
                    fields={[
                        // Only include the category field if options are available
                        ...(categoryOptions.length > 0 ? [{
                            inputId: "variation-category",
                            type: FieldTypeEnum.Radio,
                            label: "Category",
                            options: categoryOptions,
                            value: currentCategory,
                            onChange: (newCategoryCode: string) => {
                                setCurrentCategory(newCategoryCode);
                                // Focus the name input field after selecting a category
                                setTimeout(() => {
                                    const nameInput = document.getElementById("new-variation-name-input") as HTMLInputElement;
                                    if (nameInput) {
                                        nameInput.focus();
                                    }
                                }, 50);
                            },
                            validate: (value: string | number | undefined) => !value ? "Please select an option" : ""
                        }] : []),
                        // Name field
                        {
                            ...D_FieldModel_Name(),
                            value: newVariationName,
                            onChange: (newValue: string) => setNewVariationName(newValue),
                            autoFocus: true,
                            inputId: "new-variation-name-input" // Custom ID to reference with ref
                        }
                    ]}
                    onSubmit={createNewVariation}
                    submitButtonText="Create"
                />
            </div>
        </JC_Modal>
    );
}
