@import '../../global';
@import '../admin';

.mainContainer {
    width: 100%;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .pageTitle {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 30px;
        color: $primaryColor;
    }

    .settingsContainer {
        @include adminFormContainerStyles;
        width: 800px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .settingsHeader {
            display: grid;
            grid-template-columns: 150px 1fr 200px;
            gap: 15px;
            font-weight: bold;

            .codeHeader, .descriptionHeader, .valueHeader {
                padding: 0 10px;
                text-align: center;
            }
        }

        .settingRow {
            display: grid;
            grid-template-columns: 150px 1fr 200px;
            gap: 15px;
            align-items: center;

            .codeField, .valueField {
                width: 100%;
            }

            .descriptionField {
                width: 100%;
                .descriptionFieldInput {
                    width: 100%;
                }
            }
        }

        .addNewButton {
            margin: auto;
            margin-top: 5px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            outline: solid $smallBorderWidth $offBlack;
            background-color: $offWhite;
            user-select: none;
            cursor: pointer;
            > div {
                margin-top: -2px;
                font-size: 20px;
                font-weight: bold;
            }
        }
    }

    .actionButtons {
        display: flex;
        gap: 20px;
        margin-top: 30px;

        .cancelButton {
            width: 120px;
        }
    }
}
