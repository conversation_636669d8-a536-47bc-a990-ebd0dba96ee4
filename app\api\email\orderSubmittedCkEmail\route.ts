import { NextRequest, NextResponse } from 'next/server';
import { OrderSubmittedCkEmailModel } from '@/templates/email/OrderSubmittedCk';
import { EmailBusiness } from '../business';
 
export async function POST(request: NextRequest) {

    try {
        const emailData:OrderSubmittedCkEmailModel = await request.json();
        await EmailBusiness.SendOrderSubmittedCkEmail(emailData);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}