import 'react-toastify/dist/ReactToastify.css';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'react-toastify';
import { isEmail, isMobilePhone } from 'validator';


// ----------- //
// - General - //
// ----------- //

export class JC_Utils {

    // Check if URL matches current URL
    static isOnPage(checkUrl?:string) {
        let currentUrl = window.location.href.substring(process.env.apiBaseUrl?.length ?? 0, window.location.href.length);
        return currentUrl == checkUrl || currentUrl == `/${checkUrl}`;
    }

    // Check if user is on a mobile device
    static isOnMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // Stringify object then parse it so setState call force trigger rerender
    static parseStringify(theObject:any) {
        return JSON.parse(JSON.stringify(theObject));
    }

    // Random GUID
    static generateGuid() {
        return uuidv4();
    }

    // Empty GUID (all zeros)
    static emptyGuid() {
        return "********-0000-0000-0000-************";
    }

    // Check if 2 arrays are equals (does not account for order)
    static arraysEqual(array1:any[], array2:any[]) {
        return array1.length == array2.length && array1.every(x1 => array2.find(x2 => JSON.stringify(x1) == JSON.stringify(x2)));
    }

    // Check if 2 arrays of guid's are equals (does not account for order)
    static guidArraysEqual(array1:string[], array2:string[]) {
        return array1.length == array2.length && array1.every(x1 => array2.find(x2 => JSON.stringify(x1.toLowerCase()) == JSON.stringify(x2.toLowerCase())));
    }

    // Check if string is in a list of strings, ignoring casing
    static stringInListOfStrings(theString:string, theList:string[]) {
        return theList.map(s => s.toLowerCase()).includes(theString.toLowerCase());
    }

    // Convert a list of arrays into a single array
    static flattenArrays(arrays:any[][]) {
        return arrays.flat();
    }

    // Check if string not null and not empty
    static stringNullOrEmpty(inString?:string|null) {
        return inString == undefined || inString == null || inString.trim().length == 0;
    }

    // Round to 2dp and cut off 0's
    static roundAndCutZeroes(num:number, dp:number) {
        if (num == null || num == 0) {
            return 0;
        }
        const newNum = parseFloat(num?.toFixed(dp));
        return Math.round(newNum * 100) / 100;
    }

    // See if search string split by words matches other string
    static searchMatches(searchString:string, checkString:string) {
        let searchWords:string[] = searchString?.toLowerCase().trim().split(' ');
        return searchWords.every(word => checkString.toLowerCase().indexOf(word) > -1);
    }

    // Toast
    static showToastError(text:string) {
        toast.error(text, { position: this.isOnMobile() ? "top-center" : "bottom-right" });
    }
    static showToastWarning(text:string) {
        toast.warning(text, { position: this.isOnMobile() ? "top-center" : "bottom-right" });
    }
    static showToastSuccess(text:string) {
        toast.success(text, { position: this.isOnMobile() ? "top-center" : "bottom-right" });
    }

    // Sleep
    static async sleep(seconds:number) {
        return new Promise(r => setTimeout(r, seconds*1000));
    }

    // Hardcoded list of sound files in public/sounds directory
    private static soundFiles: string[] = [
        "Ahawhaw, My God.wav",
        "Ahhh1.wav",
        "Ahhh2.wav",
        "Ahhh3.wav",
        "Aww, He's Too Busy Lookign At The Dick Head In Front.wav",
        "Aww, You Got All Black Bitches. How I Am I Suppose To Win.wav",
        "Awww, Fuck.wav",
        "Ay, Yeah, Pass It To Him.wav",
        "Ayyaaahh0.wav",
        "Ayyaaahh1.wav",
        "Beautiful...Ahhh.wav",
        "Can't Even Get Past Your Stupid Defence.wav",
        "Eheeeeeh.wav",
        "Ffff, They're Too Black And Fast.wav",
        "For Fuck's Sake.wav",
        "Fuck Off, Chris. Just Fuck Off.wav",
        "Fuck Off, Chris.wav",
        "Fuck's Sake0.wav",
        "Fuck's Sake1.wav",
        "Fuck0.wav",
        "Fuck1.wav",
        "Fuckin faggot.wav",
        "Hahaha.wav",
        "Heeee.wav",
        "I can't do this shit! Ahh!.wav",
        "I Can't Do This.wav",
        "I can't even fucking beat a fucking retard.wav",
        "I Can't Handle This, Chris.wav",
        "I Cant' Get Passed You.wav",
        "I hate this game, it's so shit.wav",
        "I'm losing to a fucking retard.wav",
        "I'm Playing Like Absolute Shit.wav",
        "It gives me shit.wav",
        "mmmm, mmmm, MMM MMM.wav",
        "Moan0.wav",
        "Moan1.wav",
        "Nooo, Nooo.wav",
        "Nooo0.wav",
        "Nooo1.wav",
        "Oh My God.wav",
        "Ohhh now I gotta oooo ahhhh.wav",
        "Ohhhh Nooooo.wav",
        "Ohhhh.wav",
        "Saaay eehh.wav",
        "See How Slow He Was Moving.wav",
        "That Is Crap.wav",
        "This Is Absolute Horse Shit.wav",
        "This is fucked.wav",
        "This Is Fucking Crap.wav",
        "Wawaweewa.wav",
        "What Am I Supposed To Do.wav",
        "Woo.wav",
        "Yeah, Fucking Fantastic.wav",
        "Yeah, Why Dont' You Pass It There. That's A Good Idea.wav"
    ];

    // Play a random sound from the public/sounds directory
    static playRandomSound() {
        try {
            if (this.soundFiles.length === 0) {
                console.error("No sound files found");
                return;
            }

            // Select a random sound file
            const randomIndex = Math.floor(Math.random() * this.soundFiles.length);
            const soundFile = this.soundFiles[randomIndex];

            // Create and play the audio
            const audio = new Audio(`/sounds/${soundFile}`);
            audio.play();
        } catch (error) {
            console.error("Error playing sound:", error);
        }
    }

    // Eg. "productVariation" -> "Product Variations"
    static routeNameToDescription(routeName:string) {
        if (routeName.indexOf('/') >= 0) {
            return routeName;
        } else {
            const result = routeName.replace(/([A-Z])/g, ' $1');
            return result.charAt(0).toUpperCase() + result.slice(1) + 's';
        }
    }

    static getNumOrdinal(num:number) {
        if (num.toString().split('.')[0].slice(-2)[0] == '1') {
            return "th";
        }
        switch (num % 10) {
            case 1:  return "st";
            case 2:  return "nd";
            case 3:  return "rd";
            default: return "th";
        }
    }

    /**
     * Ensures all items in a list have unique SortOrder values
     * If an item doesn't have a SortOrder, it will be assigned one
     * Ensures there are no gaps larger than 1 between consecutive sort orders
     * @param items List of items to organize
     * @returns The same list with updated SortOrder values
     */
    static organiseSortOrders<T extends { SortOrder?: number }>(items: T[]): T[] {
        if (!items || items.length === 0) {
            return items;
        }

        // First, assign a default SortOrder to any items that don't have one
        let maxSortOrder = 0;
        for (const item of items) {
            if (item.SortOrder !== undefined && item.SortOrder > maxSortOrder) {
                maxSortOrder = item.SortOrder;
            }
        }

        // Assign SortOrder to items that don't have one
        for (const item of items) {
            if (item.SortOrder === undefined) {
                maxSortOrder += 10; // Temporarily use larger increments
                item.SortOrder = maxSortOrder;
            }
        }

        // Check for duplicates and resolve them
        const usedSortOrders = new Map<number, boolean>();
        for (const item of items) {
            if (usedSortOrders.has(item.SortOrder!)) {
                // Found a duplicate, assign a new value
                maxSortOrder += 10;
                item.SortOrder = maxSortOrder;
            }
            usedSortOrders.set(item.SortOrder!, true);
        }

        // Sort the items by their current SortOrder
        const sortedItems = [...items].sort((a, b) =>
            (a.SortOrder || 0) - (b.SortOrder || 0)
        );

        // Reassign SortOrder values with increments of 1
        // If we want to preserve the minimum value, we can start from the minimum existing value
        let minSortOrder = sortedItems[0].SortOrder || 0;

        // Reassign all SortOrder values with increments of exactly 1
        for (let i = 0; i < sortedItems.length; i++) {
            sortedItems[i].SortOrder = minSortOrder + i;
        }

        return items;
    }

}


// --------- //
// - DATES - //
// --------- //

export class JC_Utils_Dates {

    // Get number of minutes between 2 dates
    static minutesBetweenDates(inDate1:Date, inDate2:Date) {
        let date1 = new Date(inDate1);
        let date2 = new Date(inDate2);
        let msBetween = Math.abs(date1.getTime() - date2.getTime());
        const minutesBetween = msBetween / (60 * 1000);
        return minutesBetween;
    }

    // Get formatted date string
    static formattedDateString(inDate:Date) {
        let theDate = new Date(inDate);
        let dateNum = theDate.getDate();
        let ordinal = JC_Utils.getNumOrdinal(dateNum);
        let monthLong = theDate.toLocaleString('default', { month: 'long' });
        let year = theDate.getFullYear();
        return `${dateNum}${ordinal} ${monthLong} ${year}`;
    }

    // Format Date object for Postgres timestamp
    static formatDateForPostgres(inDate:Date) {
        const year    = inDate.getFullYear();
        const month   = (inDate.getMonth() + 1).toString().padStart(2, '0');
        const day     = inDate.getDate().toString().padStart(2, '0');
        const hours   = inDate.getHours().toString().padStart(2, '0');
        const minutes = inDate.getMinutes().toString().padStart(2, '0');
        const seconds = inDate.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

}


// -------------- //
// - VALIDATION - //
// -------------- //

export class JC_Utils_Validation {

    static validEmail(inEmail:string) {
        return isEmail(inEmail);
    }

    static validPhone(inPhone:string) {
        return isMobilePhone(inPhone);
    }

    static validPassword(inPassword:string) {
        return inPassword.length >= 6                                 // At least 6 characters
        && /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(inPassword) // At least 1 symbol
        && /[0-9]+/.test(inPassword);                                 // At least one number
    }

    static validPasswordLength(inPassword:string) {
        return inPassword.length >= 6; // At least 6 characters
    }

    static validPasswordSymbol(inPassword:string) {
        return /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(inPassword); // At least 1 symbol
    }

    static validPasswordNumber(inPassword:string) {
        return /[0-9]+/.test(inPassword); // At least one number
    }

}


// ------- //
// - CSS - //
// ------- //

export class JC_Utils_CSS {

    static forceHideHeaderFooter(styles:any) {
        document.getElementById("JC_header")?.classList.add(styles.forceHidden);
        document.getElementById("JC_header_dietary")?.classList.add(styles.forceHidden);
        document.getElementById("JC_footer")?.classList.add(styles.forceHidden);
    }

    static forceWhiteBackground(styles:any) {
        document.getElementById("rootMainContainer")?.classList.add(styles.forceWhiteBackground);
    }

    static forceRootOverflowYHidden(styles:any) {
        document.getElementById("rootMainContainer")?.classList.add(styles.forceOverflowYHidden);
    }

}


// ------- //
// - CK - //
// ------- //

export class JC_Utils_CK {

    static allBarcodes = [
        "0754523545004",
        "0754523545011",
        "0754523545028",
        "0754523545035",
        "0754523545042",
        "0754523545059",
        "0754523545066",
        "0754523545073",
        "0754523545080",
        "0754523545097",
        "0754523545103",
        "0754523545110",
        "0754523545127",
        "0754523545134",
        "0754523545141",
        "0754523545158",
        "0754523545165",
        "0754523545172",
        "0754523545189",
        "0754523545196",
        "0754523545202",
        "0754523545219",
        "0754523545226",
        "0754523545233",
        "0754523545240",
        "0754523545257",
        "0754523545264",
        "0754523545271",
        "0754523545288",
        "0754523545295",
        "0754523545301",
        "0754523545318",
        "0754523545325",
        "0754523545332",
        "0754523545349",
        "0754523545356",
        "0754523545363",
        "0754523545370",
        "0754523545387",
        "0754523545394",
        "0754523545400",
        "0754523545417",
        "0754523545424",
        "0754523545431",
        "0754523545448",
        "0754523545455",
        "0754523545462",
        "0754523545479",
        "0754523545486",
        "0754523545493",
        "0754523545509",
        "0754523545516",
        "0754523545523",
        "0754523545530",
        "0754523545547",
        "0754523545554",
        "0754523545561",
        "0754523545578",
        "0754523545585",
        "0754523545592",
        "0754523545608",
        "0754523545615",
        "0754523545622",
        "0754523545639",
        "0754523545646",
        "0754523545653",
        "0754523545660",
        "0754523545677",
        "0754523545684",
        "0754523545691",
        "0754523545707",
        "0754523545714",
        "0754523545721",
        "0754523545738",
        "0754523545745",
        "0754523545752",
        "0754523545769",
        "0754523545776",
        "0754523545783",
        "0754523545790",
        "0754523545806",
        "0754523545813",
        "0754523545820",
        "0754523545837",
        "0754523545844",
        "0754523545851",
        "0754523545868",
        "0754523545875",
        "0754523545882",
        "0754523545899",
        "0754523545905",
        "0754523545912",
        "0754523545929",
        "0754523545936",
        "0754523545943",
        "0754523545950",
        "0754523545967",
        "0754523545974",
        "0754523545981",
        "0754523545998",
        "0754523546001",
        "0754523546018",
        "0754523546025",
        "0754523546032",
        "0754523546049",
        "0754523546056",
        "0754523546063",
        "0754523546070",
        "0754523546087",
        "0754523546094",
        "0754523546100",
        "0754523546117",
        "0754523546124",
        "0754523546131",
        "0754523546148",
        "0754523546155",
        "0754523546162",
        "0754523546179",
        "0754523546186",
        "0754523546193",
        "0754523546209",
        "0754523546216",
        "0754523546223",
        "0754523546230",
        "0754523546247",
        "0754523546254",
        "0754523546261",
        "0754523546278",
        "0754523546285",
        "0754523546292",
        "0754523546308",
        "0754523546315",
        "0754523546322",
        "0754523546339",
        "0754523546346",
        "0754523546353",
        "0754523546360",
        "0754523546377",
        "0754523546384",
        "0754523546391",
        "0754523546407",
        "0754523546414",
        "0754523546421",
        "0754523546438",
        "0754523546445",
        "0754523546452",
        "0754523546469",
        "0754523546476",
        "0754523546483",
        "0754523546490",
        "0754523546506",
        "0754523546513",
        "0754523546520",
        "0754523546537",
        "0754523546544",
        "0754523546551",
        "0754523546568",
        "0754523546575",
        "0754523546582",
        "0754523546599",
        "0754523546605",
        "0754523546612",
        "0754523546629",
        "0754523546636",
        "0754523546643",
        "0754523546667",
        "0754523546674",
        "0754523546681",
        "0754523546698",
        "0754523546704",
        "0754523546711",
        "0754523546728",
        "0754523546735",
        "0754523546742",
        "0754523546759",
        "0754523546766",
        "0754523546773",
        "0754523546780",
        "0754523546797",
        "0754523546803",
        "0754523546810",
        "0754523546827",
        "0754523546834",
        "0754523546841",
        "0754523546858",
        "0754523546865",
        "0754523546872",
        "0754523546889",
        "0754523546896",
        "0754523546902",
        "0754523546919",
        "0754523546926",
        "0754523546933",
        "0754523546940",
        "0754523546957",
        "0754523546964",
        "0754523546971",
        "0754523546988",
        "0754523546995"
    ];

}