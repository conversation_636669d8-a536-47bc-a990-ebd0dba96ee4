
# Parts

### Repo
* [GitHub](https://github.com/MrJames12345/CasellaKitchen)

### Front-End
* Design
    * [Figma](https://www.figma.com/file/r4scD2pGR2woyjDDluuj1U/Casella-Kitchen?type=design&node-id=0-1&mode=design&t=s92AyFrnZ60Bz2LT-0)
* Framework
    * NextJS Typescript
* Styling
    * scss

### Back-End
* NextJS Typescript Routes

### Authentication
* NextAuth

### Payments
* [Stripe](https://dashboard.stripe.com/dashboard)


### Database
* [Vercel Storage Postgres Database](https://vercel.com/mrjames12345s-projects/casella-kitchen-test/stores/postgres/store_dvW76hVjlCXGPv4z/data)

### File Storage
* [Vercel Storage Blob Store](https://vercel.com/mrjames12345s-projects/casella-kitchen-test/stores/blob/store_AnRA1v2hryhEKNXu/browser)

### Hosting + Deployments
* Vercel

### Domain
* [casellakitchen.com.au](https://casellakitchen.com.au)

<br/>
<br/>

# Orders

### Minimum Spend
* $50 minimum spend

### Delivery
* $15 delivery fee (value saved in [GlobalSettings])
* Free if over $150 (value saved in [GlobalSettings])

### Discounts
* User has 10% discount if [DiscountEnabled] = 1 (value saved in [GlobalSettings])