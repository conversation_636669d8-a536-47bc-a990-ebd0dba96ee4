SELECT "variation"."Id"
      ,"variation"."ProductId"
      ,"product"."Name" "__Product"
      ,"variation"."VariationCategoryCode"
      ,"category"."Name" "__Category"
      ,"variation"."DependentOnProductVariationId"
      ,"dependentOnVariation"."Name" "__DependentOnVariation"
      ,"variation"."Name"
      -- ,"variation"."AddedPrice"
      -- ,"variation"."ServingSizeGrams"
      -- ,"variation"."ServingsPerPack"
      -- ,"variation"."WeightChange"
      -- ,"variation"."ImageFileName"
      -- ,"variation"."LabelSize"
      -- ,"variation"."PackWeightText"
      -- ,"variation"."FrontLabelNameOverride"
    --   ,"variation"."FrontLabelNameXOffset"
      -- ,"variation"."BackLabelNameOverride"
      -- ,"variation"."IngredientsFontSize"
      -- ,"variation"."InstructionsText"
      -- ,"variation"."InstructionsFontSize"
      -- ,"variation"."NoteText"
      -- ,"variation"."NoteFontSize"
      -- ,"variation"."AllergensText"
      -- ,"variation"."FreeFromText"
      -- ,"variation"."BiodegOverrideText"
      -- ,"variation"."StoreFrozenOverride"
    --   ,"variation"."IconName"
      -- ,"variation"."IconX"
      -- ,"variation"."IconY"
      -- ,"variation"."IconSize"
      ,"variation"."SortOrder"
      ,"variation"."EnabledOnWebsite"
    --   ,"variation"."NutValsEnabled"
      ,"variation"."CreatedAt"
      ,"variation"."ModifiedAt"
      ,"variation"."Deleted"
FROM public."ProductVariation" "variation"
INNER JOIN public."Product" "product" ON "variation"."ProductId" = "product"."Id"
INNER JOIN public."VariationCategory" "category" ON "variation"."VariationCategoryCode" = "category"."Code"
LEFT JOIN public."ProductVariation" "dependentOnVariation" ON "variation"."DependentOnProductVariationId" = "dependentOnVariation"."Id"
WHERE 1=1
      AND "variation"."Deleted" = 'False'
      -- AND "group"."IsDemo" = 'True'
      -- AND "variation"."Id" = 'A4A37358-15D3-440A-8A68-30C3ADB92C25'
      -- AND "variation"."ProductId" = 'abba8870-3a1d-45db-b90f-7f870a526cc5' -- Crumpets
      AND "variation"."ProductId" = '1e559c96-1603-4b55-aa43-d043e9a8fb35' -- Special Occasion Crumpets
      -- AND "variation"."ProductId" = '1e5535f2-5d9a-4c87-8770-c1e9f6256af9'
ORDER BY "product"."SortOrder"
        ,"category"."Name"
        ,"variation"."SortOrder";
-- ORDER BY "variation"."CreatedAt" DESC;


-- Icon
-- UPDATE public."ProductVariation"
-- SET "IconName"    = 'MothersDay2'
-- WHERE "Id" = 'abbc8a3c-b1a2-4dc2-93a8-4327d94945d5';
-- UPDATE public."ProductVariation"
-- SET "IconName"    = 'MothersDay2'
-- WHERE "Id" = '41d7d1cb-6916-42d9-b751-cfaddb027ce0';

-- UPDATE public."ProductVariation"
-- SET "Name"    = 'Caramel Kiss Filled'
-- WHERE "Id" = 'abbc8a3c-b1a2-4dc2-93a8-4327d94945d5';
-- UPDATE public."ProductVariation"
-- SET "Name"    = 'Hazelnut Kiss Filled'
-- WHERE "Id" = '41d7d1cb-6916-42d9-b751-cfaddb027ce0';


-- Delete
-- UPDATE public."ProductVariation"
-- SET "Deleted"    = 'True',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = '92ce3f8f-12a6-4cb4-9458-f446d18893ef'


-- Un-delete
-- UPDATE public."ProductVariation"
-- SET "Deleted"    = 'False',
--     "ModifiedAt" = NOW()
-- WHERE "Id" = 'b7aaa8e1-0c4c-4ec7-a278-cadb2d3cec68'


-- SortOrder
-- UPDATE public."ProductVariation"
-- SET "SortOrder" = '5'
-- WHERE "Id" = '3e2e8944-d20b-499a-9b5c-eeffc6756e5a'