import { sql } from "@vercel/postgres";
import { IngredientModel } from "@/app/models/Ingredient";

export class IngredientBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll(isDemoMode:boolean = false) {
        return (await sql<IngredientModel>`
            SELECT "Id",
                   "Name",
                   "Kilojoules",
                   "Protein",
                   "FatTotal",
                   "FatSaturated",
                   "Carbohydrate",
                   "Sugars",
                   "Fiber",
                   ("Carbohydrate" - "Fiber") "_NetCarbs",
                   "Sodium",
                   "CostPer100g",
                   "PercAus",
                   "LabelDescription",
                   "IsAllergen",
                   "IsOrganic",
                   "IsNotVegan",
                   "HideOnLabel",
                   "IsDemo",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."Ingredient"
            WHERE "Deleted" = 'False'
              AND "IsDemo" = ${isDemoMode}
        `).rows;
    }

    static async RecordExists(id: string) {
        const result = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."Ingredient"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `;
        return result.rows[0]["Count"] > 0;
    }


    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newIngredient:IngredientModel) {
        await sql`
            INSERT INTO public."Ingredient"
            (
                "Id",
                "Name",
                "Kilojoules",
                "Protein",
                "FatTotal",
                "FatSaturated",
                "Carbohydrate",
                "Sugars",
                "Fiber",
                "Sodium",
                "CostPer100g",
                "PercAus",
                "LabelDescription",
                "IsAllergen",
                "IsOrganic",
                "IsNotVegan",
                "HideOnLabel",
                "IsDemo",
                "CreatedAt"
            )
            VALUES
            (
                ${newIngredient.Id},
                ${newIngredient.Name},
                ${newIngredient.Kilojoules},
                ${newIngredient.Protein},
                ${newIngredient.FatTotal},
                ${newIngredient.FatSaturated},
                ${newIngredient.Carbohydrate},
                ${newIngredient.Sugars},
                ${newIngredient.Fiber},
                ${newIngredient.Sodium},
                ${newIngredient.CostPer100g},
                ${newIngredient.PercAus},
                ${newIngredient.LabelDescription},
                ${newIngredient.IsAllergen},
                ${newIngredient.IsOrganic},
                ${newIngredient.IsNotVegan},
                ${newIngredient.HideOnLabel},
                ${newIngredient.IsDemo || false},
                ${new Date().toUTCString()}
            )
        `
    }


    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(ingredient: IngredientModel) {
        // Check if the ingredient exists
        const exists = await this.RecordExists(ingredient.Id);

        // If the ingredient doesn't exist, create it instead
        if (!exists) {
            await this.Create(ingredient);
            return;
        }

        // Update the existing ingredient
        await sql`
            UPDATE public."Ingredient"
            SET "Name"             = ${ingredient.Name},
                "Kilojoules"       = ${ingredient.Kilojoules},
                "Protein"          = ${ingredient.Protein},
                "FatTotal"         = ${ingredient.FatTotal},
                "FatSaturated"     = ${ingredient.FatSaturated},
                "Carbohydrate"     = ${ingredient.Carbohydrate},
                "Sugars"           = ${ingredient.Sugars},
                "Fiber"            = ${ingredient.Fiber},
                "Sodium"           = ${ingredient.Sodium},
                "CostPer100g"      = ${ingredient.CostPer100g},
                "PercAus"          = ${ingredient.PercAus},
                "LabelDescription" = ${ingredient.LabelDescription},
                "IsAllergen"       = ${ingredient.IsAllergen},
                "IsOrganic"        = ${ingredient.IsOrganic},
                "IsNotVegan"       = ${ingredient.IsNotVegan},
                "HideOnLabel"      = ${ingredient.HideOnLabel},
                "IsDemo"           = ${ingredient.IsDemo ?? false},
                "ModifiedAt"       = ${new Date().toUTCString()},
                "Deleted"          = ${ingredient.Deleted ?? false}
            WHERE "Id" = ${ingredient.Id}
        `;
    }

    static async UpdateList(ingredients:IngredientModel[]) {
        for (const ingredient of ingredients) {
            await this.Update(ingredient);
        }
    }


    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id:string) {
        await sql`
            UPDATE public."Ingredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

}