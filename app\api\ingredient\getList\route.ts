import { NextRequest, NextResponse } from 'next/server';
import { unstable_noStore } from 'next/cache';
import { IngredientBusiness } from '../business';

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        const result = await IngredientBusiness.GetAll();
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
