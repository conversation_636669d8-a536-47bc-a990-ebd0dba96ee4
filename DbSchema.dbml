
// - User - //

Table User {
  Id                      UUID         [not null, PK]
  FirstName               varchar(100) [not null]
  LastName                varchar(100) [    null]
  Email                   varchar(100) [not null]
  PasswordHash            varchar      [not null]
  LoginFailedAttempts     int          [not null, default: 0]
  LoginLockoutDate        timestamp    [    null]
  ChangePasswordToken     varchar(200) [    null]
  ChangePasswordTokenDate timestamp    [    null]
  Phone                   varchar(20)  [    null]
  IsAdmin                 boolean      [not null, default: FALSE, note: 'An admin user can make a user admin through Users list page.']
  IsWholesale             boolean      [not null, default: FALSE, note: 'User has different prices if they are a wholesaler. An admin user can make a user wholesale through the Users list page.']
  CompanyName             varchar(200) [    null,                 note: 'Required field for wholesale users.']
  IsEmailSubscribed       boolean      [not null, default: TRUE]
  IsDiscountUser          boolean      [not null, default: FALSE, note: 'Discount amount set in [GlobalSettings].']
  StripeCustomerId        varchar(100) [    null,                 note: 'Set when user makes payment for first time since their Stripe customer account is created.']
  IsVerified              boolean      [not null, default: FALSE]
  VerificationToken       varchar(200) [    null]
  CreatedAt               timestamp    [not null, default: 'now()']
  ModifiedAt              timestamp    [    null]
  Deleted                 boolean      [not null, default: FALSE]
}

Table Address {
  Id         UUID         [not null, PK]
  UserId     UUID         [not null, ref: > User.Id]
  Line1      varchar(200) [not null]
  Line2      varchar(200) [    null]
  City       varchar(50)  [not null]
  Postcode   varchar(16)  [not null]
  Country    varchar(100) [not null, default: 'Australia']
  CreatedAt  timestamp    [not null, default: 'now()']
  ModifiedAt timestamp    [    null]
  Deleted    boolean      [not null, default: FALSE]
}

Table UserPersistedData {
    Id     UUID         [not null, PK]
    UserId UUID         [not null, ref: > User.Id]
    Code   varchar(50)  [not null, PK]
    Value  varchar(200) [not null]
}


// - Stockist - //

Table Stockist {
    Id            UUID         [not null, PK]
    Name          varchar(200) [not null]
    AddressString varchar(300) [not null]
    AddressLat    decimal      [not null]
    AddressLong   decimal      [not null]
    Phone         varchar(20)  [not null]
    LogoImageName varchar(30)  [not null]
    SiteUrl       varchar      [not null]
    MapsUrl       varchar      [not null]
    IsFullRange   boolean      [not null, default: TRUE]
    SortOrder     int          [not null, default: 999]
    Deleted       boolean      [not null, default: FALSE]
}


// - Contact - //

Table Contact {
  Id            UUID         [not null, PK]
  UserId        UUID         [    null, ref: > User.Id, note: 'Nullable since user might not be logged in so just provide name.']
  Name          varchar(200) [not null, note: 'If UserId not null then this will be the User record\'s FirstName+LastName, otherwise is whatever user inputs on "Contact" form.']
  Email         varchar(100) [not null]
  Phone         varchar(20)  [    null]
  Message       varchar      [not null]
  CreatedAt     timestamp    [not null, default: 'now()']
}

Table Product {
  Id                           UUID         [not null, PK]
  Name                         varchar(100) [not null]
  Description                  varchar(200) [    null]
  UnitName                     varchar(100) [not null, note: 'For product page, eg. "$12 / pack", "$12 / jar"']
  Price                        decimal      [not null]
  WholesalePrice               decimal      [    null, note: 'Not sure if will have fixed price or percentage of retail.']
  VariationAddedPricesJson     varchar      [    null, note: 'JSON for logic so when particular option has been selected in particular category, override AddedPrice on other particular variant option.']
  ImageFileName                varchar(100) [not null]
  EnabledOnWebsite             boolean      [not null, default: FALSE]
  SortOrder                    int          [not null, default: 999]
  CreatedAt                    timestamp    [not null, default: 'now()']
  ModifiedAt                   timestamp    [    null]
  Deleted                      boolean      [not null, default: FALSE]
}


// - Variation - //

Table VariationCategory {
  Code                    varchar(50)  [not null, PK]
  Name                    varchar(100) [not null]
  HasLabelSizeSettings    boolean      [not null, default: FALSE]
  HasIngredientsSettings  boolean      [not null, default: FALSE]
  HasServingsSettings     boolean      [not null, default: FALSE]
  HasNameOverrideSettings boolean      [not null, default: FALSE]
  HasLabelsSettings       boolean      [not null, default: FALSE]
  SortOrder               int          [not null, default: 999]
  CreatedAt               timestamp    [not null, default: 'now()']
  ModifiedAt              timestamp    [    null]
  Deleted                 boolean      [not null, default: FALSE]
}

Table ProductVariation {
  Id                            UUID         [not null, PK]
  ProductId                     UUID         [not null, ref: > Product.Id]
  VariationCategoryCode         varchar(50)  [not null, ref: > VariationCategory.Code]
  DependentOnProductVariationId UUID         [    null, ref: > ProductVariation.Id, note: 'If this has value, will only show if the dependent varation is selected in other dropdown, otherwise always show']
  Name                          varchar(100) [not null]
  AddedPrice                    decimal      [    null, note: 'Price added to product\'s base price.']
  ImageFileName                 varchar(100) [    null]
  EnabledOnWebsite              boolean      [not null, default: FALSE]
  SortOrder                     int          [not null, default: 999]
  CreatedAt                     timestamp    [not null, default: 'now()']
  ModifiedAt                    timestamp    [    null]
  Deleted                       boolean      [not null, default: FALSE]
}

Table ProductVariationsData {
    Id                  UUID         [not null, PK]
    ProductId           UUID         [not null, ref: > Product.Id]
    ProductVariationIds varchar[]    [    null, note: 'Combination of ProductVariation\'s that gives this BarcodeNumber. When NULL, this means the data is related just to a Product.']
    Code                varchar(100) [not null, note: 'Product Code used for record keeping.']
    BarcodeNumber       varchar(30)  [not null]
    CreatedAt           timestamp    [not null, default: 'now()']
    ModifiedAt          timestamp    [    null]
    Deleted             boolean      [not null, default: FALSE]
}


// - Ingredient - //

Table Ingredient {
  Id               UUID         [not null, PK]
  Name             varchar(100) [not null]
  Kilojoules       decimal      [not null]
  Protein          decimal      [not null]
  FatTotal         decimal      [not null]
  FatSaturated     decimal      [not null]
  Carbohydrate     decimal      [not null]
  Sugars           decimal      [not null]
  Fiber            decimal      [not null]
  Sodium           decimal      [not null]
  CostPer100g      decimal      [not null]
  PercAus          decimal      [not null]
  LabelDescription varchar(200) [    null]
  IsAllergen       boolean      [    null, default: FALSE]
  IsOrganic        boolean      [    null, default: FALSE]
  IsNotVegan       boolean      [    null, default: FALSE]
  HideOnLabel      boolean      [    null, default: FALSE]
  CreatedAt        timestamp    [not null, default: 'now()']
  ModifiedAt       timestamp    [    null]
  Deleted          boolean      [not null, default: FALSE]
  IsDemo           boolean      [not null, default: FALSE]
}

Table ItemIngredient {
  Id                 UUID      [not null, PK]
  ProductId          UUID      [    null, ref: > Product.Id]
  ProductVariationId UUID      [    null, ref: > ProductVariation.Id]
  IngredientId       UUID      [not null, ref: > Ingredient.Id]
  AmountGrams        decimal   [not null]
  ShowPercent        boolean   [not null]
  CreatedAt          timestamp [not null, default: 'now()']
  ModifiedAt         timestamp [    null]
  Deleted            boolean   [not null, default: FALSE]
}


// - Settings - //

Table ItemSettings {
    Id                     UUID         [not null, PK]
    ProductId              UUID         [    null, ref: > Product.Id]
    ProductVariationId     UUID         [    null, ref: > ProductVariation.Id]
    ServingSizeGrams       decimal      [    null, note: 'To show on back label.', default: 0]
    ServingsPerPack        int          [    null, note: 'To show on back label.', default: 0]
    WeightChange           decimal      [    null, note: 'Adjusts nut val\'s for this percentage change', default: 1]
    LabelSize              varchar(100) [not null, default: 'Large']
    PackWeightText         varchar(100) [    null]
    FrontLabelNameOverride varchar      [    null]
    FrontLabelNameXOffset  int          [    null]
    BackLabelNameOverride  varchar      [    null]
    IngredientsFontSize    decimal      [    null]
    IngredientsYOffset     int          [    null, default: 0]
    InstructionsText       varchar      [    null]
    InstructionsFontSize   decimal      [    null]
    NoteText               varchar      [    null]
    NoteFontSize           decimal      [    null]
    AllergensText          varchar(100) [    null]
    FreeFromText           varchar(200) [    null]
    BiodegOverrideText     varchar      [    null]
    StoreFrozenOverride    varchar      [    null]
    IconName               varchar(100) [    null]
    IconVisible            boolean      [not null, default: 1]
    IconX                  int          [    null, default: 9]
    IconY                  int          [    null, default: -12]
    IconSize               int          [    null, default: 32]
    NutValsEnabled         boolean      [not null, default: FALSE]
    CreatedAt              timestamp    [not null, default: 'now()']
    ModifiedAt             timestamp    [    null]
    Deleted                boolean      [not null, default: FALSE]
}


// - Review - //

Table UserProductReview {
  Id         UUID      [not null, PK]
  ProductId  UUID      [not null, ref: > Product.Id]
  UserId     UUID      [not null, ref: > User.Id]
  Rating     int       [not null]
  Text       varchar   [    null]
  CreatedAt  timestamp [not null, default: 'now()']
  ModifiedAt timestamp [    null]
  Deleted    boolean   [not null, default: FALSE]
}


// - Dietary Attribute - //

Table DietaryAttribute {
  Code        varchar(50)  [not null, PK]
  Description varchar(200) [not null]
  SortOrder   int          [not null, default: 999]
}

Table ProductDietaryAttribute {
  Id                   UUID        [not null, PK]
  ProductId            UUID        [not null, ref: > Product.Id]
  DietaryAttributeCode varchar(50) [not null, ref: > DietaryAttribute.Code]
}


// - Order - //

Table Order {
  Id                     UUID        [not null, PK, note: 'Order is created when user adds first item to their bag.']
  UserId                 UUID        [    null, ref: > User.Id]
  OrderStatusCode        varchar(50) [ref: > OrderStatus.Code]
  SubmittedDate          timestamp   [    null, note: 'Date that user submitted and paid for order.']
  CompletedDate          timestamp   [    null, note: 'Date that order is ready for pick up or delivery.']
  ValuesAtSubmissionJson varchar     [    null, note: 'Contains details that might change in future but could be important to know the state of at time of submission (prices, name of selected variations etc.)']
  CreatedAt              timestamp   [not null, default: 'now()']
  ModifiedAt             timestamp   [    null]
  Deleted                boolean     [not null, default: FALSE]
}

Table OrderItem {
  Id                  UUID      [not null, PK]
  OrderId             UUID      [not null, ref: > Order.Id]
  ProductId           UUID      [not null, ref: > Product.Id]
  ProductVariationIds varchar[] [not null]
  Quantity            int       [not null, default: 1]
  CreatedAt           timestamp [not null, default: 'now()']
  ModifiedAt          timestamp [    null]
  Deleted             boolean   [not null, default: FALSE]
}

Table OrderStatus {
  Code        varchar(50)  [not null, PK]
  Name        varchar(100) [not null]
  Description varchar(200) [not null]
}


// - Payment - //

Table Payment {
  Id                UUID        [not null, PK]
  OrderId           UUID        [not null, ref: > Order.Id]
  PaymentStatusCode varchar(50) [not null, ref: > PaymentStatus.Code, note: 'Depends on payment service used.']
}

Table PaymentStatus {
  Code        varchar(50)  [not null, PK]
  Name        varchar(100) [not null]
  Description varchar(200) [not null]
}


// - Global Settings - //

Table GlobalSettings {
  Code        varchar(50)  [not null, PK]
  Description varchar(200) [not null]
  Value       varchar(200) [not null]
}