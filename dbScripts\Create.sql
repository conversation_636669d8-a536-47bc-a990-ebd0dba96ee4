
-- Delete All

DROP SCHEMA public CASCADE;
CREATE SCHEMA public;

-- Create All

CREATE TABLE "User" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "FirstName" varchar(100) NOT NULL,
  "LastName" varchar(100),
  "Email" varchar(100) NOT NULL,
  "PasswordHash" varchar NOT NULL,
  "LoginFailedAttempts" int NOT NULL DEFAULT 0,
  "LoginLockoutDate" timestamp,
  "ChangePasswordToken" varchar(200),
  "ChangePasswordTokenDate" timestamp,
  "Phone" varchar(20),
  "IsAdmin" boolean NOT NULL DEFAULT FALSE,
  "IsWholesale" boolean NOT NULL DEFAULT FALSE,
  "CompanyName" varchar(200),
  "IsEmailSubscribed" boolean NOT NULL DEFAULT TRUE,
  "IsDiscountUser" boolean NOT NULL DEFAULT FALSE,
  "StripeCustomerId" varchar(100),
  "IsVerified" boolean NOT NULL DEFAULT FALSE,
  "VerificationToken" varchar(200),
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "Address" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "UserId" UUID NOT NULL,
  "Line1" varchar(200) NOT NULL,
  "Line2" varchar(200),
  "City" varchar(50) NOT NULL,
  "Postcode" varchar(16) NOT NULL,
  "Country" varchar(100) NOT NULL DEFAULT 'Australia',
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "UserPersistedData" (
  "Id" UUID NOT NULL,
  "UserId" UUID NOT NULL,
  "Code" varchar(50) NOT NULL,
  "Value" varchar(200) NOT NULL,
  PRIMARY KEY ("Id", "Code")
);

CREATE TABLE "Stockist" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "Name" varchar(200) NOT NULL,
  "AddressString" varchar(300) NOT NULL,
  "AddressLat" decimal NOT NULL,
  "AddressLong" decimal NOT NULL,
  "Phone" varchar(20) NOT NULL,
  "LogoImageName" varchar(30) NOT NULL,
  "SiteUrl" varchar NOT NULL,
  "MapsUrl" varchar NOT NULL,
  "IsFullRange" boolean NOT NULL DEFAULT TRUE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "Contact" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "UserId" UUID,
  "Name" varchar(200) NOT NULL,
  "Email" varchar(100) NOT NULL,
  "Phone" varchar(20),
  "Message" varchar NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()'
);

CREATE TABLE "Product" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "Name" varchar(100) NOT NULL,
  "Description" varchar(200),
  "UnitName" varchar(100) NOT NULL,
  "Price" decimal NOT NULL,
  "WholesalePrice" decimal,
  "VariationAddedPricesJson" varchar,
  "ImageFileName" varchar(100) NOT NULL,
  "EnabledOnWebsite" boolean NOT NULL DEFAULT FALSE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  "IsDemo" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "VariationCategory" (
  "Code" varchar(50) PRIMARY KEY NOT NULL,
  "Name" varchar(100) NOT NULL,
  "HasLabelSizeSettings" boolean NOT NULL DEFAULT FALSE,
  "HasIngredientsSettings" boolean NOT NULL DEFAULT FALSE,
  "HasServingsSettings" boolean NOT NULL DEFAULT FALSE,
  "HasNameOverrideSettings" boolean NOT NULL DEFAULT FALSE,
  "HasLabelsSettings" boolean NOT NULL DEFAULT FALSE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "ProductVariation" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "ProductId" UUID NOT NULL,
  "VariationCategoryCode" varchar(50) NOT NULL,
  "DependentOnProductVariationId" UUID,
  "Name" varchar(100) NOT NULL,
  "AddedPrice" decimal,
  "ImageFileName" varchar(100),
  "SortOrder" int NOT NULL DEFAULT 999,
  "EnabledOnWebsite" boolean NOT NULL DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "ProductVariationsData" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "ProductId" UUID NOT NULL,
  "ProductVariationIds" varchar[] NOT NULL,
  "Code" varchar(100) NOT NULL,
  "BarcodeNumber" varchar(30) NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "Ingredient" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "Name" varchar(100) NOT NULL,
  "Kilojoules" decimal NOT NULL,
  "Protein" decimal NOT NULL,
  "FatTotal" decimal NOT NULL,
  "FatSaturated" decimal NOT NULL,
  "Carbohydrate" decimal NOT NULL,
  "Sugars" decimal NOT NULL,
  "Fiber" decimal NOT NULL,
  "Sodium" decimal NOT NULL,
  "CostPer100g" decimal NOT NULL,
  "PercAus" decimal NOT NULL,
  "LabelDescription" varchar(200),
  "IsAllergen" boolean DEFAULT FALSE,
  "IsOrganic" boolean DEFAULT FALSE,
  "IsNotVegan" boolean DEFAULT FALSE,
  "HideOnLabel" boolean DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  "IsDemo" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "ItemIngredient" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "ProductId" UUID,
  "ProductVariationId" UUID,
  "IngredientId" UUID NOT NULL,
  "AmountGrams" decimal NOT NULL,
  "ShowPercent" boolean NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "ItemSettings" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "ProductId" UUID NOT NULL,
  "ProductVariationId" UUID,
  "ServingSizeGrams" decimal DEFAULT 0,
  "ServingsPerPack" int DEFAULT 0,
  "WeightChange" decimal DEFAULT 1,
  "LabelSize" varchar(100) NOT NULL DEFAULT 'Large',
  "PackWeightText" varchar(100),
  "FrontLabelNameOverride" varchar,
  "FrontLabelNameXOffset" int,
  "BackLabelNameOverride" varchar,
  "IngredientsFontSize" decimal,
  "IngredientsYOffset" int DEFAULT 0,
  "InstructionsText" varchar,
  "InstructionsFontSize" decimal,
  "NoteText" varchar,
  "NoteFontSize" decimal,
  "AllergensText" varchar(100),
  "FreeFromText" varchar(200),
  "BiodegOverrideText" varchar,
  "StoreFrozenOverride" varchar,
  "IconName" varchar(100),
  "IconX" int DEFAULT 9,
  "IconY" int DEFAULT -12,
  "IconSize" int DEFAULT 32,
  "NutValsEnabled" boolean NOT NULL DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "UserProductReview" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "ProductId" UUID NOT NULL,
  "UserId" UUID NOT NULL,
  "Rating" int NOT NULL,
  "Text" varchar,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "DietaryAttribute" (
  "Code" varchar(50) PRIMARY KEY NOT NULL,
  "Description" varchar(200) NOT NULL,
  "SortOrder" int NOT NULL DEFAULT 999
);

CREATE TABLE "ProductDietaryAttribute" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "ProductId" UUID NOT NULL,
  "DietaryAttributeCode" varchar(50) NOT NULL
);

CREATE TABLE "Order" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "UserId" UUID,
  "OrderStatusCode" varchar(50),
  "SubmittedDate" timestamp,
  "CompletedDate" timestamp,
  "ValuesAtSubmissionJson" varchar,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "OrderItem" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "OrderId" UUID NOT NULL,
  "ProductId" UUID NOT NULL,
  "ProductVariationIds" varchar[] NOT NULL,
  "Quantity" int NOT NULL DEFAULT 1,
  "CreatedAt" timestamp NOT NULL DEFAULT 'now()',
  "ModifiedAt" timestamp,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

CREATE TABLE "OrderStatus" (
  "Code" varchar(50) PRIMARY KEY NOT NULL,
  "Name" varchar(100) NOT NULL,
  "Description" varchar(200) NOT NULL
);

CREATE TABLE "Payment" (
  "Id" UUID PRIMARY KEY NOT NULL,
  "OrderId" UUID NOT NULL,
  "PaymentStatusCode" varchar(50) NOT NULL
);

CREATE TABLE "PaymentStatus" (
  "Code" varchar(50) PRIMARY KEY NOT NULL,
  "Name" varchar(100) NOT NULL,
  "Description" varchar(200) NOT NULL
);

CREATE TABLE "GlobalSettings" (
  "Code" varchar(50) PRIMARY KEY NOT NULL,
  "Description" varchar(200) NOT NULL,
  "Value" varchar(200) NOT NULL
);

-- Comments
COMMENT ON COLUMN "User"."IsAdmin" IS 'An admin user can make a user admin through Users list page.';
COMMENT ON COLUMN "User"."IsWholesale" IS 'User has different prices if they are a wholesaler. An admin user can make a user wholesale through the Users list page.';
COMMENT ON COLUMN "User"."CompanyName" IS 'Required field for wholesale users.';
COMMENT ON COLUMN "User"."IsDiscountUser" IS 'Discount amount set in [GlobalSettings].';
COMMENT ON COLUMN "User"."StripeCustomerId" IS 'Set when user makes payment for first time since their Stripe customer account is created.';
COMMENT ON COLUMN "Contact"."UserId" IS 'Nullable since user might not be logged in so just provide name.';
COMMENT ON COLUMN "Contact"."Name" IS 'If UserId not null then this will be the User record''s FirstName+LastName, otherwise is whatever user inputs on "Contact" form.';
COMMENT ON COLUMN "Product"."UnitName" IS 'For product page, eg. "$12 / pack", "$12 / jar"';
COMMENT ON COLUMN "Product"."WholesalePrice" IS 'Not sure if will have fixed price or percentage of retail.';
COMMENT ON COLUMN "Product"."VariationAddedPricesJson" IS 'JSON for logic so when particular option has been selected in particular category, override AddedPrice on other particular variant option.';
COMMENT ON COLUMN "ProductVariation"."DependentOnProductVariationId" IS 'If this has value, will only show if the dependent varation is selected in other dropdown, otherwise always show';
COMMENT ON COLUMN "ProductVariation"."AddedPrice" IS 'Price added to product''s base price.';
COMMENT ON COLUMN "ProductVariationsData"."ProductVariationIds" IS 'Combination of ProductVariation''s that gives this BarcodeNumber.';
COMMENT ON COLUMN "ProductVariationsData"."Code" IS 'Product Code used for record keeping.';
COMMENT ON COLUMN "ItemSettings"."ServingSizeGrams" IS 'To show on back label.';
COMMENT ON COLUMN "ItemSettings"."ServingsPerPack" IS 'To show on back label.';
COMMENT ON COLUMN "Order"."Id" IS 'Order is created when user adds first item to their bag.';
COMMENT ON COLUMN "Order"."SubmittedDate" IS 'Date that user submitted and paid for order.';
COMMENT ON COLUMN "Order"."CompletedDate" IS 'Date that order is ready for pick up or delivery.';
COMMENT ON COLUMN "Order"."ValuesAtSubmissionJson" IS 'Contains details that might change in future but could be important to know the state of at time of submission (prices, name of selected variations etc.)';
COMMENT ON COLUMN "Payment"."PaymentStatusCode" IS 'Depends on payment service used.';

-- Foreign Keys
ALTER TABLE "Address" ADD FOREIGN KEY ("UserId") REFERENCES "User" ("Id");
ALTER TABLE "UserPersistedData" ADD FOREIGN KEY ("UserId") REFERENCES "User" ("Id");
ALTER TABLE "Contact" ADD FOREIGN KEY ("UserId") REFERENCES "User" ("Id");

ALTER TABLE "ProductVariation" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");
ALTER TABLE "ProductVariation" ADD FOREIGN KEY ("VariationCategoryCode") REFERENCES "VariationCategory" ("Code");
ALTER TABLE "ProductVariation" ADD FOREIGN KEY ("DependentOnProductVariationId") REFERENCES "ProductVariation" ("Id");

ALTER TABLE "ProductVariationsData" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");

ALTER TABLE "ItemIngredient" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");
ALTER TABLE "ItemIngredient" ADD FOREIGN KEY ("ProductVariationId") REFERENCES "ProductVariation" ("Id");
ALTER TABLE "ItemIngredient" ADD FOREIGN KEY ("IngredientId") REFERENCES "Ingredient" ("Id");

ALTER TABLE "ItemSettings" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");
ALTER TABLE "ItemSettings" ADD FOREIGN KEY ("ProductVariationId") REFERENCES "ProductVariation" ("Id");

ALTER TABLE "UserProductReview" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");
ALTER TABLE "UserProductReview" ADD FOREIGN KEY ("UserId") REFERENCES "User" ("Id");

ALTER TABLE "ProductDietaryAttribute" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");
ALTER TABLE "ProductDietaryAttribute" ADD FOREIGN KEY ("DietaryAttributeCode") REFERENCES "DietaryAttribute" ("Code");

ALTER TABLE "Order" ADD FOREIGN KEY ("UserId") REFERENCES "User" ("Id");
ALTER TABLE "Order" ADD FOREIGN KEY ("OrderStatusCode") REFERENCES "OrderStatus" ("Code");

ALTER TABLE "OrderItem" ADD FOREIGN KEY ("OrderId") REFERENCES "Order" ("Id");
ALTER TABLE "OrderItem" ADD FOREIGN KEY ("ProductId") REFERENCES "Product" ("Id");

ALTER TABLE "Payment" ADD FOREIGN KEY ("OrderId") REFERENCES "Order" ("Id");
ALTER TABLE "Payment" ADD FOREIGN KEY ("PaymentStatusCode") REFERENCES "PaymentStatus" ("Code");
