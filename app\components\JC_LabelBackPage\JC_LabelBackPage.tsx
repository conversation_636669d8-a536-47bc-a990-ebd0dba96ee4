"use client"

import React from "react";
import JC_LabelBack from "@/app/components/JC_LabelBack/JC_LabelBack";
import { JC_LabelPageModel } from "@/app/models/ComponentModels/JC_LabelPage";
import { LabelSizeEnum } from "@/app/enums/LabelSize";
import { JC_Utils } from "@/app/Utils";
import styles from "./JC_LabelBackPage.module.scss";

export default function JC_LabelBackPage(_: Readonly<JC_LabelPageModel>) {

    // Determine number of labels based on label size
    const numLabels = _.labelSize === LabelSizeEnum.TwoByFive ? 10 : 8;

    // No auto-print functionality here - moved to Page_PrintBackLabel

    // Get the appropriate class based on label size
    const getLabelSizeClass = () => {
        if (_.labelSize === LabelSizeEnum.Small) return styles.small;
        if (_.labelSize === LabelSizeEnum.TwoByFive) return styles.twoByFive;
        return "";
    };

    return (
        <div
            className={`${styles.mainContainer} ${getLabelSizeClass()} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}
            style={_.paddingStyle}
        >
            {_.product != null && _.varData != null &&
            [...Array(numLabels)].map((num, i) =>
                <JC_LabelBack
                    key={i}
                    product={_.product}
                    varData={_.varData}
                />
            )}
        </div>
    );
}
