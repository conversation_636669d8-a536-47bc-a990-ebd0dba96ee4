"use client"

import <PERSON><PERSON><PERSON>Modal from "@/app/components/JC_Modal/JC_Modal";
import styles from "./NewGlobalSettingModal.module.scss";
import React, { useEffect, useState } from 'react';
import JC_Form from "@/app/components/JC_Form/JC_Form";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";

export default function NewGlobalSettingModal(_: Readonly<{
    isOpen: boolean;
    saveNewSetting: (newSetting: GlobalSettingsModel) => void;
    onCancel: () => void;
}>) {

    // - ----- - //
    // - STATE - //
    // - ----- - //

    const [code, setCode] = useState<string>("");
    const [description, setDescription] = useState<string>("");
    const [value, setValue] = useState<string>("");
    
    useEffect(() => {
        setTimeout(() => (document.getElementById(`new-setting-code`) as HTMLInputElement)?.focus(), 10);
    }, []);

    // - ---- - //
    // - MAIN - //
    // - ---- - //

    return (
        <JC_Modal isOpen={_.isOpen} onCancel={_.onCancel} title="New Global Setting">
            <JC_Form
                columns={2}
                submitButtonText="Add Setting"
                onSubmit={() => _.saveNewSetting(new GlobalSettingsModel({
                    Code: code,
                    Description: description,
                    Value: value
                }))}
                fields={[
                    {
                        inputId: "new-setting-code",
                        type: FieldTypeEnum.Text,
                        label: "Code",
                        value: code,
                        onChange: (newValue: string) => setCode(newValue),
                        validate: () => code.length === 0 ? "You must enter a Code!" : ""
                    },
                    {
                        inputId: "new-setting-description",
                        type: FieldTypeEnum.Text,
                        label: "Description",
                        value: description,
                        onChange: (newValue: string) => setDescription(newValue),
                        validate: () => description.length === 0 ? "You must enter a Description!" : ""
                    },
                    {
                        inputId: "new-setting-value",
                        type: FieldTypeEnum.Text,
                        label: "Value",
                        value: value,
                        onChange: (newValue: string) => setValue(newValue),
                        validate: () => value.length === 0 ? "You must enter a Value!" : ""
                    }
                ]}
            />
        </JC_Modal>
    );
}
