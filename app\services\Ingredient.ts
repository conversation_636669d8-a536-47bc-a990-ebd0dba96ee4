import { IngredientModel } from "../models/Ingredient";

export async function GetIngredients() : Promise<IngredientModel[]> {
    // Check if we're in demo mode
    const isDemoMode = window.location.pathname.includes('/demo/');

    const response = await fetch(`/api/ingredient?isDemoMode=${isDemoMode}`);
    if (!response.ok) { throw new Error("Failed to fetch Ingredients."); }
    let ingredients:IngredientModel[] = (await response.json()).result.sort((a:IngredientModel, b:IngredientModel) => a.Name > b.Name ? 1 : -1);
    ingredients.forEach(i => {
        i.Kilo<PERSON>les   = +i.<PERSON>;
        i.<PERSON>tein      = +i.<PERSON>tein;
        i.FatTotal     = +i.FatTotal;
        i.FatSaturated = +i.FatSaturated;
        i.Carbohydrate = +i.Carbohydrate;
        i.Sugars       = +i.Sugars;
        i.Fiber        = +i.Fiber;
        i._NetCarbs    = +i._NetCarbs;
        i.Sodium       = +i.Sodium;
        i.CostPer100g  = +i.CostPer100g;
        i.PercAus      = +i.PercAus;
    })
    return ingredients;
}