import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _NutritionalValuesModel } from "./_NutritionalValues";

export class IngredientModel extends _Base implements _NutritionalValuesModel {

    static apiRoute:string = "ingredient";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;
    LabelDescription: string;
    IsAllergen: boolean;
    IsOrganic: boolean;
    IsNotVegan: boolean;
    HideOnLabel: boolean;
    IsDemo: boolean;

    // Nutritional Values
    Kilojoules: number;
    Protein: number;
    FatTotal: number;
    FatSaturated: number;
    Carbohydrate: number;
    Sugars: number;
    Fiber: number;
    _NetCarbs: number;
    Sodium: number;
    PercAus: number;
    CostPer100g: number;

    // UI
    UI_EditingName?: boolean;
    UI_ToDelete?: boolean;
    UI_HasChanges?: boolean;


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<IngredientModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Name = "";
        this.LabelDescription = "";
        this.IsAllergen = false;
        this.IsOrganic = false;
        this.IsNotVegan = false;
        this.HideOnLabel = false;
        this.IsDemo = false;
        this.Kilojoules = 0;
        this.Protein = 0;
        this.FatTotal = 0;
        this.FatSaturated = 0;
        this.Carbohydrate = 0;
        this.Sugars = 0;
        this.Fiber = 0;
        this._NetCarbs = 0;
        this.Sodium = 0;
        this.PercAus = 0;
        this.CostPer100g = 0;

        Object.assign(this, init);
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Name}` + !JC_Utils.stringNullOrEmpty(this.LabelDescription) ? ` | (${this.LabelDescription})` : "";
    }
}
