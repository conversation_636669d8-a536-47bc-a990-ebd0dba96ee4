"use client"

import styles from "./page.module.scss";
import { useEffect } from "react";
import JC_AutoSlideshow from "./components/JC_AutoSlideshow/JC_AutoSlideshow";

import { useSession } from "next-auth/react";
import { JC_Utils } from "./Utils";
import { LocalStorageKeyEnum } from "./enums/LocalStorageKey";

export default function Page_Home() {

    const session = useSession();

    // IF just logged in, show "Welcome"
    useEffect(() => {
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome) == "1" && session.data != null) {
            JC_Utils.showToastSuccess(`Welcome ${session.data?.user.FirstName}!`);
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "0");
        }
    }, []);

    return (

        <div className={styles.mainContainer}>

            {/* Important Info */}
            <div className={styles.importantInfoContainer}>

                {/* Background Image */}
                <JC_AutoSlideshow
                    overrideClass={styles.impInfoBackgroundImage}
                    brightness={0.7}
                    imageSrcList={[
                        "/HomePageImpInfo.webp",
                        "/products/groups/Pasta.webp",
                        "/products/groups/Kisses.webp",
                    ]}
                />

                {/* Text */}
                <div className={styles.impInfoText}>
                    WELCOME TO CASELLA KITCHEN <br/>
                    ENJOY A RANGE OF DELICOUS<br/>
                    HOME-MADE HEALTHY FOODS
                </div>

            </div>

        </div>
    );
}