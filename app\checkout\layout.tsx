import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen - Checkout",
    description: "View and confirm your order."
};

export default async function Layout_Checkout(_: Readonly<{

    children: React.ReactNode;

}>) {

    // - AUTH - //

    const session = await auth();
    if (!session) {
        redirect("/");
    }

    // - MAIN - //

    return _.children;
    
}
