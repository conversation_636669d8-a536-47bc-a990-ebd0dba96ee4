import { _Base } from "./_Base";
import { _NutritionalValuesModel } from "./_NutritionalValues";
import { ItemIngredientModel } from "./ItemIngredient";
import { JC_Utils } from "@/app/Utils";

export class ProductVariationsDataModel extends _Base {

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ProductId: string;
    ProductVariationIds: string[] | null;
    Code: string;
    BarcodeNumber: string;

    // UI State
    UI_HasChanges: boolean;

    // Extended
    Ex_NutVals: _NutritionalValuesModel;
    Ex_NutValsEnabled: boolean;


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ProductVariationsDataModel>) {
        super(init);
        this.Id = "";
        this.ProductId = "";
        this.ProductVariationIds = []; // Default to empty array, but can be null
        this.Code = "";
        this.BarcodeNumber = "";
        this.UI_HasChanges = false;
        this.Ex_NutVals = new _NutritionalValuesModel();
        this.Ex_NutValsEnabled = false;
        Object.assign(this, init);
        // Extended
        this.Ex_NutVals = new _NutritionalValuesModel(init?.Ex_NutVals);
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.ProductId} | ${this.ProductVariationIds ? this.ProductVariationIds.join(',') : 'Product-only'}`;
    }


    // - ----- - //
    // - UTILS - //
    // - ----- - //

    // Calc nutritional values
    calcNutVals(inWeightChange:number, inIngredients:ItemIngredientModel[]) {
        if (inIngredients == null || inIngredients.length == 0) {
            this.Ex_NutVals = new _NutritionalValuesModel();
        } else {
            let totalWeight = inIngredients.map(i => i.AmountGrams).reduce((prevResult, currentValue) => prevResult! + currentValue!, 0);
            let nutVals:_NutritionalValuesModel = new _NutritionalValuesModel({
                Kilojoules:   (inIngredients.map(i => (i.Ex_Ingredient!.Kilojoules/100)  * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                Protein:      (inIngredients.map(i => (i.Ex_Ingredient!.Protein/100)     * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                FatTotal:     (inIngredients.map(i => (i.Ex_Ingredient!.FatTotal/100)    * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                FatSaturated: (inIngredients.map(i => (i.Ex_Ingredient!.FatSaturated/100)* i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                Carbohydrate: (inIngredients.map(i => (i.Ex_Ingredient!.Carbohydrate/100)* i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                Sugars:       (inIngredients.map(i => (i.Ex_Ingredient!.Sugars/100)      * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                Fiber:        (inIngredients.map(i => (i.Ex_Ingredient!.Fiber/100)       * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                Sodium:       (inIngredients.map(i => (i.Ex_Ingredient!.Sodium/100)      * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange,
                PercAus:      (inIngredients.map(i => (i.Ex_Ingredient!.PercAus/100)     * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100),
                CostPer100g:  (inIngredients.map(i => (i.Ex_Ingredient!.CostPer100g/100) * i.AmountGrams!).reduce((prevResult, currentValue) => prevResult + currentValue, 0) / totalWeight! * 100) / inWeightChange
            });
            this.Ex_NutVals = nutVals;
        }
    }

    // - -------------- - //
    // - STATIC METHODS - //
    // - -------------- - //

    /**
     * Creates ProductVariationsData records for a new variation
     * Assigns barcodes from the provided list of unused barcodes
     * @param newVariationId The ID of the new variation
     * @param productId The ID of the product
     * @param variationCategoryCode The category code of the new variation
     * @param allVariations All variations for the product
     * @param unusedBarcodes List of unused barcode numbers to assign to new records
     * @returns Array of new ProductVariationsData records with barcodes
     */
    static createForNewVariation(
        newVariationId: string,
        productId: string,
        variationCategoryCode: string,
        allVariations: any[],
        unusedBarcodes: string[]
    ): ProductVariationsDataModel[] {
        // Filter variations by category
        const variationsInOtherCategory = allVariations.filter(v => v.VariationCategoryCode !== variationCategoryCode && !v.Deleted);

        // Create new ProductVariationsData records
        const newRecords: ProductVariationsDataModel[] = [];

        // If there are no variations in another category, create a single record
        if (variationsInOtherCategory.length === 0) {
            const newRecord = new ProductVariationsDataModel({
                Id: JC_Utils.generateGuid(),
                ProductId: productId,
                ProductVariationIds: [newVariationId],
                Code: "TEST_CODE",
                BarcodeNumber: "", // Will be assigned from unused barcodes
                UI_HasChanges: true
            });
            newRecords.push(newRecord);
        }
        // Otherwise, create a record for each combination with variations in other categories
        else {
            variationsInOtherCategory.forEach(prodVar => {
                const newRecord = new ProductVariationsDataModel({
                    Id: JC_Utils.generateGuid(),
                    ProductId: productId,
                    ProductVariationIds: [newVariationId, prodVar.Id],
                    Code: "TEST_CODE",
                    BarcodeNumber: "", // Will be assigned from unused barcodes
                    UI_HasChanges: true
                });
                newRecords.push(newRecord);
            });
        }

        // Assign barcodes to the new records
        if (unusedBarcodes && unusedBarcodes.length >= newRecords.length) {
            for (let i = 0; i < newRecords.length; i++) {
                newRecords[i].BarcodeNumber = unusedBarcodes[i];
            }
        } else {
            console.warn("Not enough unused barcodes available for all new records");
        }

        return newRecords;
    }

    /**
     * Creates a ProductVariationsData record for a product without variations
     * Barcodes will be assigned in the backend
     * @param productId The ID of the product
     * @returns New ProductVariationsData record
     */
    static createForProduct(
        productId: string
    ): ProductVariationsDataModel {
        // Create new ProductVariationsData record
        const newRecord = new ProductVariationsDataModel({
            Id: JC_Utils.generateGuid(),
            ProductId: productId,
            ProductVariationIds: null, // Set to null to indicate this is for a product without variations
            Code: "TEST_CODE",
            BarcodeNumber: "", // Empty string, barcode will be assigned in backend
            UI_HasChanges: true
        });

        return newRecord;
    }
}
