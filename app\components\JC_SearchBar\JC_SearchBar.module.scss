@import '../../global';

$iconWidth: 26px;
$iconInputGap: 14px;
$resultsWidth: 580px;

.mainContainer {
    position: relative;
    width: 260px;
    display: flex;
    flex-direction: column;
    column-gap: 20px;

    .outsideClickDiv {
        @include outsideClickDiv;
    }

    .bar {
        position: relative;
        width: 100%;
    
        input {
            width: 100%;
            height: 44px;
            padding-left: calc($iconInputGap + $iconWidth + $iconInputGap);
            box-sizing: border-box;
            background-color: $offWhite;
            border: none;
            outline: solid $smallBorderWidth $offBlack;
            border-radius: $smallBorderRadius;
            font-size: 18px;

            &::placeholder {
                font-weight: bold;
                color: black
            }
    
            &:hover { 
                outline-color: $secondaryColor;
            }
            &:focus { 
                outline-color: $primaryColor;
                &::placeholder {
                    // font-weight: normal;
                    color: grey
                }
            }
        }
    
        .icon {
            width: $iconWidth;
            height: auto;
            position: absolute;
            top: 50%; transform: translateY(-50%);
            left: calc($iconInputGap + 2px);
        }
    }
    
    .results {
        position: absolute;
        bottom: -28px; transform: translateY(100%);
        width: $resultsWidth;
        height: 400px;
        border-radius: $smallBorderRadius;
        outline: solid $smallBorderWidth $primaryColor;
        background-color: $offWhite;
        z-index: 999;
        overflow: hidden;

        .resultsScrollDiv {
            width: 100%;
            height: 100%;
            overflow-y: auto;
        }

        // Logo (not searching)
        .ckLogo {
            position: absolute;
            top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 200px;
            height: auto;
        }

        // Results Tiles
        .resultTile {
            width: 100%;
            height: max-content;
            padding: 8px 30px 8px 25px;
            box-sizing: border-box;
            display: grid;
            grid-template-columns: max-content $smallImageWidth 3fr 160px;
            column-gap: 30px;
            justify-content: space-between;
            align-items: center;
            align-content: center;
            cursor: pointer;

            .resultPrice {
                flex: 1;
            }
            .resultImage {
                width: 100%;
                height: $smallImageHeight;
                border-radius: $tinyBorderRadius;
                outline: solid $tinyBorderWidth $offBlack;
                object-fit: cover;
            }
            .resultName {
                flex: 3;
                text-align: center;
            }
            .resultGroupButton {
                flex: 1.5;
                height: 32px;
                width: 100%;
                a {
                    width: 100%;
                    height: 100%;
                }
            }

            &:hover {
                background-color: $greyHover;
            }
        }

        // No Results (searched but no results)
        .noResultsMessage {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            row-gap: 60px;

            .charlotteImage {
                width: auto;
                height: 110px;
            }

            .noResultsText {
                font-size: 48px;
                font-weight: bold;
            }
        }
    }

}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        width: $resultsWidth !important;
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        width: 400px !important;
        .bar {
            input {
                height: 36px;
                padding-left: calc($iconInputGap + $iconWidth + 10px);
                font-size: 16px;
            }
            .icon {
                width: 24px;
            }
        }
        .results {
            width: 100%;
            .resultTile {
                padding: 8px 15px 8px 20px;
                grid-template-columns: 70px 3fr 160px;
                column-gap: 20px;
                font-size: $defaultFontSize;
                .resultPrice {
                    display: none;
                }
                .resultImage {
                    height: 52px;
                }
                .resultGroupButton {
                    font-size: 12px;
                }
            }
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        width: 400px !important;
        .bar {
            input {
                height: 40px;
                padding-left: 38px;
                font-size: 13px;
            }
            .icon {
                top: 20px;
                left: 9px;
            }
        }
        .results {
            width: 100%;
            outline-width: $tinyBorderWidth;
            .resultTile {
                padding: 8px 15px 8px 20px;
                grid-template-columns: 70px 3fr 160px;
                column-gap: 20px;
                font-size: $defaultFontSize;
                .resultPrice {
                    display: none;
                }
                .resultImage {
                    height: 52px;
                }
                .resultGroupButton {
                    font-size: 12px;
                }
            }
        }
    }

    .buttonContainer {
        min-width: 95px;
        height: 30px;
        padding: 0 10px;
        font-size: 13px;
        outline-width: $tinyBorderWidth;

        &:hover {
            outline-width: $smallBorderWidth;
        }
        &.buttonSelected {
            outline-width: $smallBorderWidth;
        }
        .buttonIcon {
            width: 17px !important;
        }
    }
}