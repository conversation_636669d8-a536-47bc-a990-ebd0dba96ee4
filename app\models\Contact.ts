import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";

export class ContactModel extends _Base {

    static apiRoute:string = "contact";
    
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId?: string;
    Name: string;
    Email: string;
    Phone?: string;
    Message: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ContactModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.UserId = undefined;
        this.Name = "";
        this.Email = "";
        this.Phone = undefined;
        this.Message = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Name} | ${this.Email} | ${this.Message}`;
    }
}
