import { _Base } from "./_Base";

export class StockistModel {

    static apiRoute:string = "stockist";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;
    AddressString: string;
    AddressLat: number;
    AddressLong: number;
    Phone: string;
    LogoImageName: string;
    SiteUrl: string;
    MapsUrl: string;
    IsFullRange: boolean;
    SortOrder: number;
    Deleted: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<StockistModel>) {
        this.Id = "";
        this.Name = "";
        this.AddressString = "";
        this.AddressLat = 0;
        this.AddressLong = 0;
        this.Phone = "";
        this.LogoImageName = "";
        this.SiteUrl = "";
        this.MapsUrl = "";
        this.IsFullRange = true;
        this.SortOrder = 999;
        this.Deleted = false;
        Object.assign(this, init);
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}
