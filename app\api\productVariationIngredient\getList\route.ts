import { NextRequest, NextResponse } from "next/server";
import { ProductVariationIngredientBusiness } from "../business";

export async function GET(request: NextRequest) {
    try {
        const productVariationId = new URL(request.url).searchParams.get("productVariationId");
        
        if (productVariationId) {
            const items = await ProductVariationIngredientBusiness.GetForProductVariation(productVariationId);
            return NextResponse.json(items);
        } else {
            const items = await ProductVariationIngredientBusiness.GetAll();
            return NextResponse.json(items);
        }
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
