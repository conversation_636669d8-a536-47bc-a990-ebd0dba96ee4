"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Map from "../components/JC_Map/JC_Map";
import { JC_GetList } from "../services/JC_GetList";
import { StockistModel } from "../models/Stockist";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

export default function Page_Stockists() {

    // - STATE - //

    // Products
    const [stockists, setStockists] = useState<StockistModel[]>([]);

    useEffect(() => {
        JC_GetList<StockistModel>(StockistModel.apiRoute, {}, StockistModel, LocalStorageKeyEnum.JC_Stockists).then(listResult => {
            setStockists(listResult);
        });
    }, []);


    // - MAIN - //

    return (
        <div className={styles.mainContainer}>

            {/* Title (small screens) */}
            <JC_Title overrideClass={styles.title} title="Stockists" />

            {/* Logo + Map */}
            <div className={styles.logoMap}>
                {/* Logo */}
                <Image
                    className={styles.logo}
                    src="/logos/Stockists.webp"
                    width={0}
                    height={0}
                    alt="Logo"
                    unoptimized
                />
                {/* Map */}
                <JC_Map
                    containerClassName={styles.map}
                    initialPosition={{ lat:-32.4800983, lng: 115.8135559 }}
                    initialZoom={8}
                    positions={stockists.map(s => ({
                        name: s.Name,
                        lat: s.AddressLat,
                        lng: s.AddressLong,
                        mapsUrl: s.MapsUrl
                    }))}
                    // showRefresh
                />
            </div>

            {/* Full Range Stockists */}
            {stockists.filter(s => s.IsFullRange).length > 0 && (
                <>
                    <JC_Title overrideClass={styles.groupTitle} title="Full Range" />
                    <div className={styles.listContainer}>
                        {stockists.filter(s => s.IsFullRange).map(s =>
                            <Link key={s.Id} className={styles.item} href={s.SiteUrl} target="_blank">

                                {/* Logo */}
                                <Image
                                    className={styles.itemLogo}
                                    src={`/stockistLogos/${s.LogoImageName}.webp`}
                                    width={600}
                                    height={600}
                                    alt={`${s.Name} Logo`}
                                />

                                {/* Name */}
                                <div className={styles.itemName}>{s.Name}</div>

                                {/* Address */}
                                <div className={styles.itemAddress}>
                                    {s.AddressString.substring(0, s.AddressString.indexOf(','))},
                                    <br/>
                                    {s.AddressString.substring(s.AddressString.indexOf(',')+2, s.AddressString.length)}
                                </div>

                                {/* Phone */}
                                <div className={styles.itemPhone}>{s.Phone}</div>

                            </Link>)}
                    </div>
                </>
            )}

            {/* Selected Range Stockists */}
            {stockists.filter(s => !s.IsFullRange).length > 0 && (
                <>
                    <JC_Title overrideClass={styles.groupTitle} title="Selected Range or Cafe Items" />
                    <div className={styles.listContainer}>
                        {stockists.filter(s => !s.IsFullRange).map(s =>
                            <Link key={s.Id} className={styles.item} href={s.SiteUrl} target="_blank">

                                {/* Logo */}
                                <Image
                                    className={styles.itemLogo}
                                    src={`/stockistLogos/${s.LogoImageName}.webp`}
                                    width={600}
                                    height={600}
                                    alt={`${s.Name} Logo`}
                                />

                                {/* Name */}
                                <div className={styles.itemName}>{s.Name}</div>

                                {/* Address */}
                                <div className={styles.itemAddress}>
                                    {s.AddressString.substring(0, s.AddressString.indexOf(','))},
                                    <br/>
                                    {s.AddressString.substring(s.AddressString.indexOf(',')+2, s.AddressString.length)}
                                </div>

                                {/* Phone */}
                                <div className={styles.itemPhone}>{s.Phone}</div>

                            </Link>)}
                    </div>
                </>
            )}

        </div>
    );
}


/*
Earth Wholefoods -JOONDALUP: (Freezer Range, Cafe Brownies). Mon-Sat.
Precious Organics - WILLAGEE: (CK Freezer Range). Mon-Sat.
Precious Organics - NEDLANDS: (CK Freezer Range). Mon-Sat.
The Sacred Earth Pantry - STRATHAM: (Crumpets, Brownies & Cafe Crumpets). 7 Days.
Nourish Me Wholefoods - EAST FREMANTLE: (Select Range, Crumpets and Cafe Crumpets).  Tues - Sun.
The Little Big Store Organics - WANGARA: (Freezer Range & Online Delivery). Mon - Sat.
Moore & Moore Cafe - FREMANTLE: (Cafe Crumpets & Brownies). Mon-Sun.
Raw Raw Raw - FREMANTLE MARKETS: (Brownies, Crumpets). Fri-Sun.
Loose Produce - VIC PARK: (Crumpets & Select Range). Mon - Sat.
*/


/*
Earth Wholefoods
Precious Organics
Precious Organics
The Sacred Earth Pantry
Nourish Me Wholefoods
The Little Big Store Organics
Moore & Moore Cafe
Raw Raw Raw
Loose Produce
*/