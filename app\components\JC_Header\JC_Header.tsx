import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import J<PERSON>_Button from "../JC_Button/JC_Button";
import JC_SearchBar from "../JC_SearchBar/JC_SearchBar";
import JC_CheckoutButton from "../JC_CheckoutButton/JC_CheckoutButton";
import { auth } from "@/app/auth";

export default async function JC_Header() {

    const session = await auth();
    let loggedIn:boolean = !!session?.user;

    return (
        <React.Fragment>

            {/* Header */}
            <div className={styles.mainContainer} id="JC_header">
            
                {/* Logo + Account */}
                <div className={styles.logoAccountContainer}>

                    {/* Logo */}
                    <Link href="/">
                        <Image
                            src="/logos/Main.webp"
                            width={0}
                            height={0}
                            className={styles.logo}
                            alt="CasellaKitchenLogo"
                            unoptimized
                        />
                    </Link>

                    {/* Bag + Account */}
                    <div className={`${styles.checkoutAccountContainer}`}>
                        
                        {/* Checkout */}
                        <JC_CheckoutButton />
                
                        {!loggedIn
                            /* Login/Register */
                            ? <Link href="/loginRegister" className={styles.loginRegisterContainer}>
                                <div className={styles.loginRegisterText}>Login/Register</div>
                            </Link>
                            /* Account Button */
                            : <JC_Button
                                linkToPage="account"
                                text={session?.user.FirstName}
                                iconName="User"
                            />}
                
                    </div>

                </div>

                {/* Navs */}
                <div className={styles.navsContainer}>

                    {/* Search Bar */}
                    <JC_SearchBar />

                    {/* Nav Buttons */}
                    <div className={styles.navButtons}>
                        <JC_Button linkToPage=""              text="Home"      />
                        <JC_Button linkToPage="productGroups" text="Products"  />
                        <JC_Button linkToPage="about"         text="About Us"  />
                        <JC_Button linkToPage="stockists"     text="Stockists" />
                        {/* <JC_Button linkToPage="recipes"       text="Recipes"   /> */}
                        <JC_Button linkToPage="contact"       text="Contact"   />
                    </div>
                    
                </div>

            </div>

            {/* Dietary Banner */}
            <div className={styles.dietaryBanner} id="JC_header_dietary">
                <div className={styles.word}>ORGANIC</div>
                <div className={styles.word}>PALEO FRIENDLY</div>
                <div className={styles.word}>VEGAN OPTIONS</div>
                <div className={styles.word}>LOW CARB</div>
                <div className={styles.word}>GLUTEN FREE</div>
                <div className={styles.word}>REFINED SUGAR FREE</div>
                <div className={styles.mediumScreenBreak} />
                <div className={styles.word}>DAIRY FREE</div>
                <div className={styles.word}>GRAIN FREE</div>
                <div className={styles.word}>YEAST FREE</div>
                <div className={styles.word}>SOY FREE</div>
                <div className={styles.word}>NO PRESERVATIVES</div>
                <div className={styles.word}>NO SEED OILS</div>
            </div>
        </React.Fragment>
    );
}
