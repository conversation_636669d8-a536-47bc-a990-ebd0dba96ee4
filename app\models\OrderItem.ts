import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { ProductModel } from "./Product";

export class OrderItemModel extends _Base {

    static apiRoute:string = "orderItem";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    OrderId: string;
    ProductId: string;
    ProductVariationIds: string[];
    Quantity: number;

    // Extended
    Ex_ProductName: string;
    Ex_VariationNames: string[];


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<OrderItemModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.OrderId = "";
        this.ProductId = "";
        this.ProductVariationIds = [];
        this.Quantity = 1;
        // Extended
        this.Ex_ProductName = "";
        this.Ex_VariationNames = [];
        Object.assign(this, init);
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.OrderId} | ${this.Ex_ProductName} | ${this.Ex_VariationNames}`;
    }


    // - ----- - //
    // - UTILS - //
    // - ----- - //

    getPrice(products: ProductModel[]) : number {
        let product = products.find(p => p.Id == this.ProductId)!;
        let variations = product?.Ex_Variations.filter(v => v.VariationCategoryCode != "None" && this.ProductVariationIds.map(id => id.toLowerCase()).includes(v.Id.toLowerCase()))!;
        return product.Price + variations.reduce((prev, cur) => prev + (cur.AddedPrice ?? 0), 0);
    }
}
