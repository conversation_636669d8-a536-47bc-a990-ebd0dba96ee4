$adminDividerBorder: solid $smallBorderWidth $offBlack;

@mixin adminListContainerStyles {
    height: max-content;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-margin: 10px;
    border: $adminDividerBorder;
    width: 220px;
    background-color: $offWhite;
    box-shadow: 0 0 10px rgba($offBlack, 0.2); // Add shadow matching border color, equal on all sides, stronger

    .listTitle {
        margin-bottom: 5px;
        width: 70%;
        padding: 10px;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        border-bottom: solid $smallBorderWidth $offBlack;
    }

    .tileContainer {
        width: 80%;
        display: flex;
        align-items: center;
        position: relative;
        .selectionTile {
            flex-grow: 1;
            margin: 5px auto 0 auto;
            padding: 6px;
            text-align: center;
            border-radius: $tinyBorderRadius;
            cursor: pointer;
            user-select: none;
            &:hover:not(.selected, .copyOtherSelected) { background-color: $greyHover; }
        }
        .selectionTile.selected:not(.inCopyOtherMode) {
            cursor: default !important;
        }
        .removeIcon {
            position: absolute;
            right: -25px;
            top: 60%; transform: translateY(-50%);
            padding: 5px 9px 6px 9px;
            border-radius: 50%;
            color: $errorColor;
            font-weight: bold;
            cursor: pointer;
        }
    }


}
@mixin adminFormContainerStyles {
    padding: 20px;
    border: solid $smallBorderWidth $secondaryColor;
    border-radius: $smallBorderRadius;
    background-color: $lightSecondaryColor;
    width: max-content;
    box-shadow: 0 0 10px rgba($offBlack, 0.2); // Match exactly the list container shadow
}