import { NextRequest, NextResponse } from "next/server";
import { ContactModel } from "@/app/models/Contact";
import { ContactBusiness } from "./business";

// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {

    try {
        const contactData:ContactModel = await request.json();
        await ContactBusiness.Create(contactData);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}