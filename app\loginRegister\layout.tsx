import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen - Login/Register",
    description: "Login or create an account for Casella Kitchen."
};

export default async function Layout_LoginRegister(_: Readonly<{
    children: React.ReactNode;
}>) {

    // - AUTH - //

    const session = await auth();
    if (session) {
        redirect("/account");
    }


    // - MAIN - //

    return _.children;
    
}
