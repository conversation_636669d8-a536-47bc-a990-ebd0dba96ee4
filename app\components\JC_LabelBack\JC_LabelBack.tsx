import styles from "./JC_LabelBack.module.scss";
import React from 'react';
import Image from "next/image";
import { JC_Utils } from "@/app/Utils";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { LabelSizeEnum } from '@/app/enums/LabelSize';
import { SettingsTypeEnum } from '@/app/enums/VariationSettings';

export default function JC_LabelBack(_: Readonly<{
    product: ProductModel;
    varData: ProductVariationsDataModel;
}>) {

    // - STATE - //

    // Get the selected variation IDs from the varData
    const selectedIds = _.varData.ProductVariationIds || undefined;

    // Get item settings for different settings types
    let itemSettings = {
        labelSize: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.LabelSize, selectedIds),
        ingredients: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.Ingredients, selectedIds),
        servings: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, selectedIds),
        label: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.Labels, selectedIds)
    };

    // Adjust scales based on label size
    let size = itemSettings.labelSize.LabelSize;
    let scaleClass:string = "";
    let reduceSizeTitle = 0;
    let reduceSizeBody  = 0;
    if (size == LabelSizeEnum.Small) {
        scaleClass = styles.small;
        reduceSizeTitle = 1;
        reduceSizeBody  = 1;
    }
    if (size == LabelSizeEnum.TwoByFive) {
        scaleClass = styles.twoByFive;
        reduceSizeTitle = 4;
        reduceSizeBody  = 2;
    }

    // For backward compatibility, get variations for settings
    let labelSettingsVar = _.product.getVariationForSettings(SettingsTypeEnum.Labels, selectedIds);
    let selectedVarData = _.varData; // Use the provided varData directly


    // - HANDLES - //

    function getNameOverrideText() {
        let tempDiv = document.createElement('div');
        tempDiv.innerHTML = itemSettings.label.BackLabelNameOverride;
        return tempDiv.textContent ?? "";
    }
    function generateTitle() {
        if (!JC_Utils.stringNullOrEmpty(getNameOverrideText())) {
            return itemSettings.label.BackLabelNameOverride;
        } else {
            let title = _.product.Name;
            // Only append variation name if the variation exists and is not deleted
            if (labelSettingsVar != null && !labelSettingsVar.Deleted) {
                title += ` - ${labelSettingsVar.Name}`;
            }
            return title;
        }
    }
    function getTitleSize() {
        let len = 0;
        let titleOverride = getNameOverrideText();
        if (!JC_Utils.stringNullOrEmpty(titleOverride)) {
            len = titleOverride.length ?? 0;
        } else {
            len = generateTitle().length ?? 0;
        }
        if      (len < 25) return 16 - reduceSizeTitle;
        else if (len < 27) return 15 - reduceSizeTitle;
        else if (len < 30) return 14 - reduceSizeTitle;
        else if (len < 32) return 13 - reduceSizeTitle;
        else if (len < 35) return 12 - reduceSizeTitle;
        else if (len < 38) return 11 - reduceSizeTitle;
        else               return 10 - reduceSizeTitle;
    }
    const titleSize = getTitleSize();

    // Serving size
    let servSize = itemSettings.servings.ServingSizeGrams;


    // - MAIN - //

    return (
        <div className={`${styles.mainContainer} ${scaleClass}`}>

            {/* Left */}
            <div className={styles.leftSide}>

                {/* Logo + Contact */}
                <div className={styles.logoAndContact}>
                    <div className={styles.productOf}>Product of</div>
                    <Image
                        src={`/icons/CK.webp`}
                        width={0}
                        height={0}
                        className={styles.logo}
                        alt="Instagram"
                        unoptimized
                    />
                    <div className={styles.address}>
                        <div>165 Henley St,</div>
                        <div>Henley Brook</div>
                        <div>Perth WA 6055</div>
                    </div>
                    <div className={styles.phone}>
                        Ph: 04 3808 4751
                    </div>
                    <div className={styles.email}>
                        <EMAIL>
                    </div>
                    <div className={styles.social}>
                        <Image
                            src={`/icons/SocialFacebook.webp`}
                            width={0}
                            height={0}
                            className={styles.facebookIcon}
                            alt="Facebook"
                            unoptimized
                        />
                        <Image
                            src={`/icons/SocialInsta.webp`}
                            width={0}
                            height={0}
                            className={styles.instaIcon}
                            alt="Instagram"
                            unoptimized
                        />
                        <div>Casella Kitchen</div>
                    </div>
                </div>

                {/* Biodegradable */}
                <div className={styles.biodegradable}>
                    {!JC_Utils.stringNullOrEmpty(itemSettings.label?.BiodegOverrideText)
                        ? itemSettings.label?.BiodegOverrideText
                        : "Biodegradable/Non-Recyclable Packaging"}
                </div>

                {/* Barcode */}
                <div className={styles.barcodeContainer}>
                    <Image
                        src={`/barcodes/${_.varData.BarcodeNumber}.webp`}
                        width={0}
                        height={0}
                        className={styles.barcode}
                        alt="Barcode"
                        unoptimized
                    />
                </div>

            </div>

            {/* Right */}
            <div className={styles.rightSide}>

                {/* Name */}
                <div className={styles.name} style={{fontSize: `${titleSize}px`}} dangerouslySetInnerHTML={{ __html: generateTitle() }} />

                {/* Nut Val Title */}
                <div className={styles.nutInfoTitle}>
                    Nutritional Information
                </div>

                {/* Servings */}
                <div className={styles.servingsDataContainer}>
                    <div>Servings per Package:</div>
                    <div>{itemSettings.servings.ServingsPerPack}</div>
                    <div>Serving Size:</div>
                    <div>{itemSettings.servings.ServingSizeGrams} g</div>
                    {_.product.Name.toLowerCase().includes("pasta") && <div className={styles.cookedText}>(Cooked)</div>}
                </div>

                {/* Nut Vals */}
                <div className={styles.nutritionalValuesHeadingsContainer}>
                    {_.product.Name.toLowerCase().includes("pasta") ? <div className={styles.cookedPastaText}>Values Based On<br/>Cooked Pasta</div> : <div/>}
                    <div>Average Quantity<br/>per Serving</div>
                    <div>Average Quantity<br/>per 100g</div>
                </div>
                <div className={styles.nutritionalValues}>
                    <div>Energy</div>
                    <div>{(selectedVarData.Ex_NutVals.Kilojoules*(servSize/100))?.toFixed()}</div><div>kJ</div>
                    <div>{selectedVarData.Ex_NutVals.Kilojoules?.toFixed()}</div><div>kJ</div>
                    <div>Protein</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Protein*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Protein, 1)}</div><div>g</div>
                    <div>Fat, total</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.FatTotal*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.FatTotal, 1)}</div><div>g</div>
                    <div>- saturated</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.FatSaturated*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.FatSaturated, 1)}</div><div>g</div>
                    <div>Carbohydrate</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Carbohydrate*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Carbohydrate, 1)}</div><div>g</div>
                    <div>- sugars</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Sugars*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Sugars, 1)}</div><div>g</div>
                    <div>Fibre</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Fiber*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals.Fiber, 1)}</div><div>g</div>
                    <div>Net Carbs</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals._NetCarbs*(servSize/100), 1)}</div><div>g</div>
                    <div>{JC_Utils.roundAndCutZeroes(selectedVarData.Ex_NutVals._NetCarbs, 1)}</div><div>g</div>
                    <div>Sodium</div>
                    <div>{(selectedVarData.Ex_NutVals.Sodium*(servSize/100))?.toFixed()}</div><div>mg</div>
                    <div>{selectedVarData.Ex_NutVals.Sodium?.toFixed()}</div><div>mg</div>
                </div>

                {/* Handmade With Love */}
                <div className={styles.handmadeWithLove}>
                    <div><b>Handmade with</b></div>
                    <div><i>Made in a Gluten-Free premises but where other</i></div>
                    <div><i>tree nuts, eggs & sesame seeds have been used.</i></div>
                    <Image
                        src={`/icons/Heart.webp`}
                        width={0}
                        height={0}
                        className={styles.heartIcon}
                        alt="Barcode"
                        unoptimized
                    />
                </div>

            </div>

        </div>
    );
}