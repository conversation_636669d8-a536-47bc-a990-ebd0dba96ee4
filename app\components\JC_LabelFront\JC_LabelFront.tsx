import styles from "./JC_LabelFront.module.scss";
import React, { useState, useEffect, useRef } from 'react';
import Image from "next/image";
import { JC_Utils } from "@/app/Utils";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";
import { LabelSizeEnum } from '@/app/enums/LabelSize';
import { SettingsTypeEnum } from '@/app/enums/VariationSettings';
import { ItemIngredientModel } from "@/app/models/ItemIngredient";

export default function JC_LabelFront(_: Readonly<{
    product: ProductModel;
    variations?: ProductVariationModel[];
    varData: ProductVariationsDataModel;
    onRenderComplete?: () => void;
}>) {

    // - STATE - //

    // Get the selected variation IDs from the variations prop
    const selectedIds = _.variations?.map(v => v.Id) || [];

    // Dynamic ingredients font size state
    const [ingredientsFontSize, setIngredientsFontSize] = useState<number>(11);
    const [isAdjusting, setIsAdjusting] = useState<boolean>(false);
    const mainContainerRef = useRef<HTMLDivElement>(null);
    const storeFrozenRef = useRef<HTMLDivElement>(null);



    // Get item settings for different settings types
    let itemSettings = {
        labelSize: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.LabelSize, selectedIds),
        servings: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.Servings, selectedIds),
        label: _.product.getItemSettingsForSettingsType(SettingsTypeEnum.Labels, selectedIds)
    };

    // Adjust scales based on label size
    let size = itemSettings.labelSize.LabelSize;
    let scaleClass:string = "";
    let reduceSizeTitle = 0;
    let reduceSizeAllergen  = 0;
    if (size == LabelSizeEnum.Small) {
        scaleClass = styles.small;
        reduceSizeTitle     = 1;
        reduceSizeAllergen  = 1;
    }
    if (size == LabelSizeEnum.TwoByFive) {
        scaleClass = styles.twoByFive;
        reduceSizeTitle     = 4;
        reduceSizeAllergen  = 0.5;
    }

    // Get ingredients using product model's getItemIngredients method
    let selectedVarData = _.varData; // Use the provided varData directly

    // Use the product model's getItemIngredients method to get the appropriate ingredients
    let itemIngredients = _.product.getItemIngredients(selectedIds).sort((a,b) => (a.Ex_Ingredient?.Name || '') > (b.Ex_Ingredient?.Name || '') ? 1 : -1);

    // Effect to reset font size when content changes
    useEffect(() => {
        if (!isAdjusting) {
            setIngredientsFontSize(11); // Reset to starting size
            setIsAdjusting(true);
        }
    }, [itemIngredients, itemSettings.label.StoreFrozenOverride]);

    // Effect to handle initial load case where no adjustment is needed
    useEffect(() => {
        if (!isAdjusting && ingredientsFontSize === 11) {
            // Check if adjustment is needed on initial load
            const timeoutId = setTimeout(() => {
                if (!mainContainerRef.current || !storeFrozenRef.current) return;

                const mainContainer = mainContainerRef.current;
                const storeFrozenElement = storeFrozenRef.current;
                const mainRect = mainContainer.getBoundingClientRect();
                const storeFrozenRect = storeFrozenElement.getBoundingClientRect();

                // If text already fits at initial size, notify completion
                if (!(storeFrozenRect.bottom > mainRect.bottom || storeFrozenRect.right > mainRect.right)) {
                    if (_.onRenderComplete) {
                        _.onRenderComplete();
                    }
                } else {
                    // Start adjustment process
                    setIsAdjusting(true);
                }
            }, 150); // Slightly longer delay for initial check

            return () => clearTimeout(timeoutId);
        }
    }, [ingredientsFontSize, isAdjusting, _.onRenderComplete]);

    // Effect to dynamically adjust ingredients font size based on Store Frozen text visibility
    useEffect(() => {
        if (!isAdjusting) return; // Only adjust when we're in adjusting mode

        const adjustFontSize = () => {
            if (!mainContainerRef.current || !storeFrozenRef.current) return;

            const mainContainer = mainContainerRef.current;
            const storeFrozenElement = storeFrozenRef.current;

            // Check if Store Frozen text is fully visible within the main container
            const mainRect = mainContainer.getBoundingClientRect();
            const storeFrozenRect = storeFrozenElement.getBoundingClientRect();

            // If Store Frozen text overflows the main container, reduce ingredients font size
            if (storeFrozenRect.bottom > mainRect.bottom || storeFrozenRect.right > mainRect.right) {
                setIngredientsFontSize(prevSize => {
                    const newSize = prevSize - 0.5;
                    return newSize >= 6 ? newSize : 6; // Minimum font size of 6
                });
            } else {
                // Text fits, stop adjusting
                setIsAdjusting(false);
                // Notify parent that rendering is complete
                if (_.onRenderComplete) {
                    _.onRenderComplete();
                }
            }
        };

        // Small delay to ensure DOM is fully rendered
        const timeoutId = setTimeout(adjustFontSize, 100);

        return () => clearTimeout(timeoutId);
    }, [isAdjusting, ingredientsFontSize]); // Re-run when adjusting state or font size changes

    // - HANDLES - //

    // Kangaroo
    function getKangaroo() {
        let percAus:number = selectedVarData.Ex_NutVals.PercAus;
        if (percAus < 10) {
            return "10 Less Than";
        } else {
            return percAus.toFixed();
        }
    }

    // Title
    function getNameOverrideText() {
        let tempDiv = document.createElement('div');
        tempDiv.innerHTML = itemSettings.label.FrontLabelNameOverride;
        return tempDiv.textContent ?? "";
    }
    function generateTitle() {
        if (!JC_Utils.stringNullOrEmpty(getNameOverrideText())) {
            return itemSettings.label.FrontLabelNameOverride;
        } else {
            let title = _.product.Name;
            // Get the variation for the label settings to get its name
            let labelVar = _.product.getVariationForSettings(SettingsTypeEnum.Labels, selectedIds);
            if (_.product.Ex_Variations.length > 1 && labelVar != null) {
                title += ` - ${labelVar.Name}`;
            }
            return title;
        }
    }
    function getTitleSize() {
        let len = 0;
        let titleOverride = getNameOverrideText();
        if (!JC_Utils.stringNullOrEmpty(titleOverride)) {
            len = titleOverride.length ?? 0;
        } else {
            len = generateTitle().length ?? 0;
        }
        len += (itemSettings.servings.PackWeightText?.length ?? 0) / 2;
        if      (len < 30) return 18 - reduceSizeTitle;
        else if (len < 35) return 16 - reduceSizeTitle;
        else if (len < 40) return 14 - reduceSizeTitle;
        else               return 12 - reduceSizeTitle;
    }
    const titleSize = getTitleSize();

    // Icon
    let iconName = itemSettings.label.IconName;

    // Ingredients
    function getIngPercent(itemIng:ItemIngredientModel) {
        // Don't account for weight change for front-label ingredient percentages
        // Only include non-deleted ingredients in the total
        let totalWeight = itemIngredients
            .filter(ing => !ing.Deleted)
            .reduce((prev:number, cur:ItemIngredientModel) => prev+(cur.AmountGrams ?? 0), 0);

        let percent = ((itemIng?.AmountGrams ?? 0) / totalWeight) * 100;
        if (percent < 1) {
            return "<1";
        } else {
            return percent.toFixed();
        }
    }
    function buildIngredients() {
        return itemIngredients.filter((ing:ItemIngredientModel) =>
                                // Filter out ingredients that should be hidden on the label
                                !ing.Ex_Ingredient?.HideOnLabel &&
                                // Filter out ingredients with amount of 0
                                (ing.AmountGrams !== 0 && ing.AmountGrams !== null) &&
                                // Filter out deleted ingredients
                                !ing.Deleted)
                             .sort((a:ItemIngredientModel, b:ItemIngredientModel) =>
                                (a.AmountGrams! == b.AmountGrams!) ?
                                    ((a.Ex_Ingredient?.Name || '') > (b.Ex_Ingredient?.Name || '') ? 1 : -1) :
                                    (a.AmountGrams! < b.AmountGrams! ? 1 : -1))
                             .map((ing:ItemIngredientModel) => {
                                let thisIngString = ing.Ex_Ingredient?.Name || '';
                                if (ing.ShowPercent) thisIngString += ` (${getIngPercent(ing)}%)`;
                                if (ing.Ex_Ingredient?.IsOrganic) thisIngString += `<b class=${styles.organicStar}>*</b>`;
                                if (!JC_Utils.stringNullOrEmpty(ing.Ex_Ingredient?.LabelDescription)) thisIngString += ` (${ing.Ex_Ingredient?.LabelDescription})`
                                if (ing.Ex_Ingredient?.IsAllergen) thisIngString = `<b>${thisIngString}</b>`;
                                return thisIngString;
                              })
                              .join(', ');
    }

    // Allergen
    function getAllergenSize() {
        let len = itemSettings.label?.AllergensText?.length;
        if      (len < 17) return 14 - reduceSizeAllergen;
        else if (len < 23) return 13 - reduceSizeAllergen;
        else if (len < 30) return 12 - reduceSizeAllergen;
        else if (len < 35) return 11 - reduceSizeAllergen;
        else if (len < 40) return 10 - reduceSizeAllergen;
        else if (len < 50) return 9  - reduceSizeAllergen;
        else               return 8  - reduceSizeAllergen;
    }
    const allergenSize = getAllergenSize();

    // Free From
    function getFreeFromSize() {
        let len = itemSettings.label?.FreeFromText?.length;
        if      (len < 45) return 13 - reduceSizeAllergen;
        else if (len < 55) return 11 - reduceSizeAllergen;
        else if (len < 65) return 10 - reduceSizeAllergen;
        else if (len < 75) return 9  - reduceSizeAllergen;
        else if (len < 85) return 8  - reduceSizeAllergen;
        else if (len < 95) return 7  - reduceSizeAllergen;
        else               return 6  - reduceSizeAllergen;
    }
    const freeFromSize = getFreeFromSize();

    // Store Frozen
    function getStoreFrozenSize() {
        let len = 11;
        if (itemSettings.label.StoreFrozenOverride?.includes("</p><p>")) {
            len -= 3;
        }
        return 9;
    }
    const storeFrozenSize = getStoreFrozenSize();


    // - MAIN - //

    return (
        <div ref={mainContainerRef} className={`${styles.mainContainer} ${scaleClass}`}>

            {/* Dietary Text (Absolute) */}
            <div className={styles.dietaryContainer}>
                {itemIngredients.filter(i => !i.Deleted).some((i:ItemIngredientModel) => i.IngredientId == 'e2feba84-7ddf-4298-95c6-667f765466e8' || i.IngredientId == '7d100a47-6ac9-4e94-ab84-02cc8f1b40c1') ? <div>70% Organic</div> : <div>Organic</div> /* Products that have either "Aust Almond Flour"  or "Australian Almond Flour" are not organic */}
                <div>Paleo</div>
                {itemIngredients.filter(i => !i.Deleted).some((i:ItemIngredientModel) => i.Ex_Ingredient?.IsNotVegan) || <div>Vegan</div>}
                <div>Gluten Free</div>
            </div>

            {/* Logo */}
            <Image
                className={styles.logo}
                src="/logos/Main.webp"
                width={0}
                height={0}
                alt="CasellaKitchenLogo"
                unoptimized
            />

            {/* Kangaroo (Absolute) */}
            <Image
                className={styles.kangaroo}
                src={`/kangaroos/${getKangaroo()}.webp`}
                width={0}
                height={0}
                alt="PercentAustralian"
                unoptimized
            />

            {/* Title */}
            <div className={styles.title} style={{ marginLeft: `${(itemSettings.label.FrontLabelNameXOffset || 0)*2}px`, marginRight: `-${(itemSettings.label.FrontLabelNameXOffset || 0)*2}px` }}>
                {iconName && (itemSettings.label.IconVisible !== false) &&
                <Image
                    className={styles.titleIcon}
                    src={`/labelImages/${iconName}.webp`}
                    width={0}
                    height={0}
                    alt="Icon"
                    style={{
                        left: `${(itemSettings.label.IconX??0) - (itemSettings.label.FrontLabelNameXOffset*2)}px`,
                        top: `${itemSettings.label.IconY??0}px`,
                        width: `${itemSettings.label.IconSize??0}px`
                    }}
                    unoptimized
                />} {/* Icon position and size can be customized in admin panel */}
                <div
                    style={{ fontSize: `${titleSize}px` }}
                    dangerouslySetInnerHTML={{ __html: generateTitle() }}
                />
                {!JC_Utils.stringNullOrEmpty(itemSettings.servings.PackWeightText) && <div className={styles.packWeightText} style={{fontSize: `${titleSize}px`}}>{itemSettings.servings.PackWeightText}</div>}
            </div>

            {/* Instructions */}
            {!JC_Utils.stringNullOrEmpty(itemSettings.label?.InstructionsText?.replace('<p></p>', '')) &&
            <div className={styles.bodyText} style={{fontSize: `${itemSettings.label.InstructionsFontSize || 10}px`}} dangerouslySetInnerHTML={{ __html: `<p><u>Instructions</u>: ${itemSettings.label!.InstructionsText.substring(3, itemSettings.label!.InstructionsText.length-4)}</p>` }} />}

            {/* Ingredients */}
            <div className={styles.bodyText} style={{
                fontSize: `${ingredientsFontSize}px`,
                marginTop: `${itemSettings.label.IngredientsYOffset || 0}px`
            }} dangerouslySetInnerHTML={{ __html: `<p><u>Ingredients</u>: ${buildIngredients()}.</p>` }}/>

            {/* Note */}
            {!JC_Utils.stringNullOrEmpty(itemSettings.label?.NoteText?.replace('<p></p>', '')) &&
            <div className={styles.bodyText} style={{fontSize: `${itemSettings.label.NoteFontSize || 10}px`}} dangerouslySetInnerHTML={{ __html: `<p><u>Note</u>: ${itemSettings.label!.NoteText.substring(3, itemSettings.label!.NoteText.length-4)}</p>` }} />}

            {/* Contains Organic */}
            <div className={styles.containsOrganicText}>
                <i>*Certified Organic Ingredient</i>
            </div>

            {/* Allergen Advice */}
            {!JC_Utils.stringNullOrEmpty(itemSettings.label?.AllergensText) &&
            <div className={styles.allergenAdviceText} style={{fontSize: `${allergenSize}px`}}>
                {`Allergen Advice: Contains ${itemSettings.label?.AllergensText}`}
            </div>}

            {/* Free From */}
            {!JC_Utils.stringNullOrEmpty(itemSettings.label?.FreeFromText) &&
            <div className={styles.freeFromText} style={{fontSize: `${freeFromSize}px`}}>
                {`Free From: ${itemSettings.label?.FreeFromText}`}
            </div>}

            {/* Store Frozen */}
            <div
                ref={storeFrozenRef}
                className={styles.storeFrozenText}
                style={{fontSize: `${storeFrozenSize}px`}}
                dangerouslySetInnerHTML={{ __html: !JC_Utils.stringNullOrEmpty(itemSettings.label.StoreFrozenOverride?.replace('<p></p>', '')) ? itemSettings.label.StoreFrozenOverride : "Store Frozen. Consume Best Before:" }}
            />

        </div>
    );
}