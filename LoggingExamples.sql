-- SQL Script with Console Logging Examples
-- This script demonstrates different ways to add logging to SQL scripts in PostgreSQL

-- 1. Basic RAISE NOTICE - Simple console output
DO $$
BEGIN
    RAISE NOTICE 'Basic console log: This is a simple message';
END $$;

-- 2. RAISE NOTICE with variable interpolation
DO $$
DECLARE
    table_name TEXT := 'User';
    record_count INTEGER := 42;
BEGIN
    RAISE NOTICE 'Table % has % records', table_name, record_count;
END $$;

-- 3. Different log levels (in decreasing severity)
DO $$
BEGIN
    -- Only use in extreme cases - terminates the transaction
    -- RAISE EXCEPTION 'Critical error: Database connection failed';
    
    -- Warnings - doesn't terminate but shows prominently
    RAISE WARNING 'Warning: Table has no primary key';
    
    -- Informational messages
    RAISE NOTICE 'Notice: Starting table creation process';
    
    -- Less important messages
    RAISE INFO 'Info: Processing row 1 of 1000';
    
    -- Debug messages (often hidden by default)
    RAISE DEBUG 'Debug: Variable x = 123';
    
    -- Lowest level - rarely shown
    RAISE LOG 'Log: Query execution completed';
END $$;

-- 4. Conditional logging based on a condition
DO $$
DECLARE
    error_count INTEGER := 5;
BEGIN
    IF error_count > 0 THEN
        RAISE NOTICE 'Found % errors during processing', error_count;
    END IF;
END $$;

-- 5. Logging in a loop
DO $$
DECLARE
    i INTEGER;
BEGIN
    RAISE NOTICE 'Starting loop execution...';
    
    FOR i IN 1..3 LOOP
        RAISE NOTICE 'Processing iteration %', i;
        -- Simulate some work
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Loop execution completed';
END $$;

-- 6. Logging with timestamps
DO $$
BEGIN
    RAISE NOTICE 'Operation started at %', now();
    
    -- Simulate some work
    PERFORM pg_sleep(0.5);
    
    RAISE NOTICE 'Operation completed at %', now();
END $$;

-- 7. Logging execution time
DO $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
BEGIN
    start_time := clock_timestamp();
    RAISE NOTICE 'Starting operation at %', start_time;
    
    -- Simulate some work
    PERFORM pg_sleep(0.7);
    
    end_time := clock_timestamp();
    duration := end_time - start_time;
    
    RAISE NOTICE 'Operation completed at %. Duration: %', end_time, duration;
END $$;

-- 8. Logging during table creation
DO $$
BEGIN
    RAISE NOTICE 'Starting table creation process...';
    
    -- This would be your actual CREATE TABLE statement
    -- CREATE TABLE example_table (...);
    
    RAISE NOTICE 'Table created successfully';
END $$;

-- 9. Logging during a transaction with progress updates
DO $$
DECLARE
    total_tables INTEGER := 20;
    i INTEGER;
BEGIN
    RAISE NOTICE 'Starting database setup (% tables to create)', total_tables;
    
    FOR i IN 1..total_tables LOOP
        RAISE NOTICE 'Creating table % of % (% percent complete)', 
            i, total_tables, ROUND((i::FLOAT/total_tables)*100);
        
        -- Simulate table creation
        PERFORM pg_sleep(0.05);
    END LOOP;
    
    RAISE NOTICE 'Database setup completed successfully';
END $$;

-- 10. Logging with custom formatting
DO $$
BEGIN
    RAISE NOTICE E'\n----- OPERATION REPORT -----\n'
                 'Status: Success\n'
                 'Tables Created: 20\n'
                 'Errors: 0\n'
                 'Duration: 5.2 seconds\n'
                 '----------------------------';
END $$;

-- 11. Practical example: Logging during table creation in your script
-- This shows how you might integrate logging into your actual CreateTables.sql script

DO $$
BEGIN
    RAISE NOTICE 'Starting database schema creation...';
    
    -- User Table
    RAISE NOTICE 'Creating User table...';
    -- CREATE TABLE public."User" (...);
    RAISE NOTICE 'User table created successfully';
    
    -- Address Table
    RAISE NOTICE 'Creating Address table...';
    -- CREATE TABLE public."Address" (...);
    RAISE NOTICE 'Address table created successfully';
    
    -- Continue for all tables...
    
    RAISE NOTICE 'Database schema creation completed successfully';
END $$;

-- 12. Logging with error handling
DO $$
BEGIN
    BEGIN
        RAISE NOTICE 'Attempting risky operation...';
        -- Simulate an error
        -- PERFORM 1/0;  -- This would cause division by zero error
        RAISE NOTICE 'Risky operation completed successfully';
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Error occurred: %', SQLERRM;
    END;
END $$;

-- 13. Logging to track foreign key creation
DO $$
BEGIN
    RAISE NOTICE 'Creating foreign key constraint FK_Address_User...';
    -- ALTER TABLE public."Address" ADD CONSTRAINT "FK_Address_User" FOREIGN KEY ("UserId") REFERENCES public."User" ("Id");
    RAISE NOTICE 'Foreign key constraint FK_Address_User created successfully';
END $$;

-- 14. Logging with dynamic SQL
DO $$
DECLARE
    table_names TEXT[] := ARRAY['User', 'Address', 'Product'];
    i INTEGER;
BEGIN
    FOR i IN 1..array_length(table_names, 1) LOOP
        RAISE NOTICE 'Processing table: %', table_names[i];
        -- Your dynamic SQL here
    END LOOP;
END $$;

-- 15. Integrating logging into your DropTables.sql script
DO $$
BEGIN
    RAISE NOTICE 'Starting database cleanup...';
    
    -- First, drop all constraints that might cause circular dependencies
    RAISE NOTICE 'Dropping circular dependency constraints...';
    -- ALTER TABLE IF EXISTS public."ProductVariation" DROP CONSTRAINT IF EXISTS "FK_ProductVariation_DependentVariation";
    
    -- Payment related tables
    RAISE NOTICE 'Dropping payment tables...';
    -- DROP TABLE IF EXISTS public."Payment";
    -- DROP TABLE IF EXISTS public."PaymentStatus";
    
    -- Continue for all tables...
    
    RAISE NOTICE 'Database cleanup completed successfully';
END $$;
