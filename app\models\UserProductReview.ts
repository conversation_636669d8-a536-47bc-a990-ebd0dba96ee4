import { _Base } from "./_Base";

export class UserProductReviewModel extends _Base {

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    ProductId: string;
    UserId: string;
    Rating: number;
    Text: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserProductReviewModel>) {
        super(init);
        this.Id = '';
        this.ProductId = '';
        this.UserId = '';
        this.Rating = 0;
        this.Text = '';
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.ProductId} | ${this.UserId} | ${this.Rating}`;
    }
}
