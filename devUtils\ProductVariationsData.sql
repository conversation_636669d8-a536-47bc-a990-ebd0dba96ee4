SELECT "data"."Id",
       "data"."ProductId",
       "product"."Name" "__Product",
       "data"."ProductVariationIds",
        COALESCE(
            (SELECT array_agg("variation"."Name" ORDER BY "variation_ord")
            FROM unnest("data"."ProductVariationIds") WITH ORDINALITY AS "variationId"(id, variation_ord)
            JOIN public."ProductVariation" "variation" ON "variation"."Id" = "variationId".id::uuid
            ), '{}'
        ) AS "__ProductVariations",
       "data"."Code",
       "data"."BarcodeNumber",
       "data"."CreatedAt",
       "data"."ModifiedAt",
       "data"."Deleted"
FROM public."ProductVariationsData" "data"
INNER JOIN public."Product" "product" ON "data"."ProductId" = "product"."Id"
WHERE 1=1
      AND "data"."Deleted" = 'False'
      -- AND "data"."Id" = '6377e478-e1e6-4209-a5a9-2cfa466eafc7'
      -- AND "data"."ProductId" = 'f32b1fd3-97e1-4f18-b87c-052ca1f25a83' -- Bread/Rolls/Paninis
      -- AND "data"."ProductId" = 'abba8870-3a1d-45db-b90f-7f870a526cc5' -- Crumpets
    --   AND "data"."ProductId" = '1e559c96-1603-4b55-aa43-d043e9a8fb35' -- Special Occasion Crumpets
      -- AND "data"."ProductId" = '81add596-fa0a-4994-9468-1405400fa2a2' -- Almond Pasta
      -- AND "data"."ProductId" = 'fd709898-c8ae-40cb-92e2-248383f4d2c9'
    --   AND "data"."BarcodeNumber" = '0754523545592'
ORDER BY "product"."Name";
-- ORDER BY "data"."CreatedAt" DESC;


-- Delete
-- UPDATE public."ProductVariationsData" SET "Deleted" = 'True' WHERE "Id" = '91d384aa-e08b-4df7-a155-a592caba2689'
-- UPDATE public."ProductVariationsData" SET "Deleted" = 'True' WHERE "Id" IN (
--     '68f68370-8eb6-469e-89f4-32a0661eba1f',
--     '91d384aa-e08b-4df7-a155-a592caba2689',
--     '4d5ce089-1795-4f24-896a-954dc2fd6282',
--     'bbd96d2a-ffa5-45b8-96af-48cfe0f0fcfa'
-- )


-- INSERT INTO public."ProductVariationsData"
-- ("Id", "ProductId", "ProductVariationIds", "Code", "BarcodeNumber")
-- VALUES
-- ('D06F6DDD-7ECD-4083-A170-198626C2F822', '1e559c96-1603-4b55-aa43-d043e9a8fb35', '{182788aa-b5c3-49a7-9fff-688b83c1efa5,abbc8a3c-b1a2-4dc2-93a8-4327d94945d5}', 'TEST_CODE', '0754523545592');


-- Update barcode
-- UPDATE public."ProductVariationsData" SET "BarcodeNumber" = '0754523545004' WHERE "Id" = '4a40477a-f1ac-4356-8720-30b284eec551';


-- Get available barcodes
-- WITH AllBarcodes AS (
--     SELECT unnest(ARRAY[
--         '0754523545004', '0754523545011', '0754523545028', '0754523545035', '0754523545042',
--         '0754523545059', '0754523545066', '0754523545073', '0754523545080', '0754523545097',
--         '0754523545103', '0754523545110', '0754523545127', '0754523545134', '0754523545141',
--         '0754523545158', '0754523545165', '0754523545172', '0754523545189', '0754523545196',
--         '0754523545202', '0754523545219', '0754523545226', '0754523545233', '0754523545240',
--         '0754523545257', '0754523545264', '0754523545271', '0754523545288', '0754523545295',
--         '0754523545301', '0754523545318', '0754523545325', '0754523545332', '0754523545349',
--         '0754523545356', '0754523545363', '0754523545370', '0754523545387', '0754523545394',
--         '0754523545400', '0754523545417', '0754523545424', '0754523545431', '0754523545448',
--         '0754523545455', '0754523545462', '0754523545479', '0754523545486', '0754523545493',
--         '0754523545509', '0754523545516', '0754523545523', '0754523545530', '0754523545547',
--         '0754523545554', '0754523545561', '0754523545578', '0754523545585', '0754523545592',
--         '0754523545608', '0754523545615', '0754523545622', '0754523545639', '0754523545646',
--         '0754523545653', '0754523545660', '0754523545677', '0754523545684', '0754523545691',
--         '0754523545707', '0754523545714', '0754523545721', '0754523545738', '0754523545745',
--         '0754523545752', '0754523545769', '0754523545776', '0754523545783', '0754523545790',
--         '0754523545806', '0754523545813', '0754523545820', '0754523545837', '0754523545844',
--         '0754523545851', '0754523545868', '0754523545875', '0754523545882', '0754523545899',
--         '0754523545905', '0754523545912', '0754523545929', '0754523545936', '0754523545943',
--         '0754523545950', '0754523545967', '0754523545974', '0754523545981', '0754523545998',
--         '0754523546001', '0754523546018', '0754523546025', '0754523546032', '0754523546049',
--         '0754523546056', '0754523546063', '0754523546070', '0754523546087', '0754523546094',
--         '0754523546100', '0754523546117', '0754523546124', '0754523546131', '0754523546148',
--         '0754523546155', '0754523546162', '0754523546179', '0754523546186', '0754523546193',
--         '0754523546209', '0754523546216', '0754523546223', '0754523546230', '0754523546247',
--         '0754523546254', '0754523546261', '0754523546278', '0754523546285', '0754523546292',
--         '0754523546308', '0754523546315', '0754523546322', '0754523546339', '0754523546346',
--         '0754523546353', '0754523546360', '0754523546377', '0754523546384', '0754523546391',
--         '0754523546407', '0754523546414', '0754523546421', '0754523546438', '0754523546445',
--         '0754523546452', '0754523546469', '0754523546476', '0754523546483', '0754523546490',
--         '0754523546506', '0754523546513', '0754523546520', '0754523546537', '0754523546544',
--         '0754523546551', '0754523546568', '0754523546575', '0754523546582', '0754523546599',
--         '0754523546605', '0754523546612', '0754523546629', '0754523546636', '0754523546643',
--         '0754523546667', '0754523546674', '0754523546681', '0754523546698', '0754523546704',
--         '0754523546711', '0754523546728', '0754523546735', '0754523546742', '0754523546759',
--         '0754523546766', '0754523546773', '0754523546780', '0754523546797', '0754523546803',
--         '0754523546810', '0754523546827', '0754523546834', '0754523546841', '0754523546858',
--         '0754523546865', '0754523546872', '0754523546889', '0754523546896', '0754523546902',
--         '0754523546919', '0754523546926', '0754523546933', '0754523546940', '0754523546957',
--         '0754523546964', '0754523546971', '0754523546988', '0754523546995'
--     ]) AS barcode
-- ),
-- UsedBarcodes AS (
--     SELECT "BarcodeNumber" AS barcode
--     FROM public."ProductVariationsData"
--     WHERE "Deleted" = 'False'
-- )
-- SELECT ab.barcode AS "Available Barcodes"
-- FROM AllBarcodes ab
-- LEFT JOIN UsedBarcodes ub ON ab.barcode = ub.barcode
-- WHERE ub.barcode IS NULL
-- ORDER BY ab.barcode;