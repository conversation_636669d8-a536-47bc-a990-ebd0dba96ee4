@import '../../global';
@import '../admin';

.flashOrange {
    animation: flashOrangeAnimation 1s ease-in-out;
}

// Mobile message container
.mobileMessageContainer {
    display: none;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background-color: $offWhite;
    z-index: 1000;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    color: $primaryColor;
    padding: 20px;
}

// Media queries for responsive design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        display: none;
    }

    .mobileMessageContainer {
        display: flex;
    }
}

.mainContainer {
    display: flex;

    // Basic List
    .basicListContainer {
        width: 250px;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        scroll-margin: 10px;
        padding-bottom: 0;
        border: $adminDividerBorder;
        border-top: none;
        border-bottom: none;
        border-left: none;
        box-sizing: border-box;
        overflow-y: auto;

        .listTitle {
            margin-bottom: 5px;
            width: 70%;
            padding: 10px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            border-bottom: solid $smallBorderWidth $offBlack;
        }


        .basicListSearchBox {
            margin-top: 14px;
            min-height: 35px;
            width: 80%;
            padding-left: 8px;
            box-sizing: border-box;
            border-radius: $tinyBorderRadius;
            border: none;
            outline: solid $smallBorderWidth $offBlack;
            background-color: $offWhite;
        }
        .basicListItems {
            @include hideScrollBar;
            width: 100%;
            padding-top: 5px;
            padding-bottom: 100px;
            overflow-y: auto;
            .selectionTile {
                margin: 5px auto 0 auto;
                padding: 6px;
                width: 75%;
                text-align: center;
                border-radius: $tinyBorderRadius;
                cursor: pointer;
                user-select: none;
                &:hover { background-color: $greyHover; }
            }

            .selectionTile.selected {
                cursor: default !important;
            }
        }
    }

    // Main List
    .mainListContainer {
        @include hideScrollBar;
        height: 100vh;
        overflow-y: auto;
        flex: 8;
        padding: $bodyTopBottomPadding $bodyLeftRightPadding;
        box-sizing: border-box;

        .newIngredientButton {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        .ingredientsListContainer {
            margin-top: 20px;
            width: 100%;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }

        .ingredientContainer {
            @include adminFormContainerStyles;
            width: 200px;
            padding-top: 8px;
            padding-bottom: 22px;

            .ingredientTitle {
                padding-top: 5px;
                width: 100%;
                height: 30px;
                display: flex;
                align-items: center;
                margin-bottom: 14px;
                text-align: center;
                font-weight: bold;
                user-select: none;
                > div {
                    width: 100%;
                    cursor: pointer;
                    > div {
                        // Increase width of text container
                        width: calc(100% + 20px);
                        margin-left: -10px;
                    }
                }
                .editContainer {
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    .nameFieldOverride {
                        width: 180px;
                        margin-top: 6px;
                        margin-bottom: 5px;
                        .nameFieldInputOverride{
                            height: 36px !important;
                            input {
                                text-align: center;
                                padding-left: 0;
                            }
                        }
                    }
                }
            }

            .ingredientInputListContainer {
                width: 100%;
                display: grid;
                grid-template-columns: max-content min-content;
                justify-content: space-between;
                align-items: center;
                column-gap: 20px;
                row-gap: 6px;

                .nutValName {
                    font-size: $defaultFontSize;
                }

                .ingInputContainerOverride {
                    height: 36px !important;
                }
            }

            .labelDescriptionContainer {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                row-gap: 10px;
                margin-top: 12px;

                .labelDescriptionTitle {
                    font-size: $defaultFontSize;
                }

                .labelDescriptionInput {
                    width: 100%;
                    height: 70px;
                    outline: solid 2px $offBlack;
                    border-radius: $tinyBorderRadius;
                    box-sizing: border-box;

                    textarea {
                        width: 100%;
                        height: 100%;
                        padding: 4px 6px;
                        box-sizing: border-box;
                        outline: none !important;
                        border: none !important;
                        resize: none;
                    }
                }
            }

            .settingsContainer {
                width: max-content;
                margin: 14px auto -6px auto;
                > div { margin-top: 4px; }
            }

            .newIngredientSubmitButton {
                width: 100%;
                margin-top: 24px;
                display: flex;
                justify-content: center;
            }
        }

        .saveButton {
            position: fixed;
            top: 30px;
            right: 34px;
            .saveIconOverride {
                width: 25px;
                margin-left: -2px;
                margin-right: -2px;
            }
        }
    }
}


@keyframes flashOrangeAnimation {
    0%   {  background-color: $miscellaneousColor1; }
    100% {  background-color: $lightSecondaryColor; }
}