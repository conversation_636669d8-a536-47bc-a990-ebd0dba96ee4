import { sql } from "@vercel/postgres";
import { ProductVariationIngredientModel } from "@/app/models/ProductVariationIngredient";
import { JC_Utils } from "@/app/Utils";

export class ProductVariationIngredientBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll() {
        return (await sql<ProductVariationIngredientModel>`
            SELECT "variationIngredient"."Id",
                   "variationIngredient"."ProductVariationId",
                   "variationIngredient"."IngredientId",
                   "variationIngredient"."AmountGrams",
                   "variationIngredient"."ShowPercent",
                   "variationIngredient"."CreatedAt",
                   "variationIngredient"."ModifiedAt",
                   "variationIngredient"."Deleted",
                   "variation"."Name" "Ex_ProductVariationName",
                   "ingredient"."Name" "Ex_IngredientName"
            FROM public."ProductVariationIngredient" "variationIngredient"
            INNER JOIN public."ProductVariation" "variation" ON "variationIngredient"."ProductVariationId" = "variation"."Id"
            INNER JOIN public."Ingredient" "ingredient" ON "variationIngredient"."IngredientId" = "ingredient"."Id"
            WHERE "variationIngredient"."Deleted" = 'False'
              AND "variation"."Deleted" = 'False'
              AND "ingredient"."Deleted" = 'False'
            ORDER BY "ingredient"."Name"
        `).rows;
    }

    static async GetForProductVariation(productVariationId: string) {
        return (await sql<ProductVariationIngredientModel>`
            SELECT "variationIngredient"."Id",
                   "variationIngredient"."ProductVariationId",
                   "variationIngredient"."IngredientId",
                   "variationIngredient"."AmountGrams",
                   "variationIngredient"."ShowPercent",
                   "variationIngredient"."CreatedAt",
                   "variationIngredient"."ModifiedAt",
                   "variationIngredient"."Deleted",
                   "variation"."Name" "Ex_ProductVariationName",
                   "ingredient"."Name" "Ex_IngredientName"
            FROM public."ProductVariationIngredient" "variationIngredient"
            INNER JOIN public."ProductVariation" "variation" ON "variationIngredient"."ProductVariationId" = "variation"."Id"
            INNER JOIN public."Ingredient" "ingredient" ON "variationIngredient"."IngredientId" = "ingredient"."Id"
            WHERE "variationIngredient"."ProductVariationId" = ${productVariationId}
              AND "variationIngredient"."Deleted" = 'False'
              AND "variation"."Deleted" = 'False'
              AND "ingredient"."Deleted" = 'False'
            ORDER BY "ingredient"."Name"
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newItem: ProductVariationIngredientModel) {
        await sql`
            INSERT INTO public."ProductVariationIngredient"
            (
                "Id",
                "ProductVariationId",
                "IngredientId",
                "AmountGrams",
                "ShowPercent",
                "CreatedAt"
            )
            VALUES
            (
                ${newItem.Id},
                ${newItem.ProductVariationId},
                ${newItem.IngredientId},
                ${newItem.AmountGrams},
                ${newItem.ShowPercent},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(items: ProductVariationIngredientModel[]) {
        for (const item of items) {
            await this.Create(item);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(item: ProductVariationIngredientModel) {
        await sql`
            UPDATE public."ProductVariationIngredient"
            SET "AmountGrams"        = ${item.AmountGrams},
                "ShowPercent"        = ${item.ShowPercent},
                "ModifiedAt"         = ${new Date().toUTCString()},
                "Deleted"            = ${item.Deleted ?? false}
            WHERE "ProductVariationId" = ${item.ProductVariationId}
              AND "IngredientId" = ${item.IngredientId}
        `;
    }

    static async UpdateList(items: ProductVariationIngredientModel[]) {
        for (const item of items) {
            await this.Update(item);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."ProductVariationIngredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteForProductVariation(productVariationId: string) {
        await sql`
            UPDATE public."ProductVariationIngredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductVariationId" = ${productVariationId}
        `;
    }
}
