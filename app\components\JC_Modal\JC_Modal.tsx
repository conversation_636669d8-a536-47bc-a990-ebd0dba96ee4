"use client"

import styles from "./JC_Modal.module.scss";
import React from 'react';
import Image from "next/image";
import { JC_Utils } from "@/app/Utils";
import JC_Spinner from "../JC_Spinner/JC_Spinner";

export default function JC_Modal(_: Readonly<{

    overrideClass?: string;
    width?: string;
    children: React.ReactNode;
    title?: string;
    isOpen: boolean;
    onCancel: () => void;
    transparent?: boolean;
    isLoading?: boolean;

}>) {
    return _.isOpen
        ?
        (
            <React.Fragment>
                <div className={styles.blackOverlay} onClick={_.onCancel}/>
                <div style={_.width ? { width: _.width } : {}} className={`${styles.modalContainer} ${_.transparent ? styles.forceTransparent : ''} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>
                    {_.title && <div className={styles.title}>{_.title}</div>}
                    <Image
                        src={`/icons/Cross.webp`}
                        width={50}
                        height={50}
                        className={styles.cancelButton}
                        onClick={_.onCancel}
                        alt="Cancel"
                    />

                    {/* Loading Spinner */}
                    {_.isLoading && <JC_Spinner overrideClass={styles.loadingSpinner} />}

                    {/* Content - hidden when loading */}
                    {!_.isLoading &&
                    <div className={_.isLoading ? styles.modalContentHidden : ""}>
                        {_.children}
                    </div>}
                </div>
            </React.Fragment>
        )
        :
        null;
}
