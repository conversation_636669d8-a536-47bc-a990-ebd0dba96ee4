-- Drop Tables SQL Script for DbSchema.dbml
-- Tables are dropped in reverse order of creation to handle dependencies

-- Setup logging wrapper
DO $$
BEGIN
    RAISE NOTICE E'\n===== STARTING DATABASE SCHEMA CLEANUP =====\n';
    RAISE NOTICE 'Drop operation started at %', now();
END $$;

-- First, drop all constraints that might cause circular dependencies
DO $$
BEGIN
    RAISE NOTICE 'Dropping circular dependency constraints...';
END $$;
ALTER TABLE IF EXISTS public."ProductVariation" DROP CONSTRAINT IF EXISTS "FK_ProductVariation_DependentVariation";

-- Payment related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping payment related tables...';
END $$;
DROP TABLE IF EXISTS public."Payment";
DROP TABLE IF EXISTS public."PaymentStatus";

-- Order related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping order related tables...';
END $$;
DROP TABLE IF EXISTS public."OrderItem";
DROP TABLE IF EXISTS public."Order";
DROP TABLE IF EXISTS public."OrderStatus";

-- Review related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping review related tables...';
END $$;
DROP TABLE IF EXISTS public."UserProductReview";

-- Ingredient related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping ingredient related tables...';
END $$;
DROP TABLE IF EXISTS public."ProductVariationIngredient";
DROP TABLE IF EXISTS public."Ingredient";

-- Product variation related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping product variation related tables...';
END $$;
DROP TABLE IF EXISTS public."ProductVariationsData";
DROP TABLE IF EXISTS public."ProductVariation";
DROP TABLE IF EXISTS public."VariationCategory";

-- Product dietary attribute related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping product dietary attribute related tables...';
END $$;
DROP TABLE IF EXISTS public."ProductDietaryAttribute";
DROP TABLE IF EXISTS public."DietaryAttribute";

-- Product related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping product related tables...';
END $$;
DROP TABLE IF EXISTS public."Product";

-- Contact related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping contact related tables...';
END $$;
DROP TABLE IF EXISTS public."Contact";

-- Stockist related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping stockist related tables...';
END $$;
DROP TABLE IF EXISTS public."Stockist";

-- User related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping user related tables...';
END $$;
DROP TABLE IF EXISTS public."UserPersistedData";
DROP TABLE IF EXISTS public."Address";
DROP TABLE IF EXISTS public."User";

-- Settings related tables
DO $$
BEGIN
    RAISE NOTICE 'Dropping settings related tables...';
END $$;
DROP TABLE IF EXISTS public."GlobalSettings";

-- Verify all tables are dropped
DO $$
DECLARE
    table_count INTEGER;
    remaining_tables TEXT;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
BEGIN
    start_time := clock_timestamp();

    -- Check if any tables still exist
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    AND table_name IN (
        'User', 'Address', 'UserPersistedData', 'Stockist', 'Contact',
        'Product', 'VariationCategory', 'ProductVariation', 'ProductVariationsData',
        'Ingredient', 'ProductVariationIngredient', 'UserProductReview',
        'DietaryAttribute', 'ProductDietaryAttribute', 'Order', 'OrderItem',
        'OrderStatus', 'Payment', 'PaymentStatus', 'GlobalSettings'
    );

    -- Get names of any remaining tables
    IF table_count > 0 THEN
        SELECT string_agg(table_name, ', ') INTO remaining_tables
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        AND table_name IN (
            'User', 'Address', 'UserPersistedData', 'Stockist', 'Contact',
            'Product', 'VariationCategory', 'ProductVariation', 'ProductVariationsData',
            'Ingredient', 'ProductVariationIngredient', 'UserProductReview',
            'DietaryAttribute', 'ProductDietaryAttribute', 'Order', 'OrderItem',
            'OrderStatus', 'Payment', 'PaymentStatus', 'GlobalSettings'
        );
    END IF;

    end_time := clock_timestamp();
    duration := end_time - start_time;

    -- Display final results
    RAISE NOTICE E'\n===== DATABASE SCHEMA CLEANUP COMPLETED =====';
    RAISE NOTICE 'Cleanup completed at %', now();
    RAISE NOTICE 'Operation duration: %', duration;

    IF table_count > 0 THEN
        RAISE WARNING 'Warning: % tables from the schema still exist after drop script execution', table_count;
        RAISE WARNING 'Remaining tables: %', remaining_tables;
    ELSE
        RAISE NOTICE 'Success: All tables from the schema have been dropped';
    END IF;

    RAISE NOTICE '=============================================\n';
END $$;
