import { NextResponse } from 'next/server';
import { unstable_noStore } from 'next/cache';
import { VariationCategoryBusiness } from '../business';

export async function GET() {
    try {
        unstable_noStore();
        const result = await VariationCategoryBusiness.GetAll();
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
