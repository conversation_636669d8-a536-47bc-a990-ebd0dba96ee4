SELECT "variationIngredient"."Id"
      ,"variationIngredient"."ProductVariationId"
      ,"variation"."Name" "__ProductVariation"
      ,"category"."Name" "__VariationCategory"
      ,"product"."Name" "__Product"
      ,"variationIngredient"."IngredientId"
      ,"ingredient"."Name" "__Ingredient"
      ,"variationIngredient"."AmountGrams"
      ,"variationIngredient"."ShowPercent"
      ,"variationIngredient"."CreatedAt"
      ,"variationIngredient"."ModifiedAt"
      ,"variationIngredient"."Deleted"
FROM public."ProductVariationIngredient" "variationIngredient"
INNER JOIN public."ProductVariation" "variation" ON "variationIngredient"."ProductVariationId" = "variation"."Id"
INNER JOIN public."VariationCategory" "category" ON "variation"."VariationCategoryCode" = "category"."Code"
INNER JOIN public."Ingredient" "ingredient" ON "variationIngredient"."IngredientId" = "ingredient"."Id"
INNER JOIN public."Product" "product" ON "variation"."ProductId" = "product"."Id"
WHERE 1=1
    AND "variationIngredient"."Deleted" = 'False'
    AND "variation"."Deleted" = 'False'
    AND "product"."Deleted" = 'False'
    -- AND "variationIngredient"."ProductVariationId" = '11c4f7de-7638-481b-b02b-70bbf8e6bce1'
ORDER BY "product"."Name",
         "variation"."Name",
         "ingredient"."Name";