@import '../global';

.mainContainer {
    @include mainPageStyles;
    position: relative;
    min-height: 600px;

    .bodyContainer {
        display: flex;
        justify-content: space-evenly;
        align-items: flex-start;

        .charlotte {
            $charlotteMargin: calc(-100px + 10%);
            margin-left: $charlotteMargin;
            margin-right: calc(0 - $charlotteMargin);
            width: auto;
            height: 500px;
            max-height: 70%;
            opacity: $charlotteOpacity;
        }
    }
    
}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .bodyContainer {
        flex-direction: column;
        align-items: center !important;
        .charlotte {
            order: 2;
            margin: 50px 0 0 0 !important;
        }
    }
}