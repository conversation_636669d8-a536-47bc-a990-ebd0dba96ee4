@import '../../global';

.spinnerContainer {
    width: 100%;
    height: 100%;
    position: relative;
    user-select: none;

    &.emptyPage {
        height: $emptyPageHeight;
    }

    &.small {
        transform: scale(0.4);
    }
}

.spinnerImage {
    width: 100px;
    height: auto;
    position: absolute;
    left: 50%; top: 50%;
    animation: pulseInAnimation 0.4s          ease-out,
               spinAnimation    1.3s infinite linear,
               pulsingAnimation 0.9s infinite ease-in-out;
}

/* Pulse In */

@keyframes pulseInAnimation {
    0%   {  margin-left: -40px; scale: 0.4; opacity: 0.4; }
    100% {  margin-left: 0;     scale: 1;   opacity: 1;   }
}

/* Spin */

@keyframes spinAnimation {
    0%   { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
} 

/* Pulse */

@keyframes pulsingAnimation {
    0%   {  width: 100px; }
    60%  {  width: 115px; }
    100% {  width: 100px; }
}

/* Pulse Out */

@keyframes pulseOutAnimation {
    0%   {  left: 50%; scale: 1; opacity: 1;   }
    100% {  left: 42%; scale: 0; opacity: 0.4; border: solid 1px orange; }
}