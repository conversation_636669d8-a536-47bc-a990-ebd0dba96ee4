import { NextRequest, NextResponse } from "next/server";
import { ItemSettingsModel } from "@/app/models/ItemSettings";
import { ItemSettingsBusiness } from "./business";

export const dynamic = 'force-dynamic';

// - --- - //
// - GET - //
// - --- - //

export async function GET(request: NextRequest) {
    try {
        const productId = new URL(request.url).searchParams.get("productId");
        const productVariationId = new URL(request.url).searchParams.get("productVariationId");

        // If both are provided, prioritize productVariationId
        if (productVariationId) {
            const items = await ItemSettingsBusiness.GetForProductVariation(productVariationId);
            return NextResponse.json(items);
        } else if (productId) {
            const items = await ItemSettingsBusiness.GetForProduct(productId);
            return NextResponse.json(items);
        } else {
            const items = await ItemSettingsBusiness.GetAll();
            return NextResponse.json(items);
        }
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const newItem: ItemSettingsModel = new ItemSettingsModel(await request.json());
        await ItemSettingsBusiness.Create(newItem);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - UPDATE - //
// - ------ - //

export async function POST(request: NextRequest) {
    try {
        const item: ItemSettingsModel = new ItemSettingsModel(await request.json());
        await ItemSettingsBusiness.Update(item);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - DELETE - //
// - ------ - //

export async function DELETE(request: NextRequest) {
    try {
        const id: string = new URL(request.url).searchParams.get("id")!;
        await ItemSettingsBusiness.Delete(id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
