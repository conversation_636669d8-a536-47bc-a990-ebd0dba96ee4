@import '../../global';
@import '../admin';

// Force hidden class for header/footer
.forceHidden {
    display: none !important;
}

.pageWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    width: 100%;
}

.mainContainer {
    width: 100%;
    max-width: 100%;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .pageTitle {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 20px;
        color: $primaryColor;
    }

    .toggleButtonsContainer {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }

    .contentContainer {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 30px;
    }

    .previewSection {
        display: flex;
        justify-content: center;
        gap: 60px;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .labelPreviewContainer {
            display: flex;
            flex-direction: column;
            align-items: center;

            .labelPreviewTitle {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 15px;
                color: $primaryColor;
            }

            .labelPreview {
                display: flex;
                flex-direction: column;
                align-items: center;

                .paddingTop, .paddingBottom {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 60px;
                    width: 100%;
                    margin: 10px 0;
                }

                .paddingHorizontal {
                    display: flex;
                    align-items: center;

                    .paddingLeft {
                        display: flex;
                        justify-content: flex-end;
                        align-items: center;
                        width: 120px;
                        height: 100%;
                        margin-right: 50px; /* Increased from 30px to 50px */
                    }

                    .paddingRight {
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        width: 120px;
                        height: 100%;
                        margin-left: 20px; /* Added 20px margin to the left */
                    }

                    .labelPage {
                        padding-right: 20px;
                        padding-bottom: 25px;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        border-radius: 5px;
                        background-color: white;
                        overflow: auto;
                        position: relative;
                    }
                }
            }
        }
    }

    .settingsContainersRow {
        display: flex;
        flex-direction: row;
        gap: 20px;
        justify-content: center;
        width: 100%;
        margin-bottom: 20px;
    }

    .settingsContainer {
        @include adminFormContainerStyles;
        width: 450px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .settingsGroup, .backSettingsGroup {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .backSettingsGroup {
            margin-top: 20px;
        }

        .groupTitle {
            font-size: 20px;
            font-weight: bold;
            color: $offBlack;
            border-bottom: solid $smallBorderWidth $offBlack;
            padding-bottom: 5px;
            margin-bottom: 5px;
            text-align: center;
        }

        .settingsRow {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            justify-items: center;
        }

        .settingItem {
            display: flex;
            flex-direction: column;
            gap: 5px;
            width: 100%;

            .settingLabel {
                font-weight: bold;
                margin-bottom: 5px;
                text-align: center;
            }

            .settingField {
                width: 100%;
            }
        }

        .settingItemCentered {
            display: flex;
            flex-direction: column;
            gap: 5px;
            width: 100%;
            max-width: 200px;
            margin: 0 auto;
            grid-column: 1 / span 3;

            .settingLabel {
                font-weight: bold;
                margin-bottom: 5px;
                text-align: center;
            }

            .settingField {
                width: 100%;
            }
        }
    }

    .errorMessage {
        font-size: 16px;
        color: $errorColor;
        text-align: center;
        margin: 40px 0;
        padding: 20px;
        background-color: rgba(255, 0, 0, 0.05);
        border-radius: 5px;
        width: 100%;
        max-width: 600px;
    }

    // Save button styling (matches admin products page)
    .saveButton {
        position: fixed;
        top: 30px;
        right: 34px;
    }
}
