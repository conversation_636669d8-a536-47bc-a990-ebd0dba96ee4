import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { OrderModel } from "@/app/models/Order";
import { _OrderValuesAtSubmissionModel } from "@/app/models/_OrderValuesAtSubmission";
import { UserModel } from "@/app/models/User";
import { CookieKeyEnum } from "@/app/enums/CookieKey";
import { OrderStatusEnum } from "@/app/enums/OrderStatus";
import { OrderBusiness } from "../../order/business";
import { ProductBusiness } from "../../product/business";
import { UserBusiness } from "../../user/business";
import { EmailBusiness } from "../../email/business";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
    apiVersion: "2024-06-20"
});

const endpointSecret = process.env.PAYMENT_INTENT_SUCCESS_WEBHOOK_SECRET as string;

export async function POST(request: NextRequest) {

    try {

        const rawBody = await request.text();
        const signature = request.headers.get("stripe-signature")!;

        const stripeJson = JSON.parse(rawBody);

        let event:Stripe.Event = stripe.webhooks.constructEvent(rawBody, signature, endpointSecret);

        switch (event.type) {

            case "payment_intent.succeeded":

                // Delete paymentIntentId in cookies
                cookies().delete(CookieKeyEnum.JC_PaymentIntentId);

                // Set current bag to "Submitted"
                const orderId = stripeJson.data.object.metadata.orderId;
                let bag:OrderModel = await OrderBusiness.Get(orderId);
                let user:UserModel|null = bag.UserId ? await UserBusiness.Get(bag.UserId) : null;
                bag.OrderStatusCode = OrderStatusEnum.Submitted;
                bag.SubmittedDate = new Date();
                let allProducts = await ProductBusiness.GetList();
                let valuesAtSubmission:_OrderValuesAtSubmissionModel[] = bag.Ex_OrderItems.map(item => {
                    let product = allProducts.find(p => p.Id == item.ProductId)!;
                    product.Ex_Variations = [];
                    product.Ex_ProductVariationsData = [];
                    return {
                        Product: product,
                        Variations: product?.Ex_Variations.filter((v: { Id: string; }) => item.ProductVariationIds.includes(v.Id))
                    };
                });
                bag.ValuesAtSubmissionJson = JSON.stringify(valuesAtSubmission);
                await OrderBusiness.Update(bag);

                let userEmail = stripeJson.data.object.metadata.email;
                let fullName = stripeJson.data.object.metadata.fullName;

                // Send confirmation/invoice email to CK
                await EmailBusiness.SendOrderSubmittedCkEmail({
                    userId: user ? user.Id : "-",
                    fullName: fullName,
                    email: userEmail,
                    items: bag.Ex_OrderItems.map(item => ({
                        productName: item.Ex_ProductName,
                        variationsString: item.Ex_VariationNames?.join(" | "),
                        imagePath: ""
                    })),
                    amount: stripeJson.data.object.amount / 100
                });

                // Send confirmation/invoice email to User
                await EmailBusiness.SendOrderSubmittedUserEmail({
                    userId: user ? user.Id : "-",
                    fullName: fullName,
                    email: userEmail,
                    items: bag.Ex_OrderItems.map(item => ({
                        productName: item.Ex_ProductName,
                        variationsString: item.Ex_VariationNames?.join(" | "),
                        imagePath: ""
                    })),
                    amount: stripeJson.data.object.amount / 100
                });

        }

        return NextResponse.json({ status: 200 });

    } catch (error:any) {
        console.log(error);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

}