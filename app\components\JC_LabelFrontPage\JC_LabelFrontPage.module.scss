@import '../../global';

// Print Settings
@page {
    margin: 0.3cm;
}

// Page
.mainContainer {
    // Padding is now set via inline style from global settings
    width: 771px;
    height: 1090px;
    display: grid;
    grid-template-columns: max-content max-content;
    justify-content: space-between;
    align-content: space-between;
    align-items: center;
    background-color: white;
    box-sizing: border-box;
}

.small {
    // Padding is now set via inline style from global settings
}

.twoByFive {
    // Padding is now set via inline style from global settings
}

// Force hidden class for header/footer
.forceHidden {
    display: none !important;
}

// Force white background
.forceWhiteBackground {
    background-color: white !important;
}
