"use client"

import styles from "./page.module.scss";
import React from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import J<PERSON>_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Tabs from "../components/JC_Tabs/JC_Tabs";
import JC_BagView from "../components/JC_BagView/JC_BagView";
import JC_ImageGridView from "../components/JC_ImageGridView/JC_ImageGridView";
import JC_ModalConfirmation from "../components/JC_ModalConfirmation/JC_ModalConfirmation";
import JC_Checkbox from "../components/JC_Checkbox/JC_Checkbox";
import { JC_GetList } from "../services/JC_GetList";
import { JC_Post } from "../services/JC_Post";
import { AddOrderItemListToBag } from "../services/Bag";
import { signOut, useSession } from "next-auth/react";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { JC_ConfirmationModalUsageModel } from "../models/ComponentModels/JC_ConfirmationModalUsage";
import { UserModel } from "../models/User";
import { OrderModel } from "../models/Order";
import { GlobalSettingsModel } from "../models/GlobalSettings";
import { ProductModel } from "../models/Product";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

import { JC_Utils, JC_Utils_Dates } from "../Utils";


export default function Page_Account() {

    // - STATE - //

    const session = useSession();
    const router = useRouter();
    const [products, setProducts] = useState<ProductModel[]>([]);
    // Loading
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const [logoutLoading, setLogoutLoading] = useState<boolean>(false);
    // Account Details
    const [firstName, setFirstName] = useState<string>(session.data!.user.FirstName);
    const [lastName, setLastName] = useState<string>(session.data!.user.LastName);
    const [phone, setPhone] = useState<string>(session.data!.user.Phone ?? "");
    const [company, setCompany] = useState<string>(session.data!.user.CompanyName ?? "");
    const [emailPromotionsChecked, setEmailPromotionsChecked] = useState<boolean>(session.data!.user.IsEmailSubscribed);
    // Order History
    const [previousOrders, setPreviousOrders] = useState<OrderModel[]>();
    // Confirmation
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>();
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);


    // - INITIALISE - //

    useEffect(() => {
        JC_GetList<ProductModel>(ProductModel.apiRoute, {}, ProductModel).then(list => setProducts(list));
        // IF just updated details, show success
        if (localStorage.getItem("showUpdatedAccountDetailsSuccess") == "1") {
            JC_Utils.showToastSuccess("Your details have been updated!")
            localStorage.setItem("showUpdatedAccountDetailsSuccess", "0");
        }
        // Get previous orders
        JC_GetList<OrderModel>(OrderModel.apiRoute_getListUserPreviousOrders, { userId: session.data!.user.Id }, OrderModel).then(list => setPreviousOrders(list));
    }, []);


    // - HANDLES - //

    // Account Details
    async function saveAccountDetails() {
        setSaveLoading(true);
        let newUser:UserModel = {
            ...session.data!.user,
            FirstName: firstName,
            LastName: lastName,
            Phone: !JC_Utils.stringNullOrEmpty(phone) ? phone : undefined,
            CompanyName: !JC_Utils.stringNullOrEmpty(company) ? company : undefined,
            IsEmailSubscribed: emailPromotionsChecked
        };
        // Update db User
        await JC_Post<UserModel>(UserModel.apiRoute, newUser);
        // Trigger "jwt()" callback to refresh User from db
        await JC_Post<GlobalSettingsModel>(GlobalSettingsModel.apiRoute, {
            Code: "ForceRefreshAuthToken",
            Description: "",
            Value: "1"
        });
        // Update the session with new data (need this plus the update in "jwt()" callback to get update showing properly)
        const newSession = session.data;
        newSession!.user = newUser;
        await session.update(newSession);
        // Show success toast after refresh
        localStorage.setItem("showUpdatedAccountDetailsSuccess", "1");
        // Refresh
        setTimeout(() => window.location.reload(), 100);
    }

    // Reset Password
    async function resetPassword() {
        setConfirmationModalData({
            width: "380px",
            title: "Reset Password Email",
            text: `We will send you a "Reset Password" link to your email. Continue?`,
            submitButtons: [{
                text: "Send Email",
                onSubmit: async () => {
                    setConfirmationLoading(true);
                    // Generate token and trigger email
                    await JC_Post("user/triggerResetPasswordToken", { email: session.data!.user.Email });
                    setConfirmationLoading(false);
                    setConfirmationModalData(null);
                    JC_Utils.showToastSuccess("A password reset link has been sent to your email!");
                }
            }]
        });
    }

    // Expand order history item
    function expandOrder(order:OrderModel) {
        previousOrders?.forEach(o => o.Id != order.Id ? o.UI_Expanded = false : null);
        order.UI_Expanded = !order.UI_Expanded;
        setPreviousOrders(JC_Utils.parseStringify(previousOrders));
    }

    // Order Again
    async function orderAgain(order:OrderModel) {
        let numItems = order.getTotalItems();
        setConfirmationModalData({
            width: "380px",
            title: "Order Again",
            text: `Would you like to add ${numItems == 1 ? "this item" : `these ${numItems} items`} from your order on ${(new Date(order.SubmittedDate!)).toLocaleDateString()} to your bag?`,
            submitButtons: [{
                text: "Add to Bag",
                onSubmit: async () => {
                    setConfirmationLoading(true);
                    await AddOrderItemListToBag(session.data, order.Ex_OrderItems);
                    localStorage.setItem(LocalStorageKeyEnum.JC_ShowBagItemsAdded, "1");
                    router.push("checkout")
                }
            }]
        });
    }


    // - BUILD - //

    // Account Details
    function _buildAccountDetails() {
        return <div className={styles.accountDetailsContainer}>
            <JC_Form
                onSubmit={saveAccountDetails}
                isDisabled={confirmationLoading || logoutLoading}
                isLoading={saveLoading}
                fields={[
                    // Email
                    {
                        ...D_FieldModel_Email(),
                        overrideClass: styles.fieldOverride,
                        value: session.data!.user.Email,
                        readOnly: true
                    },
                    // First Name
                    {
                        ...D_FieldModel_FirstName(),
                        overrideClass: styles.fieldOverride,
                        value: firstName,
                        onChange: (newValue) => setFirstName(newValue),
                    },
                    // Last Name
                    {
                        ...D_FieldModel_LastName(),
                        overrideClass: styles.fieldOverride,
                        value: lastName,
                        onChange: (newValue) => setLastName(newValue),
                    },
                    // Phone
                    {
                        ...D_FieldModel_Phone(),
                        overrideClass: styles.fieldOverride,
                        value: phone,
                        onChange: (newValue) => setPhone(newValue)
                    },
                    // Company Name
                    {
                        inputId: "company-name-input",
                        type: FieldTypeEnum.Text,
                        label: "Company (optional)",
                        iconName: "User",
                        value: company,
                        onChange: (newValue) => setCompany(newValue)
                    },
                    // Email Promotions
                    {
                        overrideClass: styles.fieldOverride,
                        inputId: "email-promotions-checkbox",
                        type: FieldTypeEnum.Text,
                        customNode: <JC_Checkbox key="details-&-payment-title" label="Email Promotions" checked={emailPromotionsChecked} onChange={() => setEmailPromotionsChecked(!emailPromotionsChecked)} />
                    }
                ]}
            />

            <hr style={{ width: "80%", borderTopWidth: "2px", borderColor: "grey" }}/>

            {/* Reset Password */}
            <JC_Button
                text="Reset Password"
                onClick={resetPassword}
                isDisabled={confirmationLoading || saveLoading || logoutLoading}
            />

            {/* Logout */}
            <JC_Button
                text="Logout"
                onClick={() => { setLogoutLoading(true); signOut({ callbackUrl: "/loginRegister" }); }}
                isDisabled={confirmationLoading || saveLoading}
                isLoading={logoutLoading}
            />
        </div>;
    }

    // Order History
    function _buildOrderHistory() {
        return <div className={styles.orderHistoryContainer}>

            {previousOrders!.length == 0 &&
            <div className={styles.noOrdersText}>No orders yet!</div>}

            {previousOrders!.length > 0 &&
            previousOrders!.map(order =>
                <div key={order.Id} className={styles.orderContainer}>

                    {/* Date */}
                    <div className={styles.orderDate}>{order.SubmittedDate ? JC_Utils_Dates.formattedDateString(order.SubmittedDate!) : "???"}</div>

                    {/* Header */}
                    <div className={styles.orderHeader} onClick={() => expandOrder(order)}>
                        {/* Image grid view */}
                        <JC_ImageGridView
                            overrideClass={styles.imageGridViewOverride}
                            imagePaths={order.Ex_OrderItems.sort((a,b) => a.CreatedAt > b.CreatedAt ? 1 : -1).slice(0, 4).map(item => {
                                let product = products.find(p => p.Id == item.ProductId);

                                return !JC_Utils.stringNullOrEmpty(product?.ImageFileName) ? `/products/products/${product!.ImageFileName}.webp` : ''
                            })}
                        />
                        {/* Num Items */}
                        <div className={styles.headerBodyText}>{`${order.getTotalItems()} item${order.getTotalItems() == 1 ? "" : "s"}`}</div>
                        {/* Total Price */}
                        <div className={styles.headerBodyText}>{`$${order.getTotalPrice(products).toFixed(2)}`}</div>
                        {/* Button */}
                        <Image
                            className={styles.chevronIcon}
                            src="/icons/Chevron.webp"
                            style={order.UI_Expanded ? {rotate: "180deg", top: "20%"} : {}}
                            width={0}
                            height={0}
                            alt="Bag"
                            unoptimized
                        />
                        <Image
                            className={styles.chevronIconHover}
                            src="/icons/ChevronSecondary.webp"
                            style={order.UI_Expanded ? {rotate: "180deg", top: "20%"} : {}}
                            width={0}
                            height={0}
                            alt="Bag"
                            unoptimized
                        />
                    </div>

                    {/* Expanded */}
                    {order.UI_Expanded &&
                    <div className={styles.orderExpanded}>
                        <JC_Button text="Order Again" isSecondary onClick={() => orderAgain(order)} />
                        <JC_BagView hideFooter readOnly bag={order} />
                    </div>}

                </div>
            )}

        </div>;
    }


    // - Main - //

    return !previousOrders || products.length == 0
        ? (<JC_Spinner isPageBody />)
        : (
            <React.Fragment>

                {/* Big Screens */}
                <div className={`${styles.mainContainer} ${styles.mainBig}`}>
                    <JC_Title title="Account Details" />
                    <JC_Title title="Order History" />
                    {_buildAccountDetails()}
                    {_buildOrderHistory()}
                </div>

                {/* Small Screens */}
                <div className={`${styles.mainContainer} ${styles.mainSmall}`}>
                    <JC_Tabs
                        tabs={[{
                            title: "Account Details",
                            body: _buildAccountDetails()
                        },{
                            title: "Order History",
                            body: _buildOrderHistory()
                        }]}
                    />
                </div>

                {/* Confirmation */}
                {confirmationModalData &&
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />}


            </React.Fragment>
        );
}
