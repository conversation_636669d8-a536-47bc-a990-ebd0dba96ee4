import { JC_Utils } from "../Utils";

const resetIntervalMins = 30;

export async function JC_Get<T>(routeName:string, params:any, mapper?: new (init?: Partial<T>) => T) : Promise<T> {

    const response = await fetch(`/api/${routeName}?${new URLSearchParams(params)}`);
    if (!response.ok) { throw new Error(`Failed to fetch ${JC_Utils.routeNameToDescription(routeName)}.`); }
    let result = (await response.json()).result;
    return mapper != null ? new mapper(result) : result;
}