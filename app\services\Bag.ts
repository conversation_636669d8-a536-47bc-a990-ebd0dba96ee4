import { JC_Get } from "./JC_Get";
import { JC_Put } from "./JC_Put";
import { JC_Post } from "./JC_Post";
import { Session } from "next-auth";
import { OrderModel } from "../models/Order";
import { OrderItemModel } from "../models/OrderItem";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { OrderStatusEnum } from "../enums/OrderStatus";
import { JC_Utils } from "../Utils";


// - --- - //
// - GET - //
// - --- - //

// Get bag
export async function GetBag(sessionData:Session|null) : Promise<OrderModel> {
    
    // IF not logged in
    if (sessionData?.user == null) {
        // IF don't have OrderId in localstorage yet, create new Order
        let bagOrderId = localStorage.getItem(LocalStorageKeyEnum.JC_BagId) as string;
        if (JC_Utils.stringNullOrEmpty(bagOrderId)) {
            let bag:OrderModel = new OrderModel();
            localStorage.setItem(LocalStorageKeyEnum.JC_BagId, bag.Id);
            await JC_Put("order", bag);
            return bag;
        }
        // ELSE return bag
        else {
            let bag = await JC_Get<OrderModel>(OrderModel.apiRoute, { orderId: bagOrderId }, OrderModel);
            // IF bag is not in "Bag" state, ignore this and create a new bag
            if (bag.OrderStatusCode != OrderStatusEnum.Bag) {
                bag = new OrderModel();
                localStorage.setItem(LocalStorageKeyEnum.JC_BagId, bag.Id);
                await JC_Put("order", bag);
            }
            return bag;
        }
    }
    // ELSE IF logged in
    else {
        // Get bag by UserId and Status
        let bag:OrderModel = await JC_Get<OrderModel>(OrderModel.apiRoute_getByUserAndStatus, { userId: sessionData.user.Id, orderStatusCode: OrderStatusEnum.Bag }, OrderModel);
        if (!bag) {
            bag = new OrderModel({ UserId: sessionData.user.Id });
            await JC_Put("order", bag);
        }
        if (!bag.Ex_OrderItems) { bag.Ex_OrderItems = []; }
        return bag;
    }

}


// - ------ - //
// - UPDATE - //
// - ------ - //

// Add to bag
export async function AddToBag(sessionData:Session|null, productId:string, variationIds:string[], quantity:number) {
    let bag = await GetBag(sessionData);
    let existingItem = bag.Ex_OrderItems?.find(i => i.ProductId == productId && JC_Utils.arraysEqual(i.ProductVariationIds, variationIds));
    let newItem:OrderItemModel = new OrderItemModel({
        OrderId: bag.Id,
        ProductId: productId,
        ProductVariationIds: variationIds,
        Quantity: quantity
    });
    if (existingItem) {
        existingItem.Quantity += quantity;
        await JC_Post("orderItem", existingItem);
    } else {
        await JC_Put("orderItem", newItem, { userId: sessionData?.user.Id });
    }
}

// Increment Quantity
export async function IncrementBagItemQuantity(sessionData:Session|null, itemId:string) {
    return await SetQuantity(sessionData, itemId, +1);
}

// Decrement Quantity
export async function DecrementBagItemQuantity(sessionData:Session|null, itemId:string) {
    return await SetQuantity(sessionData, itemId, -1);
}

// Remove from bag
export async function RemoveFromBag(sessionData:Session|null, itemId:string) {
    let bag = await GetBag(sessionData);
    let existingItem = bag.Ex_OrderItems?.find(i => i.Id == itemId)!;
    existingItem.Deleted = true;
    await JC_Post("orderItem", existingItem);
    bag.Ex_OrderItems = bag.Ex_OrderItems.filter(i => i.Id != existingItem.Id);
    return bag;
}

// Attach the current Bag to a User (used when register)
export async function SetUserIdOnBag(sessionData:Session|null, userId:string) {
    let bag = await GetBag(sessionData);
    bag.UserId = userId;
    await JC_Post("order", bag);
    return bag;
}

// Add existing Order Items to current bag
export async function AddOrderItemListToBag(sessionData:Session|null, orderItems:OrderItemModel[]) {
    let bag = await GetBag(sessionData);
    let newList:OrderItemModel[] = [];
    for (let i = 0; i < orderItems.length; i++) {
        let item = orderItems[i];
        // IF exists in bag already, set new quantity
        let existingItem = bag.Ex_OrderItems?.find(i => i.ProductId == item.ProductId && JC_Utils.arraysEqual(i.ProductVariationIds, item.ProductVariationIds));
        if (existingItem) {
            await SetQuantity(sessionData, existingItem.Id, item.Quantity);
        }
        // ELSE add as new item
        else {
            let createdAtDate = new Date();
            createdAtDate.setSeconds(createdAtDate.getSeconds() + i); // To make sure "CreatedAt" is different for every OrderItem
            newList.push(new OrderItemModel({ 
                ...item,
                Id: JC_Utils.generateGuid(),
                OrderId: bag.Id,
                CreatedAt: createdAtDate
            }));
        }
    }
    if (newList.length > 0) {
        await JC_Post("orderItem/createOrderItemList", { orderItemList: newList });
    }
    return bag;
}


// Replace User's current bag with given bag
export async function ReplaceCurrentBag(sessionData:Session|null, fromOrder:OrderModel) {
    let bag = await GetBag(sessionData);
    let newList:OrderItemModel[] = [];
    for (let i = 0; i < fromOrder.Ex_OrderItems.length; i++) {
        let item = fromOrder.Ex_OrderItems[i];
        let createdAtDate = new Date();
        createdAtDate.setSeconds(createdAtDate.getSeconds() + i); // To make sure "CreatedAt" is different for every OrderItem
        newList.push(new OrderItemModel({
            ...item,
            Id: JC_Utils.generateGuid(),
            OrderId: bag.Id,
            CreatedAt: createdAtDate
        }));
    }
    await JC_Post("order/replaceOrderItems", { orderId: bag.Id, newList: newList });
}



// - ------- - //
// - PRIVATE - //
// - ------- - //

async function SetQuantity(sessionData:Session|null, itemId:string, change:number) {
    let bag = await GetBag(sessionData);
    let existingItem = bag.Ex_OrderItems!.find(i => i.Id == itemId)!;
    existingItem.Quantity += change;
    await JC_Post("orderItem", existingItem);
    return bag;
}