import { sql } from "@vercel/postgres";
import { ItemIngredientModel } from "@/app/models/ItemIngredient";

export class ItemIngredientBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetAll() {
        return (await sql<ItemIngredientModel>`
            SELECT "itemIng"."Id",
                   "itemIng"."ProductId",
                   "itemIng"."ProductVariationId",
                   "itemIng"."IngredientId",
                   "itemIng"."AmountGrams",
                   "itemIng"."ShowPercent",
                   "itemIng"."CreatedAt",
                   "itemIng"."ModifiedAt",
                   "itemIng"."Deleted",
                   "product"."Name" "Ex_ProductName",
                   "variation"."Name" "Ex_ProductVariationName",
                   json_build_object(
                       'Id', "ingredient"."Id",
                       'Name', "ingredient"."Name",
                       'LabelDescription', "ingredient"."LabelDescription",
                       'IsAllergen', "ingredient"."IsAllergen",
                       'IsOrganic', "ingredient"."IsOrganic",
                       'IsNotVegan', "ingredient"."IsNotVegan",
                       'HideOnLabel', "ingredient"."HideOnLabel",
                       'IsDemo', "ingredient"."IsDemo",
                       'Kilojoules', "ingredient"."Kilojoules",
                       'Protein', "ingredient"."Protein",
                       'FatTotal', "ingredient"."FatTotal",
                       'FatSaturated', "ingredient"."FatSaturated",
                       'Carbohydrate', "ingredient"."Carbohydrate",
                       'Sugars', "ingredient"."Sugars",
                       'Fiber', "ingredient"."Fiber",
                       '_NetCarbs', "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                       'Sodium', "ingredient"."Sodium",
                       'PercAus', "ingredient"."PercAus",
                       'CostPer100g', "ingredient"."CostPer100g"
                   ) "Ex_Ingredient"
            FROM public."ItemIngredient" "itemIng"
            LEFT JOIN public."Product" "product" ON "itemIng"."ProductId" = "product"."Id"
            LEFT JOIN public."ProductVariation" "variation" ON "itemIng"."ProductVariationId" = "variation"."Id"
            INNER JOIN public."Ingredient" "ingredient" ON "itemIng"."IngredientId" = "ingredient"."Id"
            WHERE "itemIng"."Deleted" = 'False'
              AND "ingredient"."Deleted" = 'False'
              AND ("product"."Deleted" = 'False' OR "product"."Deleted" IS NULL)
              AND ("variation"."Deleted" = 'False' OR "variation"."Deleted" IS NULL)
            ORDER BY "ingredient"."Name"
        `).rows;
    }

    static async GetForProduct(productId: string) {
        return (await sql<ItemIngredientModel>`
            SELECT "itemIng"."Id",
                   "itemIng"."ProductId",
                   "itemIng"."ProductVariationId",
                   "itemIng"."IngredientId",
                   "itemIng"."AmountGrams",
                   "itemIng"."ShowPercent",
                   "itemIng"."CreatedAt",
                   "itemIng"."ModifiedAt",
                   "itemIng"."Deleted",
                   "product"."Name" "Ex_ProductName",
                   "variation"."Name" "Ex_ProductVariationName",
                   json_build_object(
                       'Id', "ingredient"."Id",
                       'Name', "ingredient"."Name",
                       'LabelDescription', "ingredient"."LabelDescription",
                       'IsAllergen', "ingredient"."IsAllergen",
                       'IsOrganic', "ingredient"."IsOrganic",
                       'IsNotVegan', "ingredient"."IsNotVegan",
                       'HideOnLabel', "ingredient"."HideOnLabel",
                       'IsDemo', "ingredient"."IsDemo",
                       'Kilojoules', "ingredient"."Kilojoules",
                       'Protein', "ingredient"."Protein",
                       'FatTotal', "ingredient"."FatTotal",
                       'FatSaturated', "ingredient"."FatSaturated",
                       'Carbohydrate', "ingredient"."Carbohydrate",
                       'Sugars', "ingredient"."Sugars",
                       'Fiber', "ingredient"."Fiber",
                       '_NetCarbs', "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                       'Sodium', "ingredient"."Sodium",
                       'PercAus', "ingredient"."PercAus",
                       'CostPer100g', "ingredient"."CostPer100g"
                   ) "Ex_Ingredient"
            FROM public."ItemIngredient" "itemIng"
            LEFT JOIN public."Product" "product" ON "itemIng"."ProductId" = "product"."Id"
            LEFT JOIN public."ProductVariation" "variation" ON "itemIng"."ProductVariationId" = "variation"."Id"
            INNER JOIN public."Ingredient" "ingredient" ON "itemIng"."IngredientId" = "ingredient"."Id"
            WHERE "itemIng"."ProductId" = ${productId}
              AND "itemIng"."ProductVariationId" IS NULL
              AND "itemIng"."Deleted" = 'False'
              AND "product"."Deleted" = 'False'
              AND "ingredient"."Deleted" = 'False'
            ORDER BY "ingredient"."Name"
        `).rows;
    }

    static async GetForProductVariation(productVariationId: string) {
        return (await sql<ItemIngredientModel>`
            SELECT "itemIng"."Id",
                   "itemIng"."ProductId",
                   "itemIng"."ProductVariationId",
                   "itemIng"."IngredientId",
                   "itemIng"."AmountGrams",
                   "itemIng"."ShowPercent",
                   "itemIng"."CreatedAt",
                   "itemIng"."ModifiedAt",
                   "itemIng"."Deleted",
                   "product"."Name" "Ex_ProductName",
                   "variation"."Name" "Ex_ProductVariationName",
                   json_build_object(
                       'Id', "ingredient"."Id",
                       'Name', "ingredient"."Name",
                       'LabelDescription', "ingredient"."LabelDescription",
                       'IsAllergen', "ingredient"."IsAllergen",
                       'IsOrganic', "ingredient"."IsOrganic",
                       'IsNotVegan', "ingredient"."IsNotVegan",
                       'HideOnLabel', "ingredient"."HideOnLabel",
                       'IsDemo', "ingredient"."IsDemo",
                       'Kilojoules', "ingredient"."Kilojoules",
                       'Protein', "ingredient"."Protein",
                       'FatTotal', "ingredient"."FatTotal",
                       'FatSaturated', "ingredient"."FatSaturated",
                       'Carbohydrate', "ingredient"."Carbohydrate",
                       'Sugars', "ingredient"."Sugars",
                       'Fiber', "ingredient"."Fiber",
                       '_NetCarbs', "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                       'Sodium', "ingredient"."Sodium",
                       'PercAus', "ingredient"."PercAus",
                       'CostPer100g', "ingredient"."CostPer100g"
                   ) "Ex_Ingredient"
            FROM public."ItemIngredient" "itemIng"
            LEFT JOIN public."Product" "product" ON "itemIng"."ProductId" = "product"."Id"
            LEFT JOIN public."ProductVariation" "variation" ON "itemIng"."ProductVariationId" = "variation"."Id"
            INNER JOIN public."Ingredient" "ingredient" ON "itemIng"."IngredientId" = "ingredient"."Id"
            WHERE "itemIng"."ProductVariationId" = ${productVariationId}
              AND "itemIng"."Deleted" = 'False'
              AND "product"."Deleted" = 'False'
              AND "variation"."Deleted" = 'False'
              AND "ingredient"."Deleted" = 'False'
            ORDER BY "ingredient"."Name"
        `).rows;
    }

    static async RecordExists(productId: string | null, productVariationId: string | null, ingredientId: string) {
        const result = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."ItemIngredient"
            WHERE "IngredientId" = ${ingredientId}
              AND (
                  ("ProductId" = ${productId} AND "ProductVariationId" IS NULL)
                  OR
                  ("ProductId" IS NULL AND "ProductVariationId" = ${productVariationId})
              )
              AND "Deleted" = 'False'
        `;
        return result.rows[0]["Count"] > 0;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newItem: ItemIngredientModel) {
        // Validate that either ProductId or ProductVariationId is set (but not both)
        if ((newItem.ProductId === null || newItem.ProductId === "") &&
            (newItem.ProductVariationId === null || newItem.ProductVariationId === "")) {
            throw new Error("Either ProductId or ProductVariationId must be set for ItemIngredient");
        }

        // If both are set, prioritize ProductVariationId and set ProductId to null
        if (newItem.ProductId && newItem.ProductVariationId) {
            console.warn("Both ProductId and ProductVariationId are set for ItemIngredient. Using ProductVariationId only.");
            newItem.ProductId = null;
        }

        await sql`
            INSERT INTO public."ItemIngredient"
            (
                "Id",
                "ProductId",
                "ProductVariationId",
                "IngredientId",
                "AmountGrams",
                "ShowPercent",
                "CreatedAt"
            )
            VALUES
            (
                ${newItem.Id},
                ${newItem.ProductId},
                ${newItem.ProductVariationId},
                ${newItem.IngredientId},
                ${newItem.AmountGrams},
                ${newItem.ShowPercent},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(items: ItemIngredientModel[]) {
        for (const item of items) {
            await this.Create(item);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(item: ItemIngredientModel) {
        // Validate that either ProductId or ProductVariationId is set (but not both)
        if ((item.ProductId === null || item.ProductId === "") &&
            (item.ProductVariationId === null || item.ProductVariationId === "")) {
            throw new Error("Either ProductId or ProductVariationId must be set for ItemIngredient");
        }

        // If both are set, prioritize ProductVariationId and set ProductId to null
        if (item.ProductId && item.ProductVariationId) {
            console.warn("Both ProductId and ProductVariationId are set for ItemIngredient. Using ProductVariationId only.");
            item.ProductId = null;
        }

        // Check if the record exists by Id
        const recordExistsById = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."ItemIngredient"
            WHERE "Id" = ${item.Id}
        `;
        const existsById = recordExistsById.rows[0]["Count"] > 0;

        // If the record exists by Id, update it using the Id
        if (existsById) {
            await sql`
                UPDATE public."ItemIngredient"
                SET "ProductId"          = ${item.ProductId},
                    "ProductVariationId" = ${item.ProductVariationId},
                    "IngredientId"       = ${item.IngredientId},
                    "AmountGrams"        = ${item.AmountGrams},
                    "ShowPercent"        = ${item.ShowPercent},
                    "ModifiedAt"         = ${new Date().toUTCString()},
                    "Deleted"            = ${item.Deleted ?? false}
                WHERE "Id" = ${item.Id}
            `;
            return;
        }

        // If the record doesn't exist by Id, check if it exists by IngredientId, ProductId, and ProductVariationId
        const exists = await this.RecordExists(item.ProductId, item.ProductVariationId, item.IngredientId);

        // If the record doesn't exist at all, create it
        if (!exists) {
            await this.Create(item);
            return;
        }

        // Update the existing record by IngredientId, ProductId, and ProductVariationId
        await sql`
            UPDATE public."ItemIngredient"
            SET "AmountGrams"        = ${item.AmountGrams},
                "ShowPercent"        = ${item.ShowPercent},
                "ModifiedAt"         = ${new Date().toUTCString()},
                "Deleted"            = ${item.Deleted ?? false}
            WHERE "IngredientId" = ${item.IngredientId}
              AND (
                  ("ProductId" = ${item.ProductId} AND "ProductVariationId" IS NULL)
                  OR
                  ("ProductId" IS NULL AND "ProductVariationId" = ${item.ProductVariationId})
              )
        `;
    }

    static async UpdateList(items: ItemIngredientModel[]) {
        for (const item of items) {
            await this.Update(item);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."ItemIngredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteForProduct(productId: string) {
        await sql`
            UPDATE public."ItemIngredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductId" = ${productId}
              AND "ProductVariationId" IS NULL
        `;
    }

    static async DeleteForProductVariation(productVariationId: string) {
        await sql`
            UPDATE public."ItemIngredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductVariationId" = ${productVariationId}
        `;
    }
}
