"use client"

// Styles
import styles from "./JC_CheckoutButton.module.scss";
// React
import React from 'react';
// Next
import Link from 'next/link';
import Image from "next/image";

export default function JC_CheckoutButton() {
    return (

        <Link href="/checkout">

            <div className={styles.checkoutButtonContainer}>

                {/* Icon */}
                <Image
                    src="/icons/Bag.webp"
                    width={100}
                    height={100}
                    className={styles.bagIcon}
                    alt="Bag"
                />
                
                {/* Icon With CK */}
                <Image
                    src="/icons/BagWithCK.webp"
                    width={100}
                    height={100}
                    className={styles.bagIconWithCK}
                    alt="BagWithLogo"
                />
                
                {/* Text */}
                <div className={styles.checkoutText}>Checkout</div>

            </div>

        </Link>

    );
}
