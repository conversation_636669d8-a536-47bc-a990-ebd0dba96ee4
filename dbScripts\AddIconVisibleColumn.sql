-- Add IconVisible column to ItemSettings table
ALTER TABLE public."ItemSettings"
ADD COLUMN "IconVisible" boolean NOT NULL DEFAULT TRUE;

-- Update any existing records to have IconVisible set to TRUE by default
UPDATE public."ItemSettings"
SET "IconVisible" = TRUE
WHERE "IconVisible" IS NULL;

-- Verify the column was added correctly
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'ItemSettings'
AND column_name = 'IconVisible';
