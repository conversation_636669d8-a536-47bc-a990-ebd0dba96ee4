import { NextRequest, NextResponse } from "next/server";
import { ProductVariationIngredientModel } from "@/app/models/ProductVariationIngredient";
import { ProductVariationIngredientBusiness } from "./business";

export const dynamic = 'force-dynamic';

// - --- - //
// - GET - //
// - --- - //

export async function GET(request: NextRequest) {
    try {
        const productVariationId = new URL(request.url).searchParams.get("productVariationId");
        
        if (productVariationId) {
            const items = await ProductVariationIngredientBusiness.GetForProductVariation(productVariationId);
            return NextResponse.json(items);
        } else {
            const items = await ProductVariationIngredientBusiness.GetAll();
            return NextResponse.json(items);
        }
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const newItem: ProductVariationIngredientModel = new ProductVariationIngredientModel(await request.json());
        await ProductVariationIngredientBusiness.Create(newItem);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - UPDATE - //
// - ------ - //

export async function POST(request: NextRequest) {
    try {
        const item: ProductVariationIngredientModel = new ProductVariationIngredientModel(await request.json());
        await ProductVariationIngredientBusiness.Update(item);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// - ------ - //
// - DELETE - //
// - ------ - //

export async function DELETE(request: NextRequest) {
    try {
        const id: string = new URL(request.url).searchParams.get("id")!;
        await ProductVariationIngredientBusiness.Delete(id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
