import { sql } from "@vercel/postgres";
import { OrderModel } from "@/app/models/Order";
import { OrderItemModel } from "@/app/models/OrderItem";
import { JC_Utils } from "@/app/Utils";
import { OrderItemBusiness } from "../orderItem/business";

export class OrderBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    // By Id
    static async Get(orderId:string) {
        return (await sql<OrderModel>`
            SELECT
                "Id",
                "UserId",
                "OrderStatusCode",
                "SubmittedDate",
                "CompletedDate",
                "ValuesAtSubmissionJson",
                "CreatedAt",
                "ModifiedAt",
                "Deleted",
                (
                        SELECT json_agg(json_build_object(
                            'Id',                  "orderItem"."Id",
                            'OrderId',             "orderItem"."OrderId",
                            'ProductId',           "orderItem"."ProductId",
                            'ProductVariationIds', "orderItem"."ProductVariationIds",
                            'Quantity',            "orderItem"."Quantity",
                            'CreatedAt',           "orderItem"."CreatedAt",
                            'ModifiedAt',          "orderItem"."ModifiedAt",
                            'Deleted',             "orderItem"."Deleted",
                            'Ex_ProductName',      "itemProduct"."Name",
                            'Ex_VariationNames',   ARRAY( SELECT "Name" FROM public."ProductVariation" WHERE "Id"=ANY("orderItem"."ProductVariationIds"::UUID[]) AND "VariationCategoryCode" <> 'None' )
                        ) ORDER BY "orderItem"."CreatedAt" DESC)
                        FROM public."OrderItem" "orderItem"
                        INNER JOIN public."Product" "itemProduct" ON "orderItem"."ProductId" = "itemProduct"."Id"
                        WHERE "orderItem"."Deleted" = false
                          AND "orderItem"."OrderId" = "order"."Id"
                          AND "itemProduct"."EnabledOnWebsite" = true
                          AND NOT EXISTS (
                              SELECT 1 FROM public."ProductVariation" "pv"
                              WHERE "pv"."Id" = ANY("orderItem"."ProductVariationIds"::UUID[])
                              AND ("pv"."EnabledOnWebsite" = false OR "pv"."Deleted" = true)
                          )
                    ) AS "Ex_OrderItems"
            FROM public."Order" "order"
            WHERE "Id" = ${orderId}
              AND "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows[0];
    }

    // By User and Status
    static async GetByUserAndStatus(userId:string, orderStatusCode:string) {
        return (await sql<OrderModel>`
            SELECT
                "Id",
                "UserId",
                "OrderStatusCode",
                "SubmittedDate",
                "CompletedDate",
                "ValuesAtSubmissionJson",
                "CreatedAt",
                "ModifiedAt",
                "Deleted",
                (
                        SELECT json_agg(json_build_object(
                            'Id',                  "orderItem"."Id",
                            'OrderId',             "orderItem"."OrderId",
                            'ProductId',           "orderItem"."ProductId",
                            'ProductVariationIds', "orderItem"."ProductVariationIds",
                            'Quantity',            "orderItem"."Quantity",
                            'CreatedAt',           "orderItem"."CreatedAt",
                            'ModifiedAt',          "orderItem"."ModifiedAt",
                            'Deleted',             "orderItem"."Deleted",
                            'Ex_ProductName',      "itemProduct"."Name",
                            'Ex_VariationNames',   ARRAY( SELECT "Name" FROM public."ProductVariation" WHERE "Id"=ANY("orderItem"."ProductVariationIds"::UUID[]) AND "VariationCategoryCode" <> 'None' )
                        ) ORDER BY "orderItem"."CreatedAt" DESC)
                        FROM public."OrderItem" "orderItem"
                        INNER JOIN public."Product" "itemProduct" ON "orderItem"."ProductId" = "itemProduct"."Id"
                        WHERE "orderItem"."Deleted" = false
                          AND "orderItem"."OrderId" = "order"."Id"
                          AND "itemProduct"."EnabledOnWebsite" = true
                          AND NOT EXISTS (
                              SELECT 1 FROM public."ProductVariation" "pv"
                              WHERE "pv"."Id" = ANY("orderItem"."ProductVariationIds"::UUID[])
                              AND ("pv"."EnabledOnWebsite" = false OR "pv"."Deleted" = true)
                          )
                    ) AS "Ex_OrderItems"
            FROM public."Order" "order"
            WHERE "Deleted" = false
            AND "UserId" = ${userId}
            AND "OrderStatusCode" = ${orderStatusCode}
            ORDER BY "CreatedAt" DESC
        `).rows[0];
    }

    // By User and Status
    static async GetListUserPreviousOrders(userId:string) {
        return (await sql<OrderModel>`
            SELECT
                "Id",
                "UserId",
                "OrderStatusCode",
                "SubmittedDate",
                "CompletedDate",
                "ValuesAtSubmissionJson",
                "CreatedAt",
                "ModifiedAt",
                "Deleted",
                (
                        SELECT json_agg(json_build_object(
                            'Id',                  "orderItem"."Id",
                            'OrderId',             "orderItem"."OrderId",
                            'ProductId',           "orderItem"."ProductId",
                            'ProductVariationIds', "orderItem"."ProductVariationIds",
                            'Quantity',            "orderItem"."Quantity",
                            'CreatedAt',           "orderItem"."CreatedAt",
                            'ModifiedAt',          "orderItem"."ModifiedAt",
                            'Deleted',             "orderItem"."Deleted",
                            'Ex_ProductName',      "itemProduct"."Name",
                            'Ex_VariationNames',   ARRAY( SELECT "Name" FROM public."ProductVariation" WHERE "Id"=ANY("orderItem"."ProductVariationIds"::UUID[]) AND "VariationCategoryCode" <> 'None' )
                        ) ORDER BY "orderItem"."CreatedAt" DESC)
                        FROM public."OrderItem" "orderItem"
                        INNER JOIN public."Product" "itemProduct" ON "orderItem"."ProductId" = "itemProduct"."Id"
                        WHERE "orderItem"."Deleted" = false
                          AND "orderItem"."OrderId" = "order"."Id"
                          AND "itemProduct"."EnabledOnWebsite" = true
                          AND NOT EXISTS (
                              SELECT 1 FROM public."ProductVariation" "pv"
                              WHERE "pv"."Id" = ANY("orderItem"."ProductVariationIds"::UUID[])
                              AND ("pv"."EnabledOnWebsite" = false OR "pv"."Deleted" = true)
                          )
                    ) AS "Ex_OrderItems"
            FROM public."Order" "order"
            WHERE "Deleted" = false
            AND "OrderStatusCode" IN ('Submitted', 'Completed')
            AND "UserId" = ${userId}
            ORDER BY "CreatedAt" DESC
        `).rows;
    }


    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newOrder:OrderModel) {
        await sql`
            INSERT INTO public."Order"
            (
                "Id",
                "UserId",
                "OrderStatusCode",
                "SubmittedDate",
                "CompletedDate",
                "ValuesAtSubmissionJson",
                "CreatedAt"
            )
            VALUES
            (
                ${newOrder.Id},
                ${newOrder.UserId},
                ${newOrder.OrderStatusCode},
                ${newOrder.SubmittedDate ? new Date(newOrder.SubmittedDate).toISOString() : null},
                ${newOrder.CompletedDate ? new Date(newOrder.CompletedDate).toISOString() : null},
                ${newOrder.ValuesAtSubmissionJson},
                ${new Date().toUTCString()}
            )
        `;
        return newOrder;
    }

    // From UserId
    static async CreateOrderFromUserId(userId?:string) {
        let newOrder:OrderModel = await new OrderModel({ UserId: !JC_Utils.stringNullOrEmpty(userId) ? userId : undefined });
        await this.Create(newOrder);
        return newOrder;
    }


    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(orderData:OrderModel) {
        await sql`
            UPDATE public."Order"
                SET "Id"                     = ${orderData.Id},
                    "UserId"                 = ${orderData.UserId},
                    "OrderStatusCode"        = ${orderData.OrderStatusCode},
                    "SubmittedDate"          = ${orderData.SubmittedDate ? new Date(orderData.SubmittedDate).toISOString() : null},
                    "CompletedDate"          = ${orderData.CompletedDate ? new Date(orderData.CompletedDate).toISOString() : null},
                    "ValuesAtSubmissionJson" = ${orderData.ValuesAtSubmissionJson},
                    "ModifiedAt"             = ${new Date().toUTCString()},
                    "Deleted"                = ${orderData.Deleted}
                WHERE "Id" = ${orderData.Id}
        `;
    }

    // Replace order items with new items
    static async ReplaceOrderItems(orderId:string, newList:OrderItemModel[]) {
        // Delete all current items for this order
        await OrderItemBusiness.DeleteForOrder(orderId);
        // Add from new list
        await OrderItemBusiness.CreateList(newList);
    }


    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id:string) {
        await sql`
            UPDATE public."Order"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

}