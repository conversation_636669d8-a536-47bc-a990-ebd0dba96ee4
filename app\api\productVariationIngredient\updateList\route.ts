import { NextRequest, NextResponse } from "next/server";
import { ProductVariationIngredientModel } from "@/app/models/ProductVariationIngredient";
import { ProductVariationIngredientBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const requestData = await request.json();
        const items: ProductVariationIngredientModel[] = requestData.items.map((p: any) => new ProductVariationIngredientModel(p));
        await ProductVariationIngredientBusiness.UpdateList(items);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
