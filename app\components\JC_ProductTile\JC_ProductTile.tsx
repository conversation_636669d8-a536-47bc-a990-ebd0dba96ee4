// Styles
import styles from "./JC_ProductTile.module.scss";
// React
import React from 'react';
// Next
import Link from "next/link";
import Image from "next/image";

export default function JC_ProductTile(_: Readonly<{

    name: string;
    imageName: string;
    href: string;
    
}>) {
    return (
        <Link href={_.href} className={styles.mainContainer}>

            {/* Image */}
            <Image
                src={`/products/${_.imageName}.webp`}
                width={150}
                height={150}
                className={styles.groupImage}
                alt={_.name}
            />

            {/* Name */}
            <div className={styles.groupName}>{_.name}</div>
            
        </Link>
    );
}