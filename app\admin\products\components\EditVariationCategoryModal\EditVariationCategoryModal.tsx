"use client"

import React, { useState, useEffect } from 'react';
import JC_Modal from "@/app/components/JC_Modal/JC_Modal";
import JC_Form from "@/app/components/JC_Form/JC_Form";
import { ProductModel } from "@/app/models/Product";
import { JC_GetList } from "@/app/services/JC_GetList";
import { VariationCategoryModel } from "@/app/models/VariationCategory";
import { JC_FieldOption } from "@/app/models/ComponentModels/JC_FieldOption";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import styles from "./EditVariationCategoryModal.module.scss";

interface EditVariationCategoryModalProps {
    isOpen: boolean;
    onCancel: () => void;
    onSave: (oldCategoryCode: string, newCategoryCode: string, newCategory: VariationCategoryModel) => void;
    currentCategoryCode: string;
    product: ProductModel | null;
}

export default function EditVariationCategoryModal({
    isOpen,
    onCancel,
    onSave,
    currentCategoryCode,
    product
}: EditVariationCategoryModalProps) {
    const [initialised, setInitialised] = useState<boolean>(false);
    const [selectedCategory, setSelectedCategory] = useState<string>(currentCategoryCode);
    const [categoryOptions, setCategoryOptions] = useState<JC_FieldOption[]>([]);
    const [variationCategories, setVariationCategories] = useState<VariationCategoryModel[]>([]);
    const [modalKey, setModalKey] = useState<number>(0);

    // Reset to fresh state when modal opens or closes
    useEffect(() => {
        if (isOpen) {
            // Completely reset all state when opening
            setInitialised(false);
            setCategoryOptions([]);
            setVariationCategories([]);
            setModalKey(prev => prev + 1); // Force form re-render
        } else {
            // Reset state when closing
            setSelectedCategory("");
            setInitialised(false);
            setCategoryOptions([]);
            setVariationCategories([]);
        }
    }, [isOpen, currentCategoryCode]);

    // Initialize data when modal is opened
    useEffect(() => {
        if (isOpen) {
            // Load all variation categories
            JC_GetList<VariationCategoryModel>("variationCategory", {}, VariationCategoryModel)
                .then((categories) => {
                    // Process variation categories
                    const filteredCategories = categories
                        .filter(c => c.Code !== "None")
                        .sort((a, b) => a.SortOrder - b.SortOrder);

                    // Store the variation categories for later use
                    setVariationCategories(filteredCategories);

                    // Get existing variation categories from the product
                    const existingCategories = product?.Ex_Variations
                        ?.filter(v => !v.Deleted)
                        ?.map(v => v.VariationCategoryCode) || [];

                    // Get unique categories (a product can have multiple variations in the same category)
                    const uniqueExistingCategories = existingCategories.filter((value, index, self) =>
                        self.indexOf(value) === index
                    );

                    // Create options for JC_Radio
                    const options = filteredCategories.map(category => ({
                        OptionId: category.Code,
                        Label: category.Name,
                        // Disable the current category and any other existing categories on this product
                        Disabled: category.Code === currentCategoryCode || 
                                 (uniqueExistingCategories.includes(category.Code) && category.Code !== currentCategoryCode)
                    }));

                    setCategoryOptions(options);

                    // Set the selected category after categories are loaded
                    setSelectedCategory(currentCategoryCode);

                    // Set initialised to true after all data is loaded
                    setTimeout(() => {
                        setInitialised(true);
                    }, 100);
                });
        }
    }, [isOpen, product, currentCategoryCode]);

    // Handle save
    function handleSave() {
        if (!selectedCategory || selectedCategory === currentCategoryCode) return;

        // Find the selected category object
        const selectedCategoryObject = variationCategories.find(cat => cat.Code === selectedCategory);
        if (!selectedCategoryObject) return;

        onSave(currentCategoryCode, selectedCategory, selectedCategoryObject);
    }

    return (
        <JC_Modal
            isOpen={isOpen}
            onCancel={onCancel}
            title="Edit Category"
            isLoading={!initialised}
        >
            <div className={styles.modalContent}>
                {/* Category Selection Form */}
                <JC_Form
                    key={modalKey}
                    fields={[
                        {
                            inputId: "variation-category",
                            type: FieldTypeEnum.Radio,
                            label: "Category",
                            options: categoryOptions,
                            value: selectedCategory,
                            onChange: (newCategoryCode: string) => {
                                setSelectedCategory(newCategoryCode);
                            },
                            validate: (value: string | number | undefined) => {
                                if (!value) return "Please select a category";
                                if (value === currentCategoryCode) return "Please select a different category";
                                return "";
                            }
                        }
                    ]}
                    onSubmit={handleSave}
                    submitButtonText="Save"
                />
            </div>
        </JC_Modal>
    );
}
