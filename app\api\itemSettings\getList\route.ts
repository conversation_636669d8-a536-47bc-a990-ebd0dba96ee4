import { NextRequest, NextResponse } from "next/server";
import { ItemSettingsBusiness } from "../business";

export async function GET(request: NextRequest) {
    try {
        const productId = new URL(request.url).searchParams.get("productId");
        const productVariationId = new URL(request.url).searchParams.get("productVariationId");

        // If both are provided, prioritize productVariationId
        if (productVariationId) {
            const items = await ItemSettingsBusiness.GetForProductVariation(productVariationId);
            return NextResponse.json(items);
        } else if (productId) {
            const items = await ItemSettingsBusiness.GetForProduct(productId);
            return NextResponse.json(items);
        } else {
            const items = await ItemSettingsBusiness.GetAll();
            return NextResponse.json(items);
        }
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
