import styles from "./JC_BagView.module.scss";
import React, { useEffect, useState } from 'react';
import Image from "next/image";
import Link from 'next/link';
import JC_Spinner from '../JC_Spinner/JC_Spinner';
import { JC_Get } from "@/app/services/JC_Get";
import { JC_GetList } from '@/app/services/JC_GetList';
import { DecrementBagItemQuantity, IncrementBagItemQuantity, RemoveFromBag } from '@/app/services/Bag';
import JC_ModalConfirmation from '../JC_ModalConfirmation/JC_ModalConfirmation';
import { useSession } from 'next-auth/react';
import { OrderModel } from '@/app/models/Order';
import { OrderItemModel } from '@/app/models/OrderItem';
import { ProductModel } from '@/app/models/Product';
import { JC_ConfirmationModalUsageModel } from '@/app/models/ComponentModels/JC_ConfirmationModalUsage';
import { LocalStorageKeyEnum } from '@/app/enums/LocalStorageKey';

import { JC_Utils } from "@/app/Utils";


// - VARIABLES - //

let callInProgress = false;

export default function JC_BagView(_: Readonly<{

    overrideClass?: string;
    readOnly?: boolean;
    hideFooter?: boolean;
    enableScroll?: boolean;
    bag: OrderModel;
    bagChanged?: (newBag:OrderModel) => void;

}>) {

    // - STATE - //

    const session = useSession();
    const [products, setProducts] = useState<ProductModel[]>([]);
    // Confirmation
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>();
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);
    const [discountValue, setDiscountValue] = useState<number>(1);


    // - VARIABLES - //

    let hasDiscount = session.data?.user.IsDiscountUser;


    // - INITIALISE - //

    useEffect(() => {
        JC_GetList<ProductModel>("product", {}, ProductModel).then(list => setProducts(list));
        if (hasDiscount) {
            JC_Get("globalSettings", { code: "UserDiscount" }).then((result:any) => {
                setDiscountValue(result.Value);
            });
        }
    }, []);


    // - HANDLES - //

    // Remove item
    async function removeItem(itemId:string, productName:string) {
        setConfirmationModalData({
            width: "380px",
            title: "Remove Bag Item",
            text: `Are you sure you would like to remove ${productName} from your bag?`,
            submitButtons: [{
                text: "Remove",
                onSubmit: async () => {
                    setConfirmationLoading(true);
                    if (!callInProgress) {
                        callInProgress = true;
                        let newBag = await RemoveFromBag(session.data, itemId);
                        if (_.bagChanged != null) {
                            await _.bagChanged(newBag);
                        }
                        callInProgress = false;
                    }
                    setConfirmationLoading(false);
                    setConfirmationModalData(null);
                    JC_Utils.showToastSuccess("Item removed");
                }
            }]
        });
    }

    // Decerement quantity
    async function decrementQuantity(itemId:string) {
        if (!callInProgress) {
            callInProgress = true;
            let newBag = await DecrementBagItemQuantity(session.data, itemId);
            if (_.bagChanged != null) {
                await _.bagChanged(newBag);
            }
            callInProgress = false;
        }
    }

    // Incerement quantity
    async function incrementQuantity(itemId:string) {
        if (!callInProgress) {
            callInProgress = true;
            let newBag = await IncrementBagItemQuantity(session.data, itemId);
            if (_.bagChanged != null) {
                await _.bagChanged(newBag);
            }
            callInProgress = false;
        }
    }


    // - MAIN - //

    return products.length == 0
        ? (<div style={{height: "100px", paddingTop: "10px"}}><JC_Spinner /></div>)
        : (

            <div className={`${styles.mainContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

                {/* List */}
                <div className={`${styles.bagList} ${_.enableScroll ? styles.enableScroll : ""}`}>

                    {/* No Items */}
                    {(!_.bag.Ex_OrderItems || _.bag.Ex_OrderItems.length == 0) &&
                    <div className={styles.noItemsContainer}>
                        <div className={styles.noItemsText}>
                            You have no items yet!
                        </div>
                    </div>}

                    {/* Items */}
                    {_.bag.Ex_OrderItems?.sort((a,b) => a.CreatedAt > b.CreatedAt ? 1 : -1).map((item:OrderItemModel) => {
                        let product = products.find(p => p.Id == item.ProductId)!;
                        let variations = product?.Ex_Variations.filter(v => v.VariationCategoryCode != "None" && item.ProductVariationIds.map(id => id.toLowerCase()).includes(v.Id.toLowerCase()))!;


                        return <div key={item.Id} className={styles.itemContainer}>

                            {/* Image */}
                            <Image
                                className={styles.itemImage}
                                src={!JC_Utils.stringNullOrEmpty(product?.ImageFileName) ? `/products/products/${product.ImageFileName}.webp` : `/products/placeholder.webp`}
                                width={0}
                                height={0}
                                alt="CasellaKitchenLogo"
                                unoptimized
                            />
                            {/* Main */}
                            <div className={styles.itemBody}>
                                {/* Title */}
                                <Link href={`/product?id=${product.Id}`}><div className={styles.itemTitle}>{`${product.Name} ${variations.map(v => `(${v.Name})`).join(' ')}`}</div></Link>
                                {/* Price + Quantity */}
                                <div className={styles.priceQuantityContainer}>
                                    {/* Price */}
                                    <div className={styles.itemPrice}>${JC_Utils.roundAndCutZeroes(item.getPrice(products) * discountValue, 2)}</div>
                                    {/* Quantity */}
                                    <div className={styles.quantityContainer}>
                                        {/* Remove Button */}
                                        {!_.readOnly &&
                                        <Image
                                            src={`/icons/Cross.webp`}
                                            width={0}
                                            height={0}
                                            className={styles.removeButton}
                                            alt="Cancel"
                                            onClick={() => removeItem(item.Id, `${product.Name} ${variations.map(v => `(${v.Name})`).join(' ')}`)}
                                            unoptimized
                                        />}
                                        {/* Buttons + Num */}
                                        {!_.readOnly && <div className={styles.decrementButton} onClick={() => item.Quantity > 1 ? decrementQuantity(item.Id) : null}>-</div>}
                                        <div className={styles.quantityText}>{item.Quantity}</div>
                                        {!_.readOnly && <div className={styles.incrementButton} onClick={() => incrementQuantity(item.Id)}>+</div>}
                                    </div>
                                </div>
                            </div>
                            {/* Item Total Price */}
                            <div className={styles.itemTotalPrice}>
                                ${(((product.Price + variations.reduce((prev, cur) => prev+(cur.AddedPrice ?? 0), 0))*item.Quantity) * discountValue).toFixed(2)}
                            </div>

                        </div>;
                    })}

                </div>

                {/* Total */}
                {_.hideFooter ||
                <div className={styles.totalFooter}>
                    <div>Total</div>
                    <div>{!_.bag.Ex_OrderItems || _.bag.Ex_OrderItems.length == 0 ? '-' : `${_.bag.getTotalItems()} items | $${(_.bag.getTotalPrice(products) * discountValue).toFixed(2)}`}</div>
                </div>}

                {/* Confirmation */}
                {confirmationModalData &&
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />}

            </div>

    );
}