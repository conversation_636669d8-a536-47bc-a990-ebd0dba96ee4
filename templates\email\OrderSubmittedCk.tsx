import { OrderModel } from "@/app/models/Order";

export default function Template_OrderSubmittedCkEmail(_:OrderSubmittedCkEmailModel) {
    return (

        <div>
            <table>

                <tr>
                    <td>USER ID MATE:</td>
                    <td>{_.userId}</td>
                </tr>

                <tr>
                    <td>NAME MATE:</td>
                    <td>{_.fullName}</td>
                </tr>

                <tr>
                    <td>EMAIL MATE:</td>
                    <td>{_.email}</td>
                </tr>

                <tr>
                    <td>AMOUNT MATE:</td>
                    <td>{_.amount}</td>
                </tr>

                <br/>
                <br/>
                <br/>

                {_.items.map(item =>
                    <tr key={`${item.productName} ${item.variationsString}`}>
                        <td>{item.productName}</td>
                        <td>{item.variationsString}</td>
                    </tr>
                )}

            </table>
        </div>

    );
}

export interface OrderSubmittedCkEmailModel {
    userId: string;
    fullName: string;
    email: string;
    items: {
        productName: string;
        variationsString: string;
        imagePath: string;
    }[],
    amount: number;
}