import { sql } from "@vercel/postgres";
import { OrderItemModel } from "@/app/models/OrderItem";

export class OrderItemBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async CheckOrderExists(orderId: string) {
        return (await sql`SELECT "Id" FROM public."Order" WHERE "Id" = ${orderId} AND "Deleted" = 'False'`).rows.length > 0;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(orderItemData:OrderItemModel) {
        const theQuery = `
            INSERT INTO public."OrderItem"
            (
                "Id",
                "OrderId",
                "ProductId",
                "ProductVariationIds",
                "Quantity",
                "CreatedAt"
            )
            VALUES
            (
                '${orderItemData.Id}',
                '${orderItemData.OrderId}',
                '${orderItemData.ProductId}',
                ARRAY[${orderItemData.ProductVariationIds.map((_, index) => `$${index+1}`).join(',')}],
                ${orderItemData.Quantity},
                '${orderItemData.CreatedAt}'
            )
        `;
        // Use incoming item's "CreatedAt" to make sure this "CreatedAt" is different to others, so will sort properly on page
        // If do "new Date().toUTCString()" from doing "MoveAllLocalToUser()", all "CreatedAt" values are the same
        await sql.query(theQuery, orderItemData.ProductVariationIds);
    }

    static async CreateList(orderItemListData:OrderItemModel[]) {
        for (let i = 0; i < orderItemListData.length; i++) {
            await this.Create(orderItemListData[i]);
        }
    }


    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(orderItemData:OrderItemModel) {
        await sql`
            UPDATE public."OrderItem"
            SET "Id"                  = ${orderItemData.Id},
                "Quantity"            = ${orderItemData.Quantity},
                "ModifiedAt"          = ${new Date().toUTCString()},
                "Deleted"             = ${orderItemData.Deleted}
            WHERE "Id" = ${orderItemData.Id}
        `;
    }


    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async DeleteList(orderItems:OrderItemModel[]) {
        await sql`
            UPDATE public."OrderItem"
            SET "Deleted"    = True,
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" IN (${orderItems.map(i => i.Id).join(',')})
        `;
    }

    static async DeleteForOrder(orderId:string) {
        await sql`
            UPDATE public."OrderItem"
            SET "Deleted"    = True,
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "OrderId" = ${orderId}
        `;
    }

}