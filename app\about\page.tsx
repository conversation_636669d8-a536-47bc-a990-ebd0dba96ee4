// Styles
import styles from "./page.module.scss";
// Next
import Image from "next/image";
// JC Components
import JC_Title from "../components/JC_Title/JC_Title";


export default function Page_About() {

    // - MAIN - //

    return (
        <div className={styles.mainContainer}>
            <JC_Title title="About Casella Kitchen" />
            <div className={styles.aboutBody}>
                <div className={styles.aboutContent}>Hi everyone! Welcome to Casella Kitchen.</div>
                <div className={styles.aboutContent}>My name is <PERSON> and this business came about through the love of baking at home with 5 children who have either severe anaphylaxis and/or food intolerances.</div>
                <div className={styles.aboutContent}>Having married into an Italian family, I was influenced by the Mediterranean way of eating and preparing food.</div>
                <div className={styles.aboutContent}>After discovering the Paleo diet and improving my own health through healthier eating habits, the Almond Pasta was my first product and everything else followed from there.</div>
                <div className={styles.aboutContent}>Doing the hard yards at markets around Perth and selling from my home kitchen, I discovered that there was a real need for organic wholefoods that resembled lower carb, comfort foods that taste as good as what nanna use to make!</div>
                <div className={styles.aboutContent}>During Covid, the products began to sell in organic and wholefood stores, as many as I could handle being a small batch, hand-made producer.</div>
                <div className={styles.aboutContent}>I&apos;d like to thank my family, friends, retailers and suppliers for their help, advice and support without which this business would not be here today.</div>
                <div className={styles.aboutContent}>
                    However, YOU, the customers, with your continual support, patience, feedback and encouragement, have been the driving force to keep going. I bake for you and for this I sincerely thank you.
                    <Image
                            className={styles.hearetIcon}
                            src="/icons/Heart.webp"
                            width={0}
                            height={0}
                            alt="Bag"
                            unoptimized
                        /></div>
                <div className={styles.aboutContent}>Buon Appetito!</div>
            </div>
        </div>
    );
}
