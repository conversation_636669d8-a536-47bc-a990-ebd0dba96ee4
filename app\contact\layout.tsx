import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen - Contact",
    description: "Contact <PERSON> with any enquiries."
};

export default async function Layout_Contact(_: Readonly<{

    children: React.ReactNode;

}>) {

    // - AUTH - //

    const session = await auth();
    if (!session) {
        redirect("/");
    }

    // - MAIN - //

    return _.children;
    
}
