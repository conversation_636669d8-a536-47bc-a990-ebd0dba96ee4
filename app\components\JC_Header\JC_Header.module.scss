@import '../../global';


$dietaryDivider: solid $pastelPrimaryColor 2px;

// Header
.mainContainer {
    margin: auto;
    width: 100%;
    height: max-content;
    padding: 10px 40px 30px 40px;
    box-sizing: border-box;
    background-color: $lightPrimaryColor;
    border-bottom: solid $smallBorderWidth $secondaryColor;

    // Logo + Account
    .logoAccountContainer {
        margin: auto;
        max-width: 800px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        align-items: center;

        .logo {
            margin-top: -10px;
            width: 380px;
            height: auto;
            max-height: 400px;
            box-sizing: border-box;
            cursor: pointer;
            opacity: 0.9;
            &:hover {
                opacity: 1;
            }
        }

        // Checkout + Login/Register (used in 2 places)
        .checkoutAccountContainer {
            width: max-content;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            column-gap: 40px;

            // Login/Register
            .loginRegisterContainer {
                width: max-content;
                height: max-content;
                cursor: pointer;
    
                .loginRegisterText {
                    padding: 12px 0;
                    font-size: 25px;
                    font-weight: bold;
                }
                &:hover {
                    color: $primaryColor;
                }
            }
    
            &.tinyCheckoutAccount {
                display: none;
            }
        }
    }

    // Navs
    .navsContainer {
        margin: 20px auto 0 auto;
        max-width: 1200px;
        display: flex;
        justify-content: center;
        column-gap: 30px;
        align-items: center;

        // Nav Buttons
        .navButtons {
            display: flex;
            column-gap: 20px;
        }
    }
}

// Dietary Banner
.dietaryBanner {
    width: 100%;
    padding: 14px 10px;
    display: flex;
    flex-direction: row;
    border-bottom: solid $smallBorderWidth $secondaryColor;
    background-color: $lightSecondaryColor;
    font-size: $defaultFontSize;
    font-weight: 500;
    box-sizing: border-box;
    .word {
        text-align: center;
        color: $secondaryColor;
        border-right: $dietaryDivider;
        flex: 1 1 auto;
        &:last-child {
            border-right: none !important;
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        .navsContainer {
            flex-direction: column;
            row-gap: 28px;
            .navButtons { order: -1; }
        }
    }
    .dietaryBanner {
        flex-wrap: wrap;
        row-gap: 3px;
        .mediumScreenBreak {
            flex-basis: 100%;
            height: 0;
        }
        .word:nth-child(6) {
            border-right: none;
        }
    }
}
@media (max-width: $smallScreenSize) {
    .navsContainer {
        .navButtons {
            width: 500px;
            justify-content: center;
            row-gap: 16px;
            flex-wrap: wrap;
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        border-width: $largeBorderWidth;
        .logo {
            width: 240px !important;
        }
        .logoAccountContainer {
            .checkoutAccountContainer {
                column-gap: 26px !important;
                .loginRegisterText {
                    font-size: 18px !important;
                }
            }
        }
    }
    .dietaryBanner {
        display: none;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        padding: 10px 10px 20px 10px;
        .logo {
            width: 180px !important;
        }
        .logoAccountContainer {
            flex-direction: column;
            .checkoutAccountContainer {
                column-gap: 18px !important;
            }
        }
        .navsContainer {
            margin-top: 14px;
            row-gap: 22px;
            .navButtons {
                width: 400px;
                column-gap: 15px;
                row-gap: 14px;
                a {
                    width: 90px !important;
                }
            }
        }
    }
}