"use client"

import React, { useState } from 'react';
import J<PERSON>_Modal from "@/app/components/JC_Modal/JC_Modal";
import JC_Form from "@/app/components/JC_Form/JC_Form";
import { D_FieldModel_Name } from "@/app/models/ComponentModels/JC_Field";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationsDataModel } from "@/app/models/ProductVariationsData";

interface NewProductModalProps {
    isOpen: boolean;
    onCancel: () => void;
    onSave: (product: ProductModel, productVariationsData: ProductVariationsDataModel) => void;
    products: ProductModel[];
}

export default function NewProductModal({ isOpen, onCancel, onSave, products }: NewProductModalProps) {
    const [newProductName, setNewProductName] = useState<string>("");

    // Create new product
    function createNewProduct() {
        if (newProductName.trim() === "") return;

        // Create a new product
        const newProduct = new ProductModel({
            Name: newProductName,
            UI_HasChanges: true
        });

        // Get the next sort order
        const sortOrders = products
            .filter(p => !p.Deleted)
            .map(p => p.SortOrder);
        newProduct.SortOrder = sortOrders.length > 0 ? Math.max(...sortOrders) + 1 : 1;

        // Ensure settings have the correct ProductId and are marked as changed
        newProduct.Ex_Settings.ProductId = newProduct.Id;
        newProduct.Ex_Settings.UI_HasChanges = true;

        // Create a product-only ProductVariationsData record
        // Barcodes will be assigned in the backend
        const productVariationsData = ProductVariationsDataModel.createForProduct(
            newProduct.Id
        );

        // Call the save callback with the new product and product-only variations data
        onSave(newProduct, productVariationsData);

        // Reset the form
        setNewProductName("");
    }

    // Reset the form when the modal is opened
    React.useEffect(() => {
        if (isOpen) {
            setNewProductName("");
        }
    }, [isOpen]);

    return (
        <JC_Modal
            isOpen={isOpen}
            onCancel={onCancel}
            title="New Product"
        >
            <JC_Form
                fields={[
                    {
                        ...D_FieldModel_Name(),
                        value: newProductName,
                        onChange: (newValue: string) => setNewProductName(newValue),
                        autoFocus: true
                    }
                ]}
                onSubmit={createNewProduct}
                submitButtonText="Create"
            />
        </JC_Modal>
    );
}
