import { NextRequest, NextResponse } from "next/server";
import { ProductVariationBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const { id, sortOrder } = await request.json();
        await ProductVariationBusiness.UpdateSortOrder(id, sortOrder);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
