import { NextRequest, NextResponse } from 'next/server';
import { unstable_noStore } from 'next/cache';
import { IngredientModel } from '@/app/models/Ingredient';
import { IngredientBusiness } from './business';

// - --- - //
// - GET - //
// - --- - //

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        const result = await IngredientBusiness.GetAll();
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - -------- //
// - CREATE - //
// - -------- //

export async function PUT(request: NextRequest) {
    try {
        const newIngredient:IngredientModel = await request.json();

        await IngredientBusiness.Create(newIngredient);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// - -------- //
// - UPDATE - //
// - -------- //

export async function POST(request: NextRequest) {
    try {
        const ingredients:IngredientModel[] = await request.json();
        await IngredientBusiness.UpdateList(ingredients);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}