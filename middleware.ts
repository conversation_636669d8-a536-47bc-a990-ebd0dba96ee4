import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the pathname from the URL
  const pathname = request.nextUrl.pathname;

  // Redirect any demo routes to the home page
  if (pathname.startsWith('/demo')) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Clone the response and add the pathname to the headers
  const response = NextResponse.next();
  response.headers.set('x-pathname', pathname);

  return response;
}

// Configure the middleware to run on all paths
export const config = {
  matcher: '/:path*',
};
