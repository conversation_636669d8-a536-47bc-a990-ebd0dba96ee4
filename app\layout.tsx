import styles from "./layout.module.scss";
import type { Metadata } from "next";
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Inter, Special_Elite, Margarine } from 'next/font/google';
import { ToastContainer } from "react-toastify";
import { SessionProvider } from "next-auth/react";
import J<PERSON>_Header from "./components/JC_Header/JC_Header";
import JC_Footer from "./components/JC_Footer/JC_Footer";
import { auth } from "./auth";
import TempComingSoon from "./tempComingSoon";
import { headers } from 'next/headers';
import { shouldHideHeaderFooter } from './utils/pageConfig';
import JC_AdminBackground from "./components/JC_AdminBackground/JC_AdminBackground";

// Site Metadata
export const metadata: Metadata = {
    title: "Casella Kitchen",
    description: "Order Casella Kitchen health food products.",
};

// Font
const inter = Inter({ subsets: ["latin"], variable: '--font-inter' });
const kaushanScript = Special_Elite({ weight: "400", subsets: ["latin"], variable: '--font-kaushan-script' });
const shadowsIntoLight = Margarine({ weight: "400", subsets: ["latin"], variable: '--title-font' });

// Site Root
export default async function Layout_Root(_: Readonly<{
    children: React.ReactNode;
}>) {
    // Get the current path to check if header/footer should be hidden
    const headersList = headers();
    const path = headersList.get('x-pathname') || headersList.get('x-invoke-path') || '';
    const hideHeaderFooter = shouldHideHeaderFooter(path);

    const session = await auth();

    // Show content only if user is authenticated
    const showContent = session;

    return (
        <html lang="en">

            <body className={`${styles.rootMainContainer} ${inter.variable} ${kaushanScript.variable} ${shadowsIntoLight.variable}`} id="rootMainContainer">

                {showContent && !hideHeaderFooter && <JC_Header />}

                {showContent && <div className={styles.pageContainer}>
                    <SessionProvider session={session}>
                        {_.children}
                    </SessionProvider>
                </div>}

                {showContent && !hideHeaderFooter && <JC_Footer />}

                {!showContent && <TempComingSoon />}

                <ToastContainer />

                <SpeedInsights />

                <JC_AdminBackground />

            </body>

        </html>
  );
}
