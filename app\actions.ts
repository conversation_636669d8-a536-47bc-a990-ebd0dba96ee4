'use server'

import { signIn } from "./auth"

export async function  authenticate(email: string, password: string) {
    try {
        const result = await signIn("credentials", {
            email: email,
            password: password,
            redirect: false,
        });
        return result;
    } catch (error:any) {
        if (error.code === "invalid_credentials") {
            return { error: "Incorrect email or password" }
        } else if (error.code === "user_locked_out") {
            return { error: "Too many failed attempts. Try again later." }
        } else {
            throw Error("Failed to authenticate")
        }
    }
}