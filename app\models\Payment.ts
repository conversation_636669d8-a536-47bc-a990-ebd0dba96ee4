export class PaymentModel {

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    OrderId: string;
    PaymentStatusCode: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<PaymentModel>) {
        this.Id = "";
        this.OrderId = "";
        this.PaymentStatusCode = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.OrderId} | ${this.PaymentStatusCode}`;
    }
}
