"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";

import J<PERSON>_Spinner from "../../components/JC_Spinner/JC_Spinner";
import <PERSON><PERSON>_<PERSON>ton from "@/app/components/JC_Button/JC_Button";
import <PERSON><PERSON>_<PERSON> from "@/app/components/JC_Field/JC_Field";
import { JC_GetList } from "@/app/services/JC_GetList";
import { JC_Post } from "@/app/services/JC_Post";
import { JC_Put } from "@/app/services/JC_Put";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";
import { FieldTypeEnum } from "@/app/enums/FieldType";
import { JC_Utils } from "@/app/Utils";
import NewGlobalSettingModal from "./components/NewGlobalSettingModal/NewGlobalSettingModal";

export default function Page_GlobalSettings() {

    // - ----- - //
    // - STATE - //
    // - ----- - //

    const [globalSettings, setGlobalSettings] = useState<GlobalSettingsModel[]>([]);
    const [originalGlobalSettings, setOriginalGlobalSettings] = useState<GlobalSettingsModel[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [newSettingModalOpen, setNewSettingModalOpen] = useState<boolean>(false);

    // - --------- - //
    // - LIFECYCLE - //
    // - --------- - //

    useEffect(() => {
        fetchGlobalSettings();
    }, []);

    // These functions have been moved out of useEffect

    // - --------- - //
    // - FUNCTIONS - //
    // - --------- - //

    // Check if there are any changes
    const checkForChanges = (): boolean => {
        if (originalGlobalSettings.length === 0) return false;
        return JSON.stringify(globalSettings) !== JSON.stringify(originalGlobalSettings);
    };

    // Check if all fields are valid
    const validateAllFields = (): boolean => {
        return globalSettings.every(setting =>
            !JC_Utils.stringNullOrEmpty(setting.Code) &&
            !JC_Utils.stringNullOrEmpty(setting.Description) &&
            !JC_Utils.stringNullOrEmpty(setting.Value)
        );
    };

    const fetchGlobalSettings = async () => {
        setIsLoading(true);
        try {
            const response = await JC_GetList<GlobalSettingsModel>("globalSettings/all", {}, GlobalSettingsModel);
            if (response) {
                setGlobalSettings(response);
                setOriginalGlobalSettings(JSON.parse(JSON.stringify(response))); // Deep copy
            }
        } catch (error) {
            console.error("Error fetching global settings:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleValueChange = (index: number, field: keyof GlobalSettingsModel, value: string) => {
        // Use functional update to avoid closure issues
        setGlobalSettings(prevSettings => {
            const updatedSettings = [...prevSettings];
            updatedSettings[index] = {
                ...updatedSettings[index],
                [field]: value
            };
            return updatedSettings;
        });
    };

    const handleAddNewSetting = () => {
        setNewSettingModalOpen(true);
    };

    const saveNewSetting = (newSetting: GlobalSettingsModel) => {
        setGlobalSettings(prevSettings => [...prevSettings, newSetting]);
        setNewSettingModalOpen(false);
    };

    const handleSave = async () => {
        if (!validateAllFields()) return;

        setIsLoading(true);
        try {
            // Update existing settings
            const existingSettings = globalSettings.filter(setting =>
                originalGlobalSettings.some(orig => orig.Code === setting.Code)
            );

            // Add new settings
            const newSettings = globalSettings.filter(setting =>
                !originalGlobalSettings.some(orig => orig.Code === setting.Code)
            );

            if (existingSettings.length > 0) {
                await JC_Post("globalSettings", existingSettings, { list: "true" });
            }

            if (newSettings.length > 0) {
                await JC_Put("globalSettings", newSettings, { list: "true" });
            }

            // Refresh data
            await fetchGlobalSettings();
        } catch (error) {
            console.error("Error saving global settings:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        setGlobalSettings(() => JSON.parse(JSON.stringify(originalGlobalSettings)));
    };

    // - ------ - //
    // - RENDER - //
    // - ------ - //

    if (isLoading) {
        return <JC_Spinner isPageBody={true} />;
    }

    return (
        <div className={styles.mainContainer}>
            <h1 className={styles.pageTitle}>Global Settings</h1>

            <div className={styles.settingsContainer}>
                <div className={styles.settingsHeader}>
                    <div className={styles.codeHeader}>Code</div>
                    <div className={styles.descriptionHeader}>Description</div>
                    <div className={styles.valueHeader}>Value</div>
                </div>

                {globalSettings.map((setting, index) => (
                    <div key={setting.Code || `new-${index}`} className={styles.settingRow}>
                        <JC_Field
                            inputId={`code-${index}`}
                            type={FieldTypeEnum.Text}
                            value={setting.Code}
                            onChange={(value) => handleValueChange(index, 'Code', value)}
                            placeholder="Enter code"
                            readOnly={true}
                            overrideClass={styles.codeField}
                        />
                        <JC_Field
                            inputId={`description-${index}`}
                            type={FieldTypeEnum.Text}
                            value={setting.Description}
                            onChange={(value) => handleValueChange(index, 'Description', value)}
                            placeholder="Enter description"
                            overrideClass={styles.descriptionField}
                            inputOverrideClass={styles.descriptionFieldInput}
                        />
                        <JC_Field
                            inputId={`value-${index}`}
                            type={FieldTypeEnum.Text}
                            value={setting.Value}
                            onChange={(value) => handleValueChange(index, 'Value', value)}
                            placeholder="Enter value"
                            overrideClass={styles.valueField}
                        />
                    </div>
                ))}

                <div className={styles.addNewButton} onClick={handleAddNewSetting}>
                    <div>+</div>
                </div>
            </div>

            {/* New Setting Modal */}
            <NewGlobalSettingModal
                isOpen={newSettingModalOpen}
                saveNewSetting={saveNewSetting}
                onCancel={() => setNewSettingModalOpen(false)}
            />

            <div className={styles.actionButtons}>
                <JC_Button
                    text="Cancel"
                    onClick={handleCancel}
                    overrideClass={styles.cancelButton}
                    isDisabled={!checkForChanges()}
                />
                <JC_Button
                    text="Save"
                    onClick={handleSave}
                    isSecondary
                    isDisabled={!checkForChanges() || !validateAllFields()}
                />
            </div>
        </div>
    );
}
