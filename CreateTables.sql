-- Create Tables SQL Script generated from DbSchema.dbml

-- Setup logging wrapper
DO $$
BEGIN
    RAISE NOTICE E'\n===== STARTING DATABASE SCHEMA CREATION =====\n';
END $$;

-- User Table
DO $$
BEGIN
    RAISE NOTICE 'Creating User table...';
END $$;
CREATE TABLE public."User" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "FirstName" varchar(100) NOT NULL,
  "LastName" varchar(100) NULL,
  "Email" varchar(100) NOT NULL,
  "PasswordHash" varchar NOT NULL,
  "LoginFailedAttempts" int NOT NULL DEFAULT 0,
  "LoginLockoutDate" timestamp NULL,
  "ChangePasswordToken" varchar(200) NULL,
  "ChangePasswordTokenDate" timestamp NULL,
  "Phone" varchar(20) NULL,
  "IsAdmin" boolean NOT NULL DEFAULT FALSE,
  "IsWholesale" boolean NOT NULL DEFAULT FALSE,
  "CompanyName" varchar(200) NULL,
  "IsEmailSubscribed" boolean NOT NULL DEFAULT TRUE,
  "IsDiscountUser" boolean NOT NULL DEFAULT FALSE,
  "StripeCustomerId" varchar(100) NULL,
  "IsVerified" boolean NOT NULL DEFAULT FALSE,
  "VerificationToken" varchar(200) NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

COMMENT ON COLUMN public."User"."IsAdmin" IS 'An admin user can make a user admin through Users list page.';
COMMENT ON COLUMN public."User"."IsWholesale" IS 'User has different prices if they are a wholesaler. An admin user can make a user wholesale through the Users list page.';
COMMENT ON COLUMN public."User"."CompanyName" IS 'Required field for wholesale users.';
COMMENT ON COLUMN public."User"."IsDiscountUser" IS 'Discount amount set in [GlobalSettings].';
COMMENT ON COLUMN public."User"."StripeCustomerId" IS 'Set when user makes payment for first time since their Stripe customer account is created.';

DO $$
BEGIN
    RAISE NOTICE 'User table created successfully';
END $$;

-- Address Table
DO $$
BEGIN
    RAISE NOTICE 'Creating Address table...';
END $$;
CREATE TABLE public."Address" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "UserId" UUID NOT NULL,
  "Line1" varchar(200) NOT NULL,
  "Line2" varchar(200) NULL,
  "City" varchar(50) NOT NULL,
  "Postcode" varchar(16) NOT NULL,
  "Country" varchar(100) NOT NULL DEFAULT 'Australia',
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_Address_User" FOREIGN KEY ("UserId") REFERENCES public."User" ("Id")
);

DO $$
BEGIN
    RAISE NOTICE 'Address table created successfully';
END $$;

-- UserPersistedData Table
DO $$
BEGIN
    RAISE NOTICE 'Creating UserPersistedData table...';
END $$;
CREATE TABLE public."UserPersistedData" (
  "Id" UUID NOT NULL,
  "UserId" UUID NOT NULL,
  "Code" varchar(50) NOT NULL,
  "Value" varchar(200) NOT NULL,
  PRIMARY KEY ("Id", "Code"),
  CONSTRAINT "FK_UserPersistedData_User" FOREIGN KEY ("UserId") REFERENCES public."User" ("Id")
);

DO $$
BEGIN
    RAISE NOTICE 'UserPersistedData table created successfully';
END $$;

-- Stockist Table
DO $$
BEGIN
    RAISE NOTICE 'Creating Stockist table...';
END $$;
CREATE TABLE public."Stockist" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "Name" varchar(200) NOT NULL,
  "AddressString" varchar(300) NOT NULL,
  "AddressLat" decimal NOT NULL,
  "AddressLong" decimal NOT NULL,
  "Phone" varchar(20) NOT NULL,
  "LogoImageName" varchar(30) NOT NULL,
  "SiteUrl" varchar NOT NULL,
  "MapsUrl" varchar NOT NULL,
  "IsFullRange" boolean NOT NULL DEFAULT TRUE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

DO $$
BEGIN
    RAISE NOTICE 'Stockist table created successfully';
END $$;

-- Contact Table
DO $$
BEGIN
    RAISE NOTICE 'Creating Contact table...';
END $$;
CREATE TABLE public."Contact" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "UserId" UUID NULL,
  "Name" varchar(200) NOT NULL,
  "Email" varchar(100) NOT NULL,
  "Phone" varchar(20) NULL,
  "Message" varchar NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  CONSTRAINT "FK_Contact_User" FOREIGN KEY ("UserId") REFERENCES public."User" ("Id")
);

COMMENT ON COLUMN public."Contact"."UserId" IS 'Nullable since user might not be logged in so just provide name.';
COMMENT ON COLUMN public."Contact"."Name" IS 'If UserId not null then this will be the User record''s FirstName+LastName, otherwise is whatever user inputs on "Contact" form.';

DO $$
BEGIN
    RAISE NOTICE 'Contact table created successfully';
END $$;

-- Product Table
DO $$
BEGIN
    RAISE NOTICE 'Creating Product table...';
END $$;
CREATE TABLE public."Product" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "Name" varchar(100) NOT NULL,
  "Description" varchar(200) NULL,
  "UnitName" varchar(100) NOT NULL,
  "Price" decimal NOT NULL,
  "WholesalePrice" decimal NULL,
  "VariationAddedPricesJson" varchar NULL,
  "ImageFileName" varchar(100) NOT NULL,
  "EnabledOnWebsite" boolean NOT NULL DEFAULT FALSE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

COMMENT ON COLUMN public."Product"."UnitName" IS 'For product page, eg. "$12 / pack", "$12 / jar"';
COMMENT ON COLUMN public."Product"."WholesalePrice" IS 'Not sure if will have fixed price or percentage of retail.';
COMMENT ON COLUMN public."Product"."VariationAddedPricesJson" IS 'JSON for logic so when particular option has been selected in particular category, override AddedPrice on other particular variant option.';

-- DietaryAttribute Table
CREATE TABLE public."DietaryAttribute" (
  "Code" varchar(50) NOT NULL PRIMARY KEY,
  "Description" varchar(200) NOT NULL,
  "SortOrder" int NOT NULL DEFAULT 999
);

-- ProductDietaryAttribute Table
CREATE TABLE public."ProductDietaryAttribute" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "ProductId" UUID NOT NULL,
  "DietaryAttributeCode" varchar(50) NOT NULL,
  CONSTRAINT "FK_ProductDietaryAttribute_Product" FOREIGN KEY ("ProductId") REFERENCES public."Product" ("Id"),
  CONSTRAINT "FK_ProductDietaryAttribute_DietaryAttribute" FOREIGN KEY ("DietaryAttributeCode") REFERENCES public."DietaryAttribute" ("Code")
);

-- VariationCategory Table
CREATE TABLE public."VariationCategory" (
  "Code" varchar(50) NOT NULL PRIMARY KEY,
  "Name" varchar(100) NOT NULL,
  "HasLabelSizeSettings" boolean NOT NULL DEFAULT FALSE,
  "HasIngredientsSettings" boolean NOT NULL DEFAULT FALSE,
  "HasServingsSettings" boolean NOT NULL DEFAULT FALSE,
  "HasNameOverrideSettings" boolean NOT NULL DEFAULT FALSE,
  "HasLabelsSettings" boolean NOT NULL DEFAULT FALSE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE
);

-- ProductVariation Table
CREATE TABLE public."ProductVariation" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "ProductId" UUID NOT NULL,
  "VariationCategoryCode" varchar(50) NOT NULL,
  "DependentOnProductVariationId" UUID NULL,
  "Name" varchar(100) NOT NULL,
  "AddedPrice" decimal NULL,
  "ServingSizeGrams" decimal NULL DEFAULT 0,
  "ServingsPerPack" int NULL DEFAULT 0,
  "WeightChange" decimal NULL DEFAULT 1,
  "ImageFileName" varchar(100) NULL,
  "LabelSize" varchar(100) NOT NULL DEFAULT 'Large',
  "TitleColourHex" varchar(7) NOT NULL DEFAULT '#404040',
  "VariantColourHex" varchar(7) NOT NULL DEFAULT '#404040',
  "PackWeightText" varchar(100) NULL,
  "FrontLabelNameOverride" varchar NULL,
  "FrontLabelNameXOffset" int NULL,
  "BackLabelNameOverride" varchar NULL,
  "IngredientsFontSize" decimal NULL,
  "IngredientsYOffset" int NULL DEFAULT 0,
  "InstructionsText" varchar NULL,
  "InstructionsFontSize" decimal NULL,
  "NoteText" varchar NULL,
  "NoteFontSize" decimal NULL,
  "AllergensText" varchar(100) NULL,
  "FreeFromText" varchar(200) NULL,
  "BiodegOverrideText" varchar NULL,
  "StoreFrozenOverride" varchar NULL,
  "IconName" varchar(100) NULL,
  "IconX" int NULL DEFAULT 9,
  "IconY" int NULL DEFAULT -12,
  "IconSize" int NULL DEFAULT 32,
  "EnabledOnWebsite" boolean NOT NULL DEFAULT FALSE,
  "NutValsEnabled" boolean NOT NULL DEFAULT FALSE,
  "SortOrder" int NOT NULL DEFAULT 999,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_ProductVariation_Product" FOREIGN KEY ("ProductId") REFERENCES public."Product" ("Id"),
  CONSTRAINT "FK_ProductVariation_VariationCategory" FOREIGN KEY ("VariationCategoryCode") REFERENCES public."VariationCategory" ("Code")
);

-- Add self-reference after table is created to avoid circular dependency
ALTER TABLE public."ProductVariation" ADD CONSTRAINT "FK_ProductVariation_DependentVariation"
  FOREIGN KEY ("DependentOnProductVariationId") REFERENCES public."ProductVariation" ("Id");

COMMENT ON COLUMN public."ProductVariation"."DependentOnProductVariationId" IS 'If this has value, will only show if the dependent varation is selected in other dropdown, otherwise always show';
COMMENT ON COLUMN public."ProductVariation"."AddedPrice" IS 'Price added to product''s base price.';
COMMENT ON COLUMN public."ProductVariation"."ServingSizeGrams" IS 'To show on back label.';
COMMENT ON COLUMN public."ProductVariation"."ServingsPerPack" IS 'To show on back label.';
COMMENT ON COLUMN public."ProductVariation"."WeightChange" IS 'Adjusts nut val''s for this percentage change';

-- ProductVariationsData Table
CREATE TABLE public."ProductVariationsData" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "ProductId" UUID NOT NULL,
  "ProductVariationIds" varchar[] NOT NULL,
  "Code" varchar(100) NOT NULL,
  "BarcodeNumber" varchar(30) NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_ProductVariationsData_Product" FOREIGN KEY ("ProductId") REFERENCES public."Product" ("Id")
);

COMMENT ON COLUMN public."ProductVariationsData"."ProductVariationIds" IS 'Combination of ProductVariation''s that gives this BarcodeNumber.';
COMMENT ON COLUMN public."ProductVariationsData"."Code" IS 'Product Code used for record keeping.';

-- Ingredient Table
CREATE TABLE public."Ingredient" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "Name" varchar(100) NOT NULL,
  "Kilojoules" decimal NOT NULL,
  "Protein" decimal NOT NULL,
  "FatTotal" decimal NOT NULL,
  "FatSaturated" decimal NOT NULL,
  "Carbohydrate" decimal NOT NULL,
  "Sugars" decimal NOT NULL,
  "Fiber" decimal NOT NULL,
  "Sodium" decimal NOT NULL,
  "CostPer100g" decimal NOT NULL,
  "PercAus" decimal NOT NULL,
  "LabelDescription" varchar(200) NULL,
  "IsAllergen" boolean NULL DEFAULT FALSE,
  "IsOrganic" boolean NULL DEFAULT FALSE,
  "IsNotVegan" boolean NULL DEFAULT FALSE,
  "HideOnLabel" boolean NULL DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  "IsDemo" boolean NOT NULL DEFAULT FALSE
);

-- ProductVariationIngredient Table
CREATE TABLE public."ProductVariationIngredient" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "ProductVariationId" UUID NOT NULL,
  "IngredientId" UUID NOT NULL,
  "AmountGrams" decimal NOT NULL,
  "ShowPercent" boolean NOT NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_ProductVariationIngredient_ProductVariation" FOREIGN KEY ("ProductVariationId") REFERENCES public."ProductVariation" ("Id"),
  CONSTRAINT "FK_ProductVariationIngredient_Ingredient" FOREIGN KEY ("IngredientId") REFERENCES public."Ingredient" ("Id")
);

-- ItemSettings Table
CREATE TABLE public."ItemSettings" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "ProductId" UUID NULL,
  "ProductVariationId" UUID NULL,
  "ServingSizeGrams" decimal NULL DEFAULT 0,
  "ServingsPerPack" int NULL DEFAULT 0,
  "WeightChange" decimal NULL DEFAULT 1,
  "LabelSize" varchar(100) NOT NULL DEFAULT 'Large',
  "PackWeightText" varchar(100) NULL,
  "FrontLabelNameOverride" varchar NULL,
  "FrontLabelNameXOffset" int NULL,
  "BackLabelNameOverride" varchar NULL,
  "IngredientsFontSize" decimal NULL,
  "IngredientsYOffset" int NULL DEFAULT 0,
  "InstructionsText" varchar NULL,
  "InstructionsFontSize" decimal NULL,
  "NoteText" varchar NULL,
  "NoteFontSize" decimal NULL,
  "AllergensText" varchar(100) NULL,
  "FreeFromText" varchar(200) NULL,
  "BiodegOverrideText" varchar NULL,
  "StoreFrozenOverride" varchar NULL,
  "IconName" varchar(100) NULL,
  "IconX" int NULL DEFAULT 9,
  "IconY" int NULL DEFAULT -12,
  "IconSize" int NULL DEFAULT 32,
  "NutValsEnabled" boolean NOT NULL DEFAULT FALSE,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_ItemSettings_Product" FOREIGN KEY ("ProductId") REFERENCES public."Product" ("Id"),
  CONSTRAINT "FK_ItemSettings_ProductVariation" FOREIGN KEY ("ProductVariationId") REFERENCES public."ProductVariation" ("Id")
);

-- UserProductReview Table
CREATE TABLE public."UserProductReview" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "ProductId" UUID NOT NULL,
  "UserId" UUID NOT NULL,
  "Rating" int NOT NULL,
  "Text" varchar NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_UserProductReview_Product" FOREIGN KEY ("ProductId") REFERENCES public."Product" ("Id"),
  CONSTRAINT "FK_UserProductReview_User" FOREIGN KEY ("UserId") REFERENCES public."User" ("Id")
);

-- OrderStatus Table
CREATE TABLE public."OrderStatus" (
  "Code" varchar(50) NOT NULL PRIMARY KEY,
  "Name" varchar(100) NOT NULL,
  "Description" varchar(200) NOT NULL
);

-- Order Table
CREATE TABLE public."Order" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "UserId" UUID NULL,
  "OrderStatusCode" varchar(50) NULL,
  "SubmittedDate" timestamp NULL,
  "CompletedDate" timestamp NULL,
  "ValuesAtSubmissionJson" varchar NULL,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_Order_User" FOREIGN KEY ("UserId") REFERENCES public."User" ("Id"),
  CONSTRAINT "FK_Order_OrderStatus" FOREIGN KEY ("OrderStatusCode") REFERENCES public."OrderStatus" ("Code")
);

COMMENT ON COLUMN public."Order"."Id" IS 'Order is created when user adds first item to their bag.';
COMMENT ON COLUMN public."Order"."SubmittedDate" IS 'Date that user submitted and paid for order.';
COMMENT ON COLUMN public."Order"."CompletedDate" IS 'Date that order is ready for pick up or delivery.';
COMMENT ON COLUMN public."Order"."ValuesAtSubmissionJson" IS 'Contains details that might change in future but could be important to know the state of at time of submission (prices, name of selected variations etc.)';

-- OrderItem Table
CREATE TABLE public."OrderItem" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "OrderId" UUID NOT NULL,
  "ProductId" UUID NOT NULL,
  "ProductVariationIds" varchar[] NOT NULL,
  "Quantity" int NOT NULL DEFAULT 1,
  "CreatedAt" timestamp NOT NULL DEFAULT now(),
  "ModifiedAt" timestamp NULL,
  "Deleted" boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT "FK_OrderItem_Order" FOREIGN KEY ("OrderId") REFERENCES public."Order" ("Id"),
  CONSTRAINT "FK_OrderItem_Product" FOREIGN KEY ("ProductId") REFERENCES public."Product" ("Id")
);

-- PaymentStatus Table
CREATE TABLE public."PaymentStatus" (
  "Code" varchar(50) NOT NULL PRIMARY KEY,
  "Name" varchar(100) NOT NULL,
  "Description" varchar(200) NOT NULL
);

-- Payment Table
CREATE TABLE public."Payment" (
  "Id" UUID NOT NULL PRIMARY KEY,
  "OrderId" UUID NOT NULL,
  "PaymentStatusCode" varchar(50) NOT NULL,
  CONSTRAINT "FK_Payment_Order" FOREIGN KEY ("OrderId") REFERENCES public."Order" ("Id"),
  CONSTRAINT "FK_Payment_PaymentStatus" FOREIGN KEY ("PaymentStatusCode") REFERENCES public."PaymentStatus" ("Code")
);

COMMENT ON COLUMN public."Payment"."PaymentStatusCode" IS 'Depends on payment service used.';

-- GlobalSettings Table
DO $$
BEGIN
    RAISE NOTICE 'Creating GlobalSettings table...';
END $$;

CREATE TABLE public."GlobalSettings" (
  "Code" varchar(50) NOT NULL PRIMARY KEY,
  "Description" varchar(200) NOT NULL,
  "Value" varchar(200) NOT NULL
);

DO $$
BEGIN
    RAISE NOTICE 'GlobalSettings table created successfully';
END $$;

-- Final completion message
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    AND table_name IN (
        'User', 'Address', 'UserPersistedData', 'Stockist', 'Contact',
        'Product', 'VariationCategory', 'ProductVariation', 'ProductVariationsData',
        'Ingredient', 'ProductVariationIngredient', 'ItemSettings', 'UserProductReview',
        'DietaryAttribute', 'ProductDietaryAttribute', 'Order', 'OrderItem',
        'OrderStatus', 'Payment', 'PaymentStatus', 'GlobalSettings'
    );

    RAISE NOTICE E'\n===== DATABASE SCHEMA CREATION COMPLETED =====';
    RAISE NOTICE 'Successfully created % tables', table_count;
    RAISE NOTICE 'Schema creation completed at %', now();
    RAISE NOTICE '=============================================\n';
END $$;
