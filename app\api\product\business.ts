import { sql } from "@vercel/postgres";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationBusiness } from "../productVariation/business";
import { ItemSettingsBusiness } from "../itemSettings/business";
import { ItemIngredientModel } from "@/app/models/ItemIngredient";
import { ItemIngredientBusiness } from "../itemIngredient/business";
import { ProductVariationsDataBusiness } from "../productVariationsData/business";
import { unstable_noStore } from "next/cache";

export class ProductBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async GetList(includeAll: boolean = false) {
        try {
            unstable_noStore();
            // Get all products with their variations, dietary attributes, and variations data
            let products = (await sql<ProductModel>`
                SELECT
                    "product"."Id",
                    "product"."Name",
                    "product"."Description",
                    "product"."UnitName",
                    "product"."Price",
                    "product"."WholesalePrice",
                    "product"."VariationAddedPricesJson",
                    "product"."ImageFileName",
                    "product"."EnabledOnWebsite",
                    "product"."SortOrder",
                    "product"."Deleted",

                    (SELECT json_agg(json_build_object(
                        'Id',                         "variation"."Id",
                        'ProductId',                  "variation"."ProductId",
                        'VariationCategoryCode',      "variation"."VariationCategoryCode",
                        'DependentOnProductVariationId', "variation"."DependentOnProductVariationId",
                        'Name',                       "variation"."Name",
                        'AddedPrice',                 "variation"."AddedPrice",
                        'ImageFileName',              "variation"."ImageFileName",
                        'SortOrder',                  "variation"."SortOrder",
                        'EnabledOnWebsite',           "variation"."EnabledOnWebsite",
                        'CreatedAt',                  "variation"."CreatedAt",
                        'ModifiedAt',                 "variation"."ModifiedAt",
                        'Deleted',                    "variation"."Deleted",

                        'Ex_Settings', (
                            SELECT json_build_object(
                                'Id',                     "settings"."Id",
                                'ProductId',              "settings"."ProductId",
                                'ProductVariationId',     "settings"."ProductVariationId",
                                'ServingSizeGrams',       "settings"."ServingSizeGrams",
                                'ServingsPerPack',        "settings"."ServingsPerPack",
                                'WeightChange',           "settings"."WeightChange",
                                'LabelSize',              "settings"."LabelSize",
                                'PackWeightText',         "settings"."PackWeightText",
                                'FrontLabelNameOverride', "settings"."FrontLabelNameOverride",
                                'FrontLabelNameXOffset',  "settings"."FrontLabelNameXOffset",
                                'BackLabelNameOverride',  "settings"."BackLabelNameOverride",
                                'IngredientsFontSize',    "settings"."IngredientsFontSize",
                                'IngredientsYOffset',     "settings"."IngredientsYOffset",
                                'InstructionsText',       "settings"."InstructionsText",
                                'InstructionsFontSize',   "settings"."InstructionsFontSize",
                                'NoteText',               "settings"."NoteText",
                                'NoteFontSize',           "settings"."NoteFontSize",
                                'AllergensText',          "settings"."AllergensText",
                                'FreeFromText',           "settings"."FreeFromText",
                                'BiodegOverrideText',     "settings"."BiodegOverrideText",
                                'StoreFrozenOverride',    "settings"."StoreFrozenOverride",
                                'IconName',               "settings"."IconName",
                                'IconVisible',            "settings"."IconVisible",
                                'IconX',                  "settings"."IconX",
                                'IconY',                  "settings"."IconY",
                                'IconSize',               "settings"."IconSize",
                                'NutValsEnabled',         "settings"."NutValsEnabled"
                            )
                            FROM public."ItemSettings" "settings"
                            WHERE "settings"."ProductVariationId" = "variation"."Id"
                              AND "settings"."Deleted" = 'False'
                            LIMIT 1
                        ),

                        'Ex_Category', (
                            SELECT json_build_object(
                                'Code',                    "category"."Code",
                                'Name',                    "category"."Name",
                                'HasLabelSizeSettings',    "category"."HasLabelSizeSettings",
                                'HasIngredientsSettings',  "category"."HasIngredientsSettings",
                                'HasServingsSettings',     "category"."HasServingsSettings",
                                'HasLabelsSettings',       "category"."HasLabelsSettings",
                                'HasNameOverrideSettings', "category"."HasNameOverrideSettings",
                                'SortOrder',               "category"."SortOrder"
                            )
                            FROM public."VariationCategory" "category"
                            WHERE "category"."Code" = "variation"."VariationCategoryCode"
                              AND "category"."Deleted" = 'False'
                        ),

                        'Ex_Ingredients', (
                            SELECT json_agg(json_build_object(
                                'Id',                "varIng"."Id",
                                'ProductId',         "varIng"."ProductId",
                                'ProductVariationId', "varIng"."ProductVariationId",
                                'IngredientId',      "varIng"."IngredientId",
                                'AmountGrams',       "varIng"."AmountGrams",
                                'ShowPercent',       "varIng"."ShowPercent",
                                'Ex_ProductName',    "product"."Name",
                                'Ex_ProductVariationName', "variation"."Name",
                                'Ex_Ingredient', json_build_object(
                                    'Id',                "ingredient"."Id",
                                    'Name',              "ingredient"."Name",
                                    'Kilojoules',        "ingredient"."Kilojoules",
                                    'Protein',           "ingredient"."Protein",
                                    'FatTotal',          "ingredient"."FatTotal",
                                    'FatSaturated',      "ingredient"."FatSaturated",
                                    'Carbohydrate',      "ingredient"."Carbohydrate",
                                    'Sugars',            "ingredient"."Sugars",
                                    'Fiber',             "ingredient"."Fiber",
                                    '_NetCarbs',         "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                                    'Sodium',            "ingredient"."Sodium",
                                    'CostPer100g',       "ingredient"."CostPer100g",
                                    'PercAus',           "ingredient"."PercAus",
                                    'LabelDescription',  "ingredient"."LabelDescription",
                                    'IsAllergen',        "ingredient"."IsAllergen",
                                    'IsOrganic',         "ingredient"."IsOrganic",
                                    'IsNotVegan',        "ingredient"."IsNotVegan",
                                    'HideOnLabel',       "ingredient"."HideOnLabel",
                                    'IsDemo',            "ingredient"."IsDemo"
                                )
                            ) ORDER BY "ingredient"."Name")
                            FROM public."ItemIngredient" "varIng"
                            INNER JOIN public."Ingredient" "ingredient" ON "varIng"."IngredientId" = "ingredient"."Id"
                            WHERE "varIng"."ProductVariationId" = "variation"."Id"
                              AND "varIng"."Deleted" = 'False'
                              AND "ingredient"."Deleted" = 'False'
                        )
                    ) ORDER BY "variation"."SortOrder")
                    FROM public."ProductVariation" "variation"
                    WHERE "variation"."ProductId" = "product"."Id"
                      AND "variation"."Deleted" = 'False')
                    AS "Ex_Variations",

                    (SELECT json_agg(json_build_object(
                        'Id',                    "attribute"."Id",
                        'ProductId',             "attribute"."ProductId",
                        'DietaryAttributeCode',  "attribute"."DietaryAttributeCode"
                    ))
                    FROM public."ProductDietaryAttribute" "attribute"
                    WHERE "attribute"."ProductId" = "product"."Id")
                    AS "Ex_DietaryAttributes",

                    (SELECT json_agg(json_build_object(
                        'Id',                  "data"."Id",
                        'ProductId',           "data"."ProductId",
                        'ProductVariationIds', "data"."ProductVariationIds",
                        'Code',                "data"."Code",
                        'BarcodeNumber',       "data"."BarcodeNumber",
                        'CreatedAt',           "data"."CreatedAt",
                        'ModifiedAt',          "data"."ModifiedAt",
                        'Deleted',             "data"."Deleted"
                    ))
                    FROM public."ProductVariationsData" "data"
                    WHERE "data"."ProductId" = "product"."Id"
                      AND "data"."Deleted" = 'False')
                    AS "Ex_ProductVariationsData",

                    (SELECT json_build_object(
                        'Id',                     "settings"."Id",
                        'ProductId',              "settings"."ProductId",
                        'ProductVariationId',     "settings"."ProductVariationId",
                        'ServingSizeGrams',       "settings"."ServingSizeGrams",
                        'ServingsPerPack',        "settings"."ServingsPerPack",
                        'WeightChange',           "settings"."WeightChange",
                        'LabelSize',              "settings"."LabelSize",
                        'PackWeightText',         "settings"."PackWeightText",
                        'FrontLabelNameOverride', "settings"."FrontLabelNameOverride",
                        'FrontLabelNameXOffset',  "settings"."FrontLabelNameXOffset",
                        'BackLabelNameOverride',  "settings"."BackLabelNameOverride",
                        'IngredientsFontSize',    "settings"."IngredientsFontSize",
                        'IngredientsYOffset',     "settings"."IngredientsYOffset",
                        'InstructionsText',       "settings"."InstructionsText",
                        'InstructionsFontSize',   "settings"."InstructionsFontSize",
                        'NoteText',               "settings"."NoteText",
                        'NoteFontSize',           "settings"."NoteFontSize",
                        'AllergensText',          "settings"."AllergensText",
                        'FreeFromText',           "settings"."FreeFromText",
                        'BiodegOverrideText',     "settings"."BiodegOverrideText",
                        'StoreFrozenOverride',    "settings"."StoreFrozenOverride",
                        'IconName',               "settings"."IconName",
                        'IconVisible',            "settings"."IconVisible",
                        'IconX',                  "settings"."IconX",
                        'IconY',                  "settings"."IconY",
                        'IconSize',               "settings"."IconSize",
                        'NutValsEnabled',         "settings"."NutValsEnabled",
                        'CreatedAt',              "settings"."CreatedAt",
                        'ModifiedAt',             "settings"."ModifiedAt",
                        'Deleted',                "settings"."Deleted"
                    )
                    FROM public."ItemSettings" "settings"
                    WHERE "settings"."ProductId" = "product"."Id"
                      AND "settings"."ProductVariationId" IS NULL
                      AND "settings"."Deleted" = 'False'
                    LIMIT 1)
                    AS "Ex_Settings",

                    (SELECT json_agg(json_build_object(
                        'Id',                "itemIng"."Id",
                        'ProductId',         "itemIng"."ProductId",
                        'ProductVariationId', "itemIng"."ProductVariationId",
                        'IngredientId',      "itemIng"."IngredientId",
                        'AmountGrams',       "itemIng"."AmountGrams",
                        'ShowPercent',       "itemIng"."ShowPercent",
                        'Ex_ProductName',    "product"."Name",
                        'Ex_ProductVariationName', NULL,
                        'Ex_Ingredient', json_build_object(
                            'Id',                "ingredient"."Id",
                            'Name',              "ingredient"."Name",
                            'Kilojoules',        "ingredient"."Kilojoules",
                            'Protein',           "ingredient"."Protein",
                            'FatTotal',          "ingredient"."FatTotal",
                            'FatSaturated',      "ingredient"."FatSaturated",
                            'Carbohydrate',      "ingredient"."Carbohydrate",
                            'Sugars',            "ingredient"."Sugars",
                            'Fiber',             "ingredient"."Fiber",
                            '_NetCarbs',         "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                            'Sodium',            "ingredient"."Sodium",
                            'CostPer100g',       "ingredient"."CostPer100g",
                            'PercAus',           "ingredient"."PercAus",
                            'LabelDescription',  "ingredient"."LabelDescription",
                            'IsAllergen',        "ingredient"."IsAllergen",
                            'IsOrganic',         "ingredient"."IsOrganic",
                            'IsNotVegan',        "ingredient"."IsNotVegan",
                            'HideOnLabel',       "ingredient"."HideOnLabel",
                            'IsDemo',            "ingredient"."IsDemo"
                        )
                    ) ORDER BY "ingredient"."Name")
                    FROM public."ItemIngredient" "itemIng"
                    INNER JOIN public."Ingredient" "ingredient" ON "itemIng"."IngredientId" = "ingredient"."Id"
                    WHERE "itemIng"."ProductId" = "product"."Id"
                      AND "itemIng"."ProductVariationId" IS NULL
                      AND "itemIng"."Deleted" = 'False'
                      AND "ingredient"."Deleted" = 'False')
                    AS "Ex_Ingredients"

                FROM public."Product" "product"
                WHERE "product"."Deleted" = 'False'
                ORDER BY "product"."SortOrder"
            `).rows.map(o => new ProductModel(o));

            // Calculate nutritional values
            products.forEach((product: ProductModel) => {
                product.calcAllNutVals();
            });

            // Filter by EnabledOnWebsite if not includeAll
            if (!includeAll) {
                // Filter products by EnabledOnWebsite
                products = products.filter((product: ProductModel) => {
                    // Only keep products that are enabled on website
                    if (!product.EnabledOnWebsite) return false;

                    // Filter variations by EnabledOnWebsite
                    if (product.Ex_Variations) {
                        product.Ex_Variations = product.Ex_Variations.filter((v: ProductVariationModel) => v.EnabledOnWebsite);
                    }

                    // Keep product only if it has at least one variation
                    return product.Ex_Variations && product.Ex_Variations.length > 0;
                });
            }

            return products;
        } catch (e) {
            throw e;
        }
    }

    static async Get(id: string) {
        try {
            const products = (await sql<ProductModel>`
                SELECT
                    "product"."Id",
                    "product"."Name",
                    "product"."Description",
                    "product"."UnitName",
                    "product"."Price",
                    "product"."WholesalePrice",
                    "product"."VariationAddedPricesJson",
                    "product"."ImageFileName",
                    "product"."EnabledOnWebsite",
                    "product"."SortOrder",
                    "product"."Deleted",

                    (SELECT json_agg(json_build_object(
                        'Id',                         "variation"."Id",
                        'ProductId',                  "variation"."ProductId",
                        'VariationCategoryCode',      "variation"."VariationCategoryCode",
                        'DependentOnProductVariationId', "variation"."DependentOnProductVariationId",
                        'Name',                       "variation"."Name",
                        'AddedPrice',                 "variation"."AddedPrice",
                        'ImageFileName',              "variation"."ImageFileName",
                        'SortOrder',                  "variation"."SortOrder",
                        'EnabledOnWebsite',           "variation"."EnabledOnWebsite",
                        'CreatedAt',                  "variation"."CreatedAt",
                        'ModifiedAt',                 "variation"."ModifiedAt",
                        'Deleted',                    "variation"."Deleted",

                        'Ex_Settings', (
                            SELECT json_build_object(
                                'Id',                     "settings"."Id",
                                'ProductId',              "settings"."ProductId",
                                'ProductVariationId',     "settings"."ProductVariationId",
                                'ServingSizeGrams',       "settings"."ServingSizeGrams",
                                'ServingsPerPack',        "settings"."ServingsPerPack",
                                'WeightChange',           "settings"."WeightChange",
                                'LabelSize',              "settings"."LabelSize",
                                'PackWeightText',         "settings"."PackWeightText",
                                'FrontLabelNameOverride', "settings"."FrontLabelNameOverride",
                                'FrontLabelNameXOffset',  "settings"."FrontLabelNameXOffset",
                                'BackLabelNameOverride',  "settings"."BackLabelNameOverride",
                                'IngredientsFontSize',    "settings"."IngredientsFontSize",
                                'IngredientsYOffset',     "settings"."IngredientsYOffset",
                                'InstructionsText',       "settings"."InstructionsText",
                                'InstructionsFontSize',   "settings"."InstructionsFontSize",
                                'NoteText',               "settings"."NoteText",
                                'NoteFontSize',           "settings"."NoteFontSize",
                                'AllergensText',          "settings"."AllergensText",
                                'FreeFromText',           "settings"."FreeFromText",
                                'BiodegOverrideText',     "settings"."BiodegOverrideText",
                                'StoreFrozenOverride',    "settings"."StoreFrozenOverride",
                                'IconName',               "settings"."IconName",
                                'IconVisible',            "settings"."IconVisible",
                                'IconX',                  "settings"."IconX",
                                'IconY',                  "settings"."IconY",
                                'IconSize',               "settings"."IconSize",
                                'NutValsEnabled',         "settings"."NutValsEnabled"
                            )
                            FROM public."ItemSettings" "settings"
                            WHERE "settings"."ProductVariationId" = "variation"."Id"
                              AND "settings"."Deleted" = 'False'
                            LIMIT 1
                        ),

                        'Ex_Category', (
                            SELECT json_build_object(
                                'Code',                    "category"."Code",
                                'Name',                    "category"."Name",
                                'HasLabelSizeSettings',    "category"."HasLabelSizeSettings",
                                'HasIngredientsSettings',  "category"."HasIngredientsSettings",
                                'HasServingsSettings',     "category"."HasServingsSettings",
                                'HasLabelsSettings',       "category"."HasLabelsSettings",
                                'HasNameOverrideSettings', "category"."HasNameOverrideSettings",
                                'SortOrder',               "category"."SortOrder"
                            )
                            FROM public."VariationCategory" "category"
                            WHERE "category"."Code" = "variation"."VariationCategoryCode"
                              AND "category"."Deleted" = 'False'
                        ),

                        'Ex_Ingredients', (
                            SELECT json_agg(json_build_object(
                                'Id',                "varIng"."Id",
                                'ProductId',         "varIng"."ProductId",
                                'ProductVariationId', "varIng"."ProductVariationId",
                                'IngredientId',      "varIng"."IngredientId",
                                'AmountGrams',       "varIng"."AmountGrams",
                                'ShowPercent',       "varIng"."ShowPercent",
                                'Ex_ProductName',    "product"."Name",
                                'Ex_ProductVariationName', "variation"."Name",
                                'Ex_Ingredient', json_build_object(
                                    'Id',                "ingredient"."Id",
                                    'Name',              "ingredient"."Name",
                                    'Kilojoules',        "ingredient"."Kilojoules",
                                    'Protein',           "ingredient"."Protein",
                                    'FatTotal',          "ingredient"."FatTotal",
                                    'FatSaturated',      "ingredient"."FatSaturated",
                                    'Carbohydrate',      "ingredient"."Carbohydrate",
                                    'Sugars',            "ingredient"."Sugars",
                                    'Fiber',             "ingredient"."Fiber",
                                    '_NetCarbs',         "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                                    'Sodium',            "ingredient"."Sodium",
                                    'CostPer100g',       "ingredient"."CostPer100g",
                                    'PercAus',           "ingredient"."PercAus",
                                    'LabelDescription',  "ingredient"."LabelDescription",
                                    'IsAllergen',        "ingredient"."IsAllergen",
                                    'IsOrganic',         "ingredient"."IsOrganic",
                                    'IsNotVegan',        "ingredient"."IsNotVegan",
                                    'HideOnLabel',       "ingredient"."HideOnLabel",
                                    'IsDemo',            "ingredient"."IsDemo"
                                )
                            ) ORDER BY "ingredient"."Name")
                            FROM public."ItemIngredient" "varIng"
                            INNER JOIN public."Ingredient" "ingredient" ON "varIng"."IngredientId" = "ingredient"."Id"
                            WHERE "varIng"."ProductVariationId" = "variation"."Id"
                              AND "varIng"."Deleted" = 'False'
                              AND "ingredient"."Deleted" = 'False'
                        )
                    ) ORDER BY "variation"."SortOrder")
                    FROM public."ProductVariation" "variation"
                    WHERE "variation"."ProductId" = "product"."Id"
                      AND "variation"."Deleted" = 'False')
                    AS "Ex_Variations",

                    (SELECT json_agg(json_build_object(
                        'Id',                    "attribute"."Id",
                        'ProductId',             "attribute"."ProductId",
                        'DietaryAttributeCode',  "attribute"."DietaryAttributeCode"
                    ))
                    FROM public."ProductDietaryAttribute" "attribute"
                    WHERE "attribute"."ProductId" = "product"."Id"
                      AND "attribute"."Deleted" = 'False')
                    AS "Ex_DietaryAttributes",

                    (SELECT json_agg(json_build_object(
                        'Id',                  "data"."Id",
                        'ProductId',           "data"."ProductId",
                        'ProductVariationIds', "data"."ProductVariationIds",
                        'Code',                "data"."Code",
                        'BarcodeNumber',       "data"."BarcodeNumber",
                        'CreatedAt',           "data"."CreatedAt",
                        'ModifiedAt',          "data"."ModifiedAt",
                        'Deleted',             "data"."Deleted"
                    ))
                    FROM public."ProductVariationsData" "data"
                    WHERE "data"."ProductId" = "product"."Id"
                      AND "data"."Deleted" = 'False')
                    AS "Ex_ProductVariationsData",

                    (SELECT json_build_object(
                        'Id',                     "settings"."Id",
                        'ProductId',              "settings"."ProductId",
                        'ProductVariationId',     "settings"."ProductVariationId",
                        'ServingSizeGrams',       "settings"."ServingSizeGrams",
                        'ServingsPerPack',        "settings"."ServingsPerPack",
                        'WeightChange',           "settings"."WeightChange",
                        'LabelSize',              "settings"."LabelSize",
                        'PackWeightText',         "settings"."PackWeightText",
                        'FrontLabelNameOverride', "settings"."FrontLabelNameOverride",
                        'FrontLabelNameXOffset',  "settings"."FrontLabelNameXOffset",
                        'BackLabelNameOverride',  "settings"."BackLabelNameOverride",
                        'IngredientsFontSize',    "settings"."IngredientsFontSize",
                        'IngredientsYOffset',     "settings"."IngredientsYOffset",
                        'InstructionsText',       "settings"."InstructionsText",
                        'InstructionsFontSize',   "settings"."InstructionsFontSize",
                        'NoteText',               "settings"."NoteText",
                        'NoteFontSize',           "settings"."NoteFontSize",
                        'AllergensText',          "settings"."AllergensText",
                        'FreeFromText',           "settings"."FreeFromText",
                        'BiodegOverrideText',     "settings"."BiodegOverrideText",
                        'StoreFrozenOverride',    "settings"."StoreFrozenOverride",
                        'IconName',               "settings"."IconName",
                        'IconVisible',            "settings"."IconVisible",
                        'IconX',                  "settings"."IconX",
                        'IconY',                  "settings"."IconY",
                        'IconSize',               "settings"."IconSize",
                        'NutValsEnabled',         "settings"."NutValsEnabled",
                        'CreatedAt',              "settings"."CreatedAt",
                        'ModifiedAt',             "settings"."ModifiedAt",
                        'Deleted',                "settings"."Deleted"
                    )
                    FROM public."ItemSettings" "settings"
                    WHERE "settings"."ProductId" = "product"."Id"
                      AND "settings"."ProductVariationId" IS NULL
                      AND "settings"."Deleted" = 'False'
                    LIMIT 1)
                    AS "Ex_Settings",

                    (SELECT json_agg(json_build_object(
                        'Id',                "itemIng"."Id",
                        'ProductId',         "itemIng"."ProductId",
                        'ProductVariationId', "itemIng"."ProductVariationId",
                        'IngredientId',      "itemIng"."IngredientId",
                        'AmountGrams',       "itemIng"."AmountGrams",
                        'ShowPercent',       "itemIng"."ShowPercent",
                        'Ex_ProductName',    "product"."Name",
                        'Ex_ProductVariationName', NULL,
                        'Ex_Ingredient', json_build_object(
                            'Id',                "ingredient"."Id",
                            'Name',              "ingredient"."Name",
                            'Kilojoules',        "ingredient"."Kilojoules",
                            'Protein',           "ingredient"."Protein",
                            'FatTotal',          "ingredient"."FatTotal",
                            'FatSaturated',      "ingredient"."FatSaturated",
                            'Carbohydrate',      "ingredient"."Carbohydrate",
                            'Sugars',            "ingredient"."Sugars",
                            'Fiber',             "ingredient"."Fiber",
                            '_NetCarbs',         "ingredient"."Carbohydrate" - "ingredient"."Fiber",
                            'Sodium',            "ingredient"."Sodium",
                            'CostPer100g',       "ingredient"."CostPer100g",
                            'PercAus',           "ingredient"."PercAus",
                            'LabelDescription',  "ingredient"."LabelDescription",
                            'IsAllergen',        "ingredient"."IsAllergen",
                            'IsOrganic',         "ingredient"."IsOrganic",
                            'IsNotVegan',        "ingredient"."IsNotVegan",
                            'HideOnLabel',       "ingredient"."HideOnLabel",
                            'IsDemo',            "ingredient"."IsDemo"
                        )
                    ) ORDER BY "ingredient"."Name")
                    FROM public."ItemIngredient" "itemIng"
                    INNER JOIN public."Ingredient" "ingredient" ON "itemIng"."IngredientId" = "ingredient"."Id"
                    WHERE "itemIng"."ProductId" = "product"."Id"
                      AND "itemIng"."ProductVariationId" IS NULL
                      AND "itemIng"."Deleted" = 'False'
                      AND "ingredient"."Deleted" = 'False')
                    AS "Ex_Ingredients"

                FROM public."Product" "product"
                WHERE "product"."Id" = ${id}
                  AND "product"."Deleted" = 'False'
            `).rows.map(o => new ProductModel(o));

            if (products.length === 0) {
                return null;
            }

            const product = products[0];

            // Calculate nutritional values
            product.calcAllNutVals();

            return product;
        } catch (e) {
            throw e;
        }
    }

    static async GetNextSortOrder() {
        let latestSortOrder = (await sql`
            SELECT MAX("SortOrder") "LatestSortOrder"
            FROM public."Product"
            WHERE "Deleted" = 'False'
        `).rows[0]["LatestSortOrder"];
        return latestSortOrder + 1;
    }

    static async RecordExists(id: string) {
        const result = await sql`
            SELECT COUNT(*) as "Count"
            FROM public."Product"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `;
        return result.rows[0]["Count"] > 0;
    }


    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(newProduct:ProductModel) {
        await sql`
            INSERT INTO public."Product"
            (
                "Id",
                "Name",
                "Description",
                "UnitName",
                "Price",
                "WholesalePrice",
                "VariationAddedPricesJson",
                "ImageFileName",
                "EnabledOnWebsite",
                "SortOrder",
                "CreatedAt"
            )
            VALUES
            (
                ${newProduct.Id},
                ${newProduct.Name},
                ${newProduct.Description},
                ${newProduct.UnitName},
                ${newProduct.Price},
                ${newProduct.WholesalePrice},
                ${newProduct.VariationAddedPricesJson},
                ${newProduct.ImageFileName},
                ${newProduct.EnabledOnWebsite},
                ${await this.GetNextSortOrder()},
                ${new Date().toUTCString()}
            )
        `;

        // Update all variations on the product
        if (newProduct.Ex_Variations && newProduct.Ex_Variations.length > 0) {
            await ProductVariationBusiness.UpdateList(newProduct.Ex_Variations);
        }

        // Update ProductVariationsData records
        if (newProduct.Ex_ProductVariationsData && newProduct.Ex_ProductVariationsData.length > 0) {
            await ProductVariationsDataBusiness.UpdateList(newProduct.Ex_ProductVariationsData);
        }

        // Create ingredients for the product if they exist
        if (newProduct.Ex_Ingredients && newProduct.Ex_Ingredients.length > 0) {
            const ingredientItems = newProduct.Ex_Ingredients.map(ing => new ItemIngredientModel({
                // Preserve the original ItemIngredient's Id
                Id: ing.Id,
                // Set only ProductId, not ProductVariationId to comply with constraint
                ProductId: newProduct.Id,
                ProductVariationId: null,
                IngredientId: ing.IngredientId, // Use IngredientId, not Id
                AmountGrams: ing.AmountGrams,
                ShowPercent: ing.ShowPercent,
                Ex_ProductName: newProduct.Name,
                Ex_ProductVariationName: ""
            }));

            if (ingredientItems.length > 0) {
                await ItemIngredientBusiness.CreateList(ingredientItems);
            }
        }

        // Update ItemSettings if it has changes
        if (newProduct.Ex_Settings && newProduct.Ex_Settings.UI_HasChanges) {
            await ItemSettingsBusiness.Update(newProduct.Ex_Settings);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(product: ProductModel) {
        // Check if the product exists
        const exists = await this.RecordExists(product.Id);

        // If the product doesn't exist, create it instead
        if (!exists) {
            await this.Create(product);
            return;
        }

        // Save main product data
        await sql`
            UPDATE public."Product"
            SET "Name"                     = ${product.Name},
                "Description"              = ${product.Description},
                "UnitName"                 = ${product.UnitName},
                "Price"                    = ${product.Price},
                "WholesalePrice"           = ${product.WholesalePrice},
                "VariationAddedPricesJson" = ${product.VariationAddedPricesJson},
                "ImageFileName"            = ${product.ImageFileName},
                "EnabledOnWebsite"         = ${product.EnabledOnWebsite},
                "SortOrder"                = ${product.SortOrder},
                "ModifiedAt"               = ${new Date().toUTCString()},
                "Deleted"                  = ${product.Deleted ?? false}
            WHERE "Id" = ${product.Id}
        `;

        // Update all variations on the product
        if (product.Ex_Variations && product.Ex_Variations.length > 0) {
            await ProductVariationBusiness.UpdateList(product.Ex_Variations);
        }

        // Update ProductVariationsData records
        if (product.Ex_ProductVariationsData && product.Ex_ProductVariationsData.length > 0) {
            await ProductVariationsDataBusiness.UpdateList(product.Ex_ProductVariationsData);
        }

        // Update ingredients if this product has ingredients
        if (product.Ex_Ingredients && product.Ex_Ingredients.length > 0) {
            // Get ingredients that have changes
            const ingredientsToUpdate = product.Ex_Ingredients.filter(i => i.UI_HasChanges || i.Deleted);

            // Map ingredients to ItemIngredientModel
            const ingredientItems = ingredientsToUpdate.map(ing => new ItemIngredientModel({
                // Preserve the original ItemIngredient's Id
                Id: ing.Id,
                // Set only ProductId, not ProductVariationId to comply with constraint
                ProductId: product.Id,
                ProductVariationId: null,
                IngredientId: ing.IngredientId, // Use IngredientId, not Id
                AmountGrams: ing.AmountGrams,
                ShowPercent: ing.ShowPercent,
                Deleted: ing.Deleted,
                Ex_ProductName: product.Name,
                Ex_ProductVariationName: ""
            }));

            // Update ingredients
            if (ingredientItems.length > 0) {
                await ItemIngredientBusiness.UpdateList(ingredientItems);
            }
        }

        // Update ItemSettings if it has changes
        if (product.Ex_Settings && product.Ex_Settings.UI_HasChanges) {
            await ItemSettingsBusiness.Update(product.Ex_Settings);
        }
    }

    static async UpdateList(products: ProductModel[]) {
        for (const product of products) {
            // Use the Update method to handle both product and variations
            await this.Update(product);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id:string) {

        // - ProductVariationsData - //

        // IF only have 1 category, just delete that ProductVariationsData record
        await sql`
            UPDATE public."ProductVariationsData"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductId" = ${id}
        `;

        // - ProductVariation - //

        await sql`
            UPDATE public."ProductVariation"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductId" = ${id}
        `;

        // - ItemIngredient - //

        await sql`
            UPDATE public."ItemIngredient"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductId" = ${id}
        `;

        // - ItemSettings - //

        await sql`
            UPDATE public."ItemSettings"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "ProductId" = ${id}
        `;

        // - Product - //

        await sql`
            UPDATE public."Product"
            SET "Deleted"    = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;

    }

}