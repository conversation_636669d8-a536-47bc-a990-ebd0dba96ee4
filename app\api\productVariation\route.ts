import { NextRequest, NextResponse } from "next/server";
import { ProductModel } from "@/app/models/Product";
import { ProductVariationModel } from "@/app/models/ProductVariation";
import { ProductVariationBusiness } from "./business";
import { ProductBusiness } from "../product/business";

export const dynamic = 'force-dynamic';


// - ------ - //
// - CREATE - //
// - ------ - //

export async function PUT(request: NextRequest) {
    try {
        const newItem:ProductVariationModel = new ProductVariationModel(await request.json());
        await ProductVariationBusiness.Create(newItem);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const item:ProductVariationModel = new ProductVariationModel(await request.json());
        await ProductVariationBusiness.Update(item);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}


// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const id:string = new URL(request.url).searchParams.get("id")!;
        await ProductVariationBusiness.Delete(id);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}